# AndroidWeb Enterprise Platform Environment Variables

# Admin Credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=AndroidWeb2024!
ADMIN_EMAIL=<EMAIL>

# Database Configuration
DATABASE_URL=./data/database.json
STORAGE_PATH=./data

# AI Configuration
OLLAMA_HOST=http://localhost:11434
OLLAMA_ENABLED=true
DEFAULT_MODEL=llama3.2:1b

# Application Configuration
NEXT_PUBLIC_APP_NAME=AndroidWeb Enterprise
NEXT_PUBLIC_APP_VERSION=1.0.0
NODE_ENV=development

# Security
JWT_SECRET=androidweb_jwt_secret_key_2024
SESSION_SECRET=androidweb_session_secret_2024

# API Configuration
API_RATE_LIMIT=100
API_TIMEOUT=30000

# Features
ENABLE_AI_LEARNING=true
ENABLE_REAL_TIME_STATS=true
ENABLE_CODE_GENERATION=true
