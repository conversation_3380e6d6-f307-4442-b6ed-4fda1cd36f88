"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/chat/route";
exports.ids = ["app/api/ai/chat/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai%2Fchat%2Froute&page=%2Fapi%2Fai%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fchat%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai%2Fchat%2Froute&page=%2Fapi%2Fai%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fchat%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_claudiu_Desktop_proiecte_androidweb_src_app_api_ai_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/chat/route.ts */ \"(rsc)/./src/app/api/ai/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/chat/route\",\n        pathname: \"/api/ai/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/chat/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/api/ai/chat/route.ts\",\n    nextConfigOutput,\n    userland: _home_claudiu_Desktop_proiecte_androidweb_src_app_api_ai_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/ai/chat/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai%2Fchat%2Froute&page=%2Fapi%2Fai%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fchat%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/ai/chat/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/ai/chat/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\n// Apel REAL la Ollama AI local\nasync function callOllamaAI(message, context, generateCode) {\n    try {\n        // Prompt pentru generarea de aplicații mobile\n        const systemPrompt = `Ești un AI expert în dezvoltarea aplicațiilor mobile Android și iOS.\n    Răspunde în română și generează cod funcțional pentru cerințele utilizatorului.\n\n    Context: ${context}\n    Generează cod: ${generateCode}\n\n    Dacă utilizatorul cere o aplicație sau funcționalitate, generează:\n    1. Răspuns explicativ în română\n    2. Cod funcțional (React Native, Flutter, sau nativ)\n    3. Preview description pentru UI\n\n    Mesajul utilizatorului: ${message}`;\n        // Apel la Ollama local\n        const response = await fetch(\"http://localhost:11434/api/generate\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                model: \"qwen2.5:latest\",\n                prompt: systemPrompt,\n                stream: false,\n                options: {\n                    temperature: 0.7,\n                    top_p: 0.9,\n                    max_tokens: 2000\n                }\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`Ollama API error: ${response.status}`);\n        }\n        const aiData = await response.json();\n        const aiResponse = aiData.response || \"\";\n        // Procesează răspunsul AI pentru a extrage codul și preview-ul\n        const result = parseAIResponse(aiResponse, message);\n        return {\n            response: result.response,\n            generatedCode: result.code,\n            preview: result.preview,\n            platform: result.platform\n        };\n    } catch (error) {\n        console.error(\"Ollama AI call failed:\", error);\n        // Fallback la răspuns local inteligent\n        return generateIntelligentFallback(message, generateCode);\n    }\n}\nfunction parseAIResponse(aiResponse, userMessage) {\n    // Extrage codul din răspunsul AI (între ```code și ```)\n    const codeMatch = aiResponse.match(/```(?:javascript|jsx|typescript|tsx|dart|kotlin|swift)?\\n?([\\s\\S]*?)```/);\n    const code = codeMatch ? codeMatch[1].trim() : null;\n    // Determină platforma bazată pe cerința utilizatorului\n    let platform = \"Android\";\n    if (userMessage.toLowerCase().includes(\"ios\") || userMessage.toLowerCase().includes(\"iphone\")) {\n        platform = \"iOS\";\n    } else if (userMessage.toLowerCase().includes(\"flutter\")) {\n        platform = \"Flutter\";\n    } else if (userMessage.toLowerCase().includes(\"react native\")) {\n        platform = \"React Native\";\n    }\n    // Generează preview bazat pe conținut\n    let preview = \"Aplicație personalizată\";\n    if (userMessage.toLowerCase().includes(\"login\") || userMessage.toLowerCase().includes(\"autentificare\")) {\n        preview = \"Login Screen with authentication\";\n    } else if (userMessage.toLowerCase().includes(\"dashboard\") || userMessage.toLowerCase().includes(\"tablou\")) {\n        preview = \"Dashboard with metrics and data\";\n    } else if (userMessage.toLowerCase().includes(\"chat\") || userMessage.toLowerCase().includes(\"mesaje\")) {\n        preview = \"Chat interface with messaging\";\n    } else if (userMessage.toLowerCase().includes(\"lista\") || userMessage.toLowerCase().includes(\"list\")) {\n        preview = \"List view with items\";\n    }\n    // Curăță răspunsul de cod pentru a avea doar explicația\n    const cleanResponse = aiResponse.replace(/```[\\s\\S]*?```/g, \"\").trim();\n    return {\n        response: cleanResponse || \"Am generat codul pentru cererea ta.\",\n        code: code,\n        preview: preview,\n        platform: platform\n    };\n}\nfunction generateIntelligentFallback(message, generateCode) {\n    const lowerMessage = message.toLowerCase();\n    let response = \"Am \\xeențeles cererea ta și voi genera codul necesar.\";\n    let code = null;\n    let preview = \"Aplicație personalizată\";\n    let platform = \"Android\";\n    if (lowerMessage.includes(\"login\") || lowerMessage.includes(\"autentificare\")) {\n        response = \"Creez o interfață de login cu autentificare securizată. Voi include c\\xe2mpuri pentru email/username și parolă, plus validare.\";\n        preview = \"Login Screen with authentication\";\n        if (generateCode) {\n            code = `import React, { useState } from 'react';\nimport { View, Text, TextInput, TouchableOpacity, StyleSheet } from 'react-native';\n\nexport default function LoginScreen() {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n\n  const handleLogin = () => {\n    // Logica de autentificare\n    console.log('Login attempt:', { email, password });\n  };\n\n  return (\n    <View style={styles.container}>\n      <Text style={styles.title}>Welcome Back</Text>\n\n      <TextInput\n        style={styles.input}\n        placeholder=\"Email\"\n        value={email}\n        onChangeText={setEmail}\n        keyboardType=\"email-address\"\n      />\n\n      <TextInput\n        style={styles.input}\n        placeholder=\"Password\"\n        value={password}\n        onChangeText={setPassword}\n        secureTextEntry\n      />\n\n      <TouchableOpacity style={styles.button} onPress={handleLogin}>\n        <Text style={styles.buttonText}>Sign In</Text>\n      </TouchableOpacity>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    padding: 20,\n    backgroundColor: '#f5f5f5',\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    textAlign: 'center',\n    marginBottom: 30,\n  },\n  input: {\n    borderWidth: 1,\n    borderColor: '#ddd',\n    padding: 15,\n    marginBottom: 15,\n    borderRadius: 8,\n    backgroundColor: 'white',\n  },\n  button: {\n    backgroundColor: '#007AFF',\n    padding: 15,\n    borderRadius: 8,\n    alignItems: 'center',\n  },\n  buttonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: 'bold',\n  },\n});`;\n        }\n    } else if (lowerMessage.includes(\"dashboard\") || lowerMessage.includes(\"tablou\")) {\n        response = \"Creez un dashboard cu metrici și date importante. Voi include carduri pentru statistici și grafice.\";\n        preview = \"Dashboard with metrics and data\";\n        if (generateCode) {\n            code = `import React from 'react';\nimport { View, Text, ScrollView, StyleSheet } from 'react-native';\n\nexport default function Dashboard() {\n  const stats = [\n    { title: 'Total Users', value: '1,247', color: '#4CAF50' },\n    { title: 'Revenue', value: '$12,890', color: '#2196F3' },\n    { title: 'Orders', value: '3,456', color: '#FF9800' },\n    { title: 'Growth', value: '+23%', color: '#9C27B0' },\n  ];\n\n  return (\n    <ScrollView style={styles.container}>\n      <Text style={styles.title}>Dashboard</Text>\n\n      <View style={styles.statsGrid}>\n        {stats.map((stat, index) => (\n          <View key={index} style={[styles.statCard, { borderLeftColor: stat.color }]}>\n            <Text style={styles.statValue}>{stat.value}</Text>\n            <Text style={styles.statTitle}>{stat.title}</Text>\n          </View>\n        ))}\n      </View>\n\n      <View style={styles.activityCard}>\n        <Text style={styles.cardTitle}>Recent Activity</Text>\n        <Text style={styles.activityItem}>• New user registered</Text>\n        <Text style={styles.activityItem}>• Payment received</Text>\n        <Text style={styles.activityItem}>• Order completed</Text>\n      </View>\n    </ScrollView>\n  );\n}`;\n        }\n    } else {\n        response = `Am înțeles cererea ta: \"${message}\". Lucrez la implementarea funcționalității cerute și voi genera codul corespunzător.`;\n        if (generateCode) {\n            code = `// Cod generat pentru: ${message}\nimport React from 'react';\nimport { View, Text, StyleSheet } from 'react-native';\n\nexport default function CustomApp() {\n  return (\n    <View style={styles.container}>\n      <Text style={styles.title}>Custom App</Text>\n      <Text style={styles.description}>\n        Aplicația ta personalizată pentru: ${message}\n      </Text>\n    </View>\n  );\n}`;\n        }\n    }\n    return {\n        response,\n        generatedCode: code,\n        preview,\n        platform\n    };\n}\nasync function POST(request) {\n    try {\n        // Verifică autentificarea\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader?.startsWith(\"Bearer \")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        const user = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyToken)(token);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        const { message, context, generateCode } = await request.json();\n        if (!message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Message is required\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(\"AI Chat request:\", {\n            message,\n            context,\n            generateCode\n        });\n        // Apel la Ollama AI local\n        const aiResponse = await callOllamaAI(message, context || \"mobile_app_development\", generateCode || true);\n        console.log(\"AI Chat response generated\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response: aiResponse.response,\n            generatedCode: aiResponse.generatedCode,\n            preview: aiResponse.preview,\n            platform: aiResponse.platform,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"AI Chat API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    try {\n        // Verifică dacă Ollama este disponibil\n        const ollamaResponse = await fetch(\"http://localhost:11434/api/tags\", {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        const isOllamaAvailable = ollamaResponse.ok;\n        const models = isOllamaAvailable ? (await ollamaResponse.json()).models : [];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            aiServiceStatus: isOllamaAvailable ? \"available\" : \"offline\",\n            ollamaConnected: isOllamaAvailable,\n            models: models.map((m)=>m.name) || [\n                \"qwen2.5:latest\"\n            ],\n            capabilities: [\n                \"Real-time mobile app code generation\",\n                \"Interactive conversation with AI\",\n                \"Android/iOS development guidance\",\n                \"Live code preview and testing\",\n                \"Intelligent fallback responses\"\n            ]\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            aiServiceStatus: \"offline\",\n            ollamaConnected: false,\n            models: [\n                \"fallback\"\n            ],\n            capabilities: [\n                \"Local fallback responses\"\n            ]\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9haS9jaGF0L3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBdUQ7QUFDZjtBQUV4QywrQkFBK0I7QUFDL0IsZUFBZUUsYUFBYUMsT0FBZSxFQUFFQyxPQUFlLEVBQUVDLFlBQXFCO0lBQ2pGLElBQUk7UUFDRiw4Q0FBOEM7UUFDOUMsTUFBTUMsZUFBZSxDQUFDOzs7YUFHYixFQUFFRixRQUFRO21CQUNKLEVBQUVDLGFBQWE7Ozs7Ozs7NEJBT04sRUFBRUYsUUFBUSxDQUFDO1FBRW5DLHVCQUF1QjtRQUN2QixNQUFNSSxXQUFXLE1BQU1DLE1BQU0sdUNBQXVDO1lBQ2xFQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1lBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnQkFDbkJDLE9BQU87Z0JBQ1BDLFFBQVFUO2dCQUNSVSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQQyxhQUFhO29CQUNiQyxPQUFPO29CQUNQQyxZQUFZO2dCQUNkO1lBQ0Y7UUFDRjtRQUVBLElBQUksQ0FBQ2IsU0FBU2MsRUFBRSxFQUFFO1lBQ2hCLE1BQU0sSUFBSUMsTUFBTSxDQUFDLGtCQUFrQixFQUFFZixTQUFTZ0IsTUFBTSxDQUFDLENBQUM7UUFDeEQ7UUFFQSxNQUFNQyxTQUFTLE1BQU1qQixTQUFTa0IsSUFBSTtRQUNsQyxNQUFNQyxhQUFhRixPQUFPakIsUUFBUSxJQUFJO1FBRXRDLCtEQUErRDtRQUMvRCxNQUFNb0IsU0FBU0MsZ0JBQWdCRixZQUFZdkI7UUFFM0MsT0FBTztZQUNMSSxVQUFVb0IsT0FBT3BCLFFBQVE7WUFDekJzQixlQUFlRixPQUFPRyxJQUFJO1lBQzFCQyxTQUFTSixPQUFPSSxPQUFPO1lBQ3ZCQyxVQUFVTCxPQUFPSyxRQUFRO1FBQzNCO0lBRUYsRUFBRSxPQUFPQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1FBRXhDLHVDQUF1QztRQUN2QyxPQUFPRSw0QkFBNEJoQyxTQUFTRTtJQUM5QztBQUNGO0FBRUEsU0FBU3VCLGdCQUFnQkYsVUFBa0IsRUFBRVUsV0FBbUI7SUFDOUQsd0RBQXdEO0lBQ3hELE1BQU1DLFlBQVlYLFdBQVdZLEtBQUssQ0FBQztJQUNuQyxNQUFNUixPQUFPTyxZQUFZQSxTQUFTLENBQUMsRUFBRSxDQUFDRSxJQUFJLEtBQUs7SUFFL0MsdURBQXVEO0lBQ3ZELElBQUlQLFdBQVc7SUFDZixJQUFJSSxZQUFZSSxXQUFXLEdBQUdDLFFBQVEsQ0FBQyxVQUFVTCxZQUFZSSxXQUFXLEdBQUdDLFFBQVEsQ0FBQyxXQUFXO1FBQzdGVCxXQUFXO0lBQ2IsT0FBTyxJQUFJSSxZQUFZSSxXQUFXLEdBQUdDLFFBQVEsQ0FBQyxZQUFZO1FBQ3hEVCxXQUFXO0lBQ2IsT0FBTyxJQUFJSSxZQUFZSSxXQUFXLEdBQUdDLFFBQVEsQ0FBQyxpQkFBaUI7UUFDN0RULFdBQVc7SUFDYjtJQUVBLHNDQUFzQztJQUN0QyxJQUFJRCxVQUFVO0lBQ2QsSUFBSUssWUFBWUksV0FBVyxHQUFHQyxRQUFRLENBQUMsWUFBWUwsWUFBWUksV0FBVyxHQUFHQyxRQUFRLENBQUMsa0JBQWtCO1FBQ3RHVixVQUFVO0lBQ1osT0FBTyxJQUFJSyxZQUFZSSxXQUFXLEdBQUdDLFFBQVEsQ0FBQyxnQkFBZ0JMLFlBQVlJLFdBQVcsR0FBR0MsUUFBUSxDQUFDLFdBQVc7UUFDMUdWLFVBQVU7SUFDWixPQUFPLElBQUlLLFlBQVlJLFdBQVcsR0FBR0MsUUFBUSxDQUFDLFdBQVdMLFlBQVlJLFdBQVcsR0FBR0MsUUFBUSxDQUFDLFdBQVc7UUFDckdWLFVBQVU7SUFDWixPQUFPLElBQUlLLFlBQVlJLFdBQVcsR0FBR0MsUUFBUSxDQUFDLFlBQVlMLFlBQVlJLFdBQVcsR0FBR0MsUUFBUSxDQUFDLFNBQVM7UUFDcEdWLFVBQVU7SUFDWjtJQUVBLHdEQUF3RDtJQUN4RCxNQUFNVyxnQkFBZ0JoQixXQUFXaUIsT0FBTyxDQUFDLG1CQUFtQixJQUFJSixJQUFJO0lBRXBFLE9BQU87UUFDTGhDLFVBQVVtQyxpQkFBaUI7UUFDM0JaLE1BQU1BO1FBQ05DLFNBQVNBO1FBQ1RDLFVBQVVBO0lBQ1o7QUFDRjtBQUVBLFNBQVNHLDRCQUE0QmhDLE9BQWUsRUFBRUUsWUFBcUI7SUFDekUsTUFBTXVDLGVBQWV6QyxRQUFRcUMsV0FBVztJQUV4QyxJQUFJakMsV0FBVztJQUNmLElBQUl1QixPQUFPO0lBQ1gsSUFBSUMsVUFBVTtJQUNkLElBQUlDLFdBQVc7SUFFZixJQUFJWSxhQUFhSCxRQUFRLENBQUMsWUFBWUcsYUFBYUgsUUFBUSxDQUFDLGtCQUFrQjtRQUM1RWxDLFdBQVc7UUFDWHdCLFVBQVU7UUFDVixJQUFJMUIsY0FBYztZQUNoQnlCLE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7R0F1RVgsQ0FBQztRQUNBO0lBQ0YsT0FBTyxJQUFJYyxhQUFhSCxRQUFRLENBQUMsZ0JBQWdCRyxhQUFhSCxRQUFRLENBQUMsV0FBVztRQUVoRmxDLFdBQVc7UUFDWHdCLFVBQVU7UUFDVixJQUFJMUIsY0FBYztZQUNoQnlCLE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0FnQ2IsQ0FBQztRQUNFO0lBQ0YsT0FBTztRQUNMdkIsV0FBVyxDQUFDLHdCQUF3QixFQUFFSixRQUFRLHFGQUFxRixDQUFDO1FBQ3BJLElBQUlFLGNBQWM7WUFDaEJ5QixPQUFPLENBQUMsdUJBQXVCLEVBQUUzQixRQUFROzs7Ozs7Ozs7MkNBU0osRUFBRUEsUUFBUTs7OztDQUlwRCxDQUFDO1FBQ0U7SUFDRjtJQUVBLE9BQU87UUFDTEk7UUFDQXNCLGVBQWVDO1FBQ2ZDO1FBQ0FDO0lBQ0Y7QUFDRjtBQUVPLGVBQWVhLEtBQUtDLE9BQW9CO0lBQzdDLElBQUk7UUFDRiwwQkFBMEI7UUFDMUIsTUFBTUMsYUFBYUQsUUFBUXBDLE9BQU8sQ0FBQ3NDLEdBQUcsQ0FBQztRQUN2QyxJQUFJLENBQUNELFlBQVlFLFdBQVcsWUFBWTtZQUN0QyxPQUFPakQscURBQVlBLENBQUN5QixJQUFJLENBQUM7Z0JBQUVRLE9BQU87WUFBZSxHQUFHO2dCQUFFVixRQUFRO1lBQUk7UUFDcEU7UUFFQSxNQUFNMkIsUUFBUUgsV0FBV0ksU0FBUyxDQUFDO1FBQ25DLE1BQU1DLE9BQU8sTUFBTW5ELHNEQUFXQSxDQUFDaUQ7UUFDL0IsSUFBSSxDQUFDRSxNQUFNO1lBQ1QsT0FBT3BELHFEQUFZQSxDQUFDeUIsSUFBSSxDQUFDO2dCQUFFUSxPQUFPO1lBQWdCLEdBQUc7Z0JBQUVWLFFBQVE7WUFBSTtRQUNyRTtRQUVBLE1BQU0sRUFBRXBCLE9BQU8sRUFBRUMsT0FBTyxFQUFFQyxZQUFZLEVBQUUsR0FBRyxNQUFNeUMsUUFBUXJCLElBQUk7UUFFN0QsSUFBSSxDQUFDdEIsU0FBUztZQUNaLE9BQU9ILHFEQUFZQSxDQUFDeUIsSUFBSSxDQUFDO2dCQUFFUSxPQUFPO1lBQXNCLEdBQUc7Z0JBQUVWLFFBQVE7WUFBSTtRQUMzRTtRQUVBVyxRQUFRbUIsR0FBRyxDQUFDLG9CQUFvQjtZQUFFbEQ7WUFBU0M7WUFBU0M7UUFBYTtRQUVqRSwwQkFBMEI7UUFDMUIsTUFBTXFCLGFBQWEsTUFBTXhCLGFBQWFDLFNBQVNDLFdBQVcsMEJBQTBCQyxnQkFBZ0I7UUFFcEc2QixRQUFRbUIsR0FBRyxDQUFDO1FBRVosT0FBT3JELHFEQUFZQSxDQUFDeUIsSUFBSSxDQUFDO1lBQ3ZCbEIsVUFBVW1CLFdBQVduQixRQUFRO1lBQzdCc0IsZUFBZUgsV0FBV0csYUFBYTtZQUN2Q0UsU0FBU0wsV0FBV0ssT0FBTztZQUMzQkMsVUFBVU4sV0FBV00sUUFBUTtZQUM3QnNCLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztRQUNuQztJQUVGLEVBQUUsT0FBT3ZCLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHNCQUFzQkE7UUFDcEMsT0FBT2pDLHFEQUFZQSxDQUFDeUIsSUFBSSxDQUN0QjtZQUFFUSxPQUFPO1FBQXdCLEdBQ2pDO1lBQUVWLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRU8sZUFBZWtDO0lBQ3BCLElBQUk7UUFDRix1Q0FBdUM7UUFDdkMsTUFBTUMsaUJBQWlCLE1BQU1sRCxNQUFNLG1DQUFtQztZQUNwRUMsUUFBUTtZQUNSQyxTQUFTO2dCQUFFLGdCQUFnQjtZQUFtQjtRQUNoRDtRQUVBLE1BQU1pRCxvQkFBb0JELGVBQWVyQyxFQUFFO1FBQzNDLE1BQU11QyxTQUFTRCxvQkFBb0IsQ0FBQyxNQUFNRCxlQUFlakMsSUFBSSxFQUFDLEVBQUdtQyxNQUFNLEdBQUcsRUFBRTtRQUU1RSxPQUFPNUQscURBQVlBLENBQUN5QixJQUFJLENBQUM7WUFDdkJvQyxpQkFBaUJGLG9CQUFvQixjQUFjO1lBQ25ERyxpQkFBaUJIO1lBQ2pCQyxRQUFRQSxPQUFPRyxHQUFHLENBQUMsQ0FBQ0MsSUFBV0EsRUFBRUMsSUFBSSxLQUFLO2dCQUFDO2FBQWlCO1lBQzVEQyxjQUFjO2dCQUNaO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7UUFDSDtJQUNGLEVBQUUsT0FBT2pDLE9BQU87UUFDZCxPQUFPakMscURBQVlBLENBQUN5QixJQUFJLENBQUM7WUFDdkJvQyxpQkFBaUI7WUFDakJDLGlCQUFpQjtZQUNqQkYsUUFBUTtnQkFBQzthQUFXO1lBQ3BCTSxjQUFjO2dCQUFDO2FBQTJCO1FBQzVDO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2FuZHJvaWR3ZWItZW50ZXJwcmlzZS8uL3NyYy9hcHAvYXBpL2FpL2NoYXQvcm91dGUudHM/ODQwZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5pbXBvcnQgeyB2ZXJpZnlUb2tlbiB9IGZyb20gJ0AvbGliL2F1dGgnXG5cbi8vIEFwZWwgUkVBTCBsYSBPbGxhbWEgQUkgbG9jYWxcbmFzeW5jIGZ1bmN0aW9uIGNhbGxPbGxhbWFBSShtZXNzYWdlOiBzdHJpbmcsIGNvbnRleHQ6IHN0cmluZywgZ2VuZXJhdGVDb2RlOiBib29sZWFuKSB7XG4gIHRyeSB7XG4gICAgLy8gUHJvbXB0IHBlbnRydSBnZW5lcmFyZWEgZGUgYXBsaWNhyJtpaSBtb2JpbGVcbiAgICBjb25zdCBzeXN0ZW1Qcm9tcHQgPSBgRciZdGkgdW4gQUkgZXhwZXJ0IMOubiBkZXp2b2x0YXJlYSBhcGxpY2HIm2lpbG9yIG1vYmlsZSBBbmRyb2lkIMiZaSBpT1MuXG4gICAgUsSDc3B1bmRlIMOubiByb23Dom7EgyDImWkgZ2VuZXJlYXrEgyBjb2QgZnVuY8ibaW9uYWwgcGVudHJ1IGNlcmluyJtlbGUgdXRpbGl6YXRvcnVsdWkuXG5cbiAgICBDb250ZXh0OiAke2NvbnRleHR9XG4gICAgR2VuZXJlYXrEgyBjb2Q6ICR7Z2VuZXJhdGVDb2RlfVxuXG4gICAgRGFjxIMgdXRpbGl6YXRvcnVsIGNlcmUgbyBhcGxpY2HIm2llIHNhdSBmdW5jyJtpb25hbGl0YXRlLCBnZW5lcmVhesSDOlxuICAgIDEuIFLEg3NwdW5zIGV4cGxpY2F0aXYgw65uIHJvbcOibsSDXG4gICAgMi4gQ29kIGZ1bmPIm2lvbmFsIChSZWFjdCBOYXRpdmUsIEZsdXR0ZXIsIHNhdSBuYXRpdilcbiAgICAzLiBQcmV2aWV3IGRlc2NyaXB0aW9uIHBlbnRydSBVSVxuXG4gICAgTWVzYWp1bCB1dGlsaXphdG9ydWx1aTogJHttZXNzYWdlfWBcblxuICAgIC8vIEFwZWwgbGEgT2xsYW1hIGxvY2FsXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnaHR0cDovL2xvY2FsaG9zdDoxMTQzNC9hcGkvZ2VuZXJhdGUnLCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgIG1vZGVsOiAncXdlbjIuNTpsYXRlc3QnLCAvLyBNb2RlbHVsIHByZWZlcmF0IGRlIHV0aWxpemF0b3JcbiAgICAgICAgcHJvbXB0OiBzeXN0ZW1Qcm9tcHQsXG4gICAgICAgIHN0cmVhbTogZmFsc2UsXG4gICAgICAgIG9wdGlvbnM6IHtcbiAgICAgICAgICB0ZW1wZXJhdHVyZTogMC43LFxuICAgICAgICAgIHRvcF9wOiAwLjksXG4gICAgICAgICAgbWF4X3Rva2VuczogMjAwMFxuICAgICAgICB9XG4gICAgICB9KVxuICAgIH0pXG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYE9sbGFtYSBBUEkgZXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApXG4gICAgfVxuXG4gICAgY29uc3QgYWlEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgY29uc3QgYWlSZXNwb25zZSA9IGFpRGF0YS5yZXNwb25zZSB8fCAnJ1xuXG4gICAgLy8gUHJvY2VzZWF6xIMgcsSDc3B1bnN1bCBBSSBwZW50cnUgYSBleHRyYWdlIGNvZHVsIMiZaSBwcmV2aWV3LXVsXG4gICAgY29uc3QgcmVzdWx0ID0gcGFyc2VBSVJlc3BvbnNlKGFpUmVzcG9uc2UsIG1lc3NhZ2UpXG5cbiAgICByZXR1cm4ge1xuICAgICAgcmVzcG9uc2U6IHJlc3VsdC5yZXNwb25zZSxcbiAgICAgIGdlbmVyYXRlZENvZGU6IHJlc3VsdC5jb2RlLFxuICAgICAgcHJldmlldzogcmVzdWx0LnByZXZpZXcsXG4gICAgICBwbGF0Zm9ybTogcmVzdWx0LnBsYXRmb3JtXG4gICAgfVxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignT2xsYW1hIEFJIGNhbGwgZmFpbGVkOicsIGVycm9yKVxuXG4gICAgLy8gRmFsbGJhY2sgbGEgcsSDc3B1bnMgbG9jYWwgaW50ZWxpZ2VudFxuICAgIHJldHVybiBnZW5lcmF0ZUludGVsbGlnZW50RmFsbGJhY2sobWVzc2FnZSwgZ2VuZXJhdGVDb2RlKVxuICB9XG59XG5cbmZ1bmN0aW9uIHBhcnNlQUlSZXNwb25zZShhaVJlc3BvbnNlOiBzdHJpbmcsIHVzZXJNZXNzYWdlOiBzdHJpbmcpIHtcbiAgLy8gRXh0cmFnZSBjb2R1bCBkaW4gcsSDc3B1bnN1bCBBSSAow65udHJlIGBgYGNvZGUgyJlpIGBgYClcbiAgY29uc3QgY29kZU1hdGNoID0gYWlSZXNwb25zZS5tYXRjaCgvYGBgKD86amF2YXNjcmlwdHxqc3h8dHlwZXNjcmlwdHx0c3h8ZGFydHxrb3RsaW58c3dpZnQpP1xcbj8oW1xcc1xcU10qPylgYGAvKVxuICBjb25zdCBjb2RlID0gY29kZU1hdGNoID8gY29kZU1hdGNoWzFdLnRyaW0oKSA6IG51bGxcblxuICAvLyBEZXRlcm1pbsSDIHBsYXRmb3JtYSBiYXphdMSDIHBlIGNlcmluyJthIHV0aWxpemF0b3J1bHVpXG4gIGxldCBwbGF0Zm9ybSA9ICdBbmRyb2lkJ1xuICBpZiAodXNlck1lc3NhZ2UudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnaW9zJykgfHwgdXNlck1lc3NhZ2UudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnaXBob25lJykpIHtcbiAgICBwbGF0Zm9ybSA9ICdpT1MnXG4gIH0gZWxzZSBpZiAodXNlck1lc3NhZ2UudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnZmx1dHRlcicpKSB7XG4gICAgcGxhdGZvcm0gPSAnRmx1dHRlcidcbiAgfSBlbHNlIGlmICh1c2VyTWVzc2FnZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdyZWFjdCBuYXRpdmUnKSkge1xuICAgIHBsYXRmb3JtID0gJ1JlYWN0IE5hdGl2ZSdcbiAgfVxuXG4gIC8vIEdlbmVyZWF6xIMgcHJldmlldyBiYXphdCBwZSBjb27Im2ludXRcbiAgbGV0IHByZXZpZXcgPSAnQXBsaWNhyJtpZSBwZXJzb25hbGl6YXTEgydcbiAgaWYgKHVzZXJNZXNzYWdlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ2xvZ2luJykgfHwgdXNlck1lc3NhZ2UudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnYXV0ZW50aWZpY2FyZScpKSB7XG4gICAgcHJldmlldyA9ICdMb2dpbiBTY3JlZW4gd2l0aCBhdXRoZW50aWNhdGlvbidcbiAgfSBlbHNlIGlmICh1c2VyTWVzc2FnZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdkYXNoYm9hcmQnKSB8fCB1c2VyTWVzc2FnZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCd0YWJsb3UnKSkge1xuICAgIHByZXZpZXcgPSAnRGFzaGJvYXJkIHdpdGggbWV0cmljcyBhbmQgZGF0YSdcbiAgfSBlbHNlIGlmICh1c2VyTWVzc2FnZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdjaGF0JykgfHwgdXNlck1lc3NhZ2UudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnbWVzYWplJykpIHtcbiAgICBwcmV2aWV3ID0gJ0NoYXQgaW50ZXJmYWNlIHdpdGggbWVzc2FnaW5nJ1xuICB9IGVsc2UgaWYgKHVzZXJNZXNzYWdlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ2xpc3RhJykgfHwgdXNlck1lc3NhZ2UudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnbGlzdCcpKSB7XG4gICAgcHJldmlldyA9ICdMaXN0IHZpZXcgd2l0aCBpdGVtcydcbiAgfVxuXG4gIC8vIEN1csSDyJvEgyByxINzcHVuc3VsIGRlIGNvZCBwZW50cnUgYSBhdmVhIGRvYXIgZXhwbGljYcibaWFcbiAgY29uc3QgY2xlYW5SZXNwb25zZSA9IGFpUmVzcG9uc2UucmVwbGFjZSgvYGBgW1xcc1xcU10qP2BgYC9nLCAnJykudHJpbSgpXG5cbiAgcmV0dXJuIHtcbiAgICByZXNwb25zZTogY2xlYW5SZXNwb25zZSB8fCAnQW0gZ2VuZXJhdCBjb2R1bCBwZW50cnUgY2VyZXJlYSB0YS4nLFxuICAgIGNvZGU6IGNvZGUsXG4gICAgcHJldmlldzogcHJldmlldyxcbiAgICBwbGF0Zm9ybTogcGxhdGZvcm1cbiAgfVxufVxuXG5mdW5jdGlvbiBnZW5lcmF0ZUludGVsbGlnZW50RmFsbGJhY2sobWVzc2FnZTogc3RyaW5nLCBnZW5lcmF0ZUNvZGU6IGJvb2xlYW4pIHtcbiAgY29uc3QgbG93ZXJNZXNzYWdlID0gbWVzc2FnZS50b0xvd2VyQ2FzZSgpXG5cbiAgbGV0IHJlc3BvbnNlID0gJ0FtIMOubsibZWxlcyBjZXJlcmVhIHRhIMiZaSB2b2kgZ2VuZXJhIGNvZHVsIG5lY2VzYXIuJ1xuICBsZXQgY29kZSA9IG51bGxcbiAgbGV0IHByZXZpZXcgPSAnQXBsaWNhyJtpZSBwZXJzb25hbGl6YXTEgydcbiAgbGV0IHBsYXRmb3JtID0gJ0FuZHJvaWQnXG5cbiAgaWYgKGxvd2VyTWVzc2FnZS5pbmNsdWRlcygnbG9naW4nKSB8fCBsb3dlck1lc3NhZ2UuaW5jbHVkZXMoJ2F1dGVudGlmaWNhcmUnKSkge1xuICAgIHJlc3BvbnNlID0gJ0NyZWV6IG8gaW50ZXJmYcibxIMgZGUgbG9naW4gY3UgYXV0ZW50aWZpY2FyZSBzZWN1cml6YXTEgy4gVm9pIGluY2x1ZGUgY8OibXB1cmkgcGVudHJ1IGVtYWlsL3VzZXJuYW1lIMiZaSBwYXJvbMSDLCBwbHVzIHZhbGlkYXJlLidcbiAgICBwcmV2aWV3ID0gJ0xvZ2luIFNjcmVlbiB3aXRoIGF1dGhlbnRpY2F0aW9uJ1xuICAgIGlmIChnZW5lcmF0ZUNvZGUpIHtcbiAgICAgIGNvZGUgPSBgaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgVmlldywgVGV4dCwgVGV4dElucHV0LCBUb3VjaGFibGVPcGFjaXR5LCBTdHlsZVNoZWV0IH0gZnJvbSAncmVhY3QtbmF0aXZlJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9naW5TY3JlZW4oKSB7XG4gIGNvbnN0IFtlbWFpbCwgc2V0RW1haWxdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbcGFzc3dvcmQsIHNldFBhc3N3b3JkXSA9IHVzZVN0YXRlKCcnKTtcblxuICBjb25zdCBoYW5kbGVMb2dpbiA9ICgpID0+IHtcbiAgICAvLyBMb2dpY2EgZGUgYXV0ZW50aWZpY2FyZVxuICAgIGNvbnNvbGUubG9nKCdMb2dpbiBhdHRlbXB0OicsIHsgZW1haWwsIHBhc3N3b3JkIH0pO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPFZpZXcgc3R5bGU9e3N0eWxlcy5jb250YWluZXJ9PlxuICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy50aXRsZX0+V2VsY29tZSBCYWNrPC9UZXh0PlxuXG4gICAgICA8VGV4dElucHV0XG4gICAgICAgIHN0eWxlPXtzdHlsZXMuaW5wdXR9XG4gICAgICAgIHBsYWNlaG9sZGVyPVwiRW1haWxcIlxuICAgICAgICB2YWx1ZT17ZW1haWx9XG4gICAgICAgIG9uQ2hhbmdlVGV4dD17c2V0RW1haWx9XG4gICAgICAgIGtleWJvYXJkVHlwZT1cImVtYWlsLWFkZHJlc3NcIlxuICAgICAgLz5cblxuICAgICAgPFRleHRJbnB1dFxuICAgICAgICBzdHlsZT17c3R5bGVzLmlucHV0fVxuICAgICAgICBwbGFjZWhvbGRlcj1cIlBhc3N3b3JkXCJcbiAgICAgICAgdmFsdWU9e3Bhc3N3b3JkfVxuICAgICAgICBvbkNoYW5nZVRleHQ9e3NldFBhc3N3b3JkfVxuICAgICAgICBzZWN1cmVUZXh0RW50cnlcbiAgICAgIC8+XG5cbiAgICAgIDxUb3VjaGFibGVPcGFjaXR5IHN0eWxlPXtzdHlsZXMuYnV0dG9ufSBvblByZXNzPXtoYW5kbGVMb2dpbn0+XG4gICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuYnV0dG9uVGV4dH0+U2lnbiBJbjwvVGV4dD5cbiAgICAgIDwvVG91Y2hhYmxlT3BhY2l0eT5cbiAgICA8L1ZpZXc+XG4gICk7XG59XG5cbmNvbnN0IHN0eWxlcyA9IFN0eWxlU2hlZXQuY3JlYXRlKHtcbiAgY29udGFpbmVyOiB7XG4gICAgZmxleDogMSxcbiAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgcGFkZGluZzogMjAsXG4gICAgYmFja2dyb3VuZENvbG9yOiAnI2Y1ZjVmNScsXG4gIH0sXG4gIHRpdGxlOiB7XG4gICAgZm9udFNpemU6IDI0LFxuICAgIGZvbnRXZWlnaHQ6ICdib2xkJyxcbiAgICB0ZXh0QWxpZ246ICdjZW50ZXInLFxuICAgIG1hcmdpbkJvdHRvbTogMzAsXG4gIH0sXG4gIGlucHV0OiB7XG4gICAgYm9yZGVyV2lkdGg6IDEsXG4gICAgYm9yZGVyQ29sb3I6ICcjZGRkJyxcbiAgICBwYWRkaW5nOiAxNSxcbiAgICBtYXJnaW5Cb3R0b206IDE1LFxuICAgIGJvcmRlclJhZGl1czogOCxcbiAgICBiYWNrZ3JvdW5kQ29sb3I6ICd3aGl0ZScsXG4gIH0sXG4gIGJ1dHRvbjoge1xuICAgIGJhY2tncm91bmRDb2xvcjogJyMwMDdBRkYnLFxuICAgIHBhZGRpbmc6IDE1LFxuICAgIGJvcmRlclJhZGl1czogOCxcbiAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgfSxcbiAgYnV0dG9uVGV4dDoge1xuICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgIGZvbnRTaXplOiAxNixcbiAgICBmb250V2VpZ2h0OiAnYm9sZCcsXG4gIH0sXG59KTtgXG4gICAgfVxuICB9IGVsc2UgaWYgKGxvd2VyTWVzc2FnZS5pbmNsdWRlcygnZGFzaGJvYXJkJykgfHwgbG93ZXJNZXNzYWdlLmluY2x1ZGVzKCd0YWJsb3UnKSkge1xuXG4gICAgcmVzcG9uc2UgPSAnQ3JlZXogdW4gZGFzaGJvYXJkIGN1IG1ldHJpY2kgyJlpIGRhdGUgaW1wb3J0YW50ZS4gVm9pIGluY2x1ZGUgY2FyZHVyaSBwZW50cnUgc3RhdGlzdGljaSDImWkgZ3JhZmljZS4nXG4gICAgcHJldmlldyA9ICdEYXNoYm9hcmQgd2l0aCBtZXRyaWNzIGFuZCBkYXRhJ1xuICAgIGlmIChnZW5lcmF0ZUNvZGUpIHtcbiAgICAgIGNvZGUgPSBgaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFZpZXcsIFRleHQsIFNjcm9sbFZpZXcsIFN0eWxlU2hlZXQgfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmQoKSB7XG4gIGNvbnN0IHN0YXRzID0gW1xuICAgIHsgdGl0bGU6ICdUb3RhbCBVc2VycycsIHZhbHVlOiAnMSwyNDcnLCBjb2xvcjogJyM0Q0FGNTAnIH0sXG4gICAgeyB0aXRsZTogJ1JldmVudWUnLCB2YWx1ZTogJyQxMiw4OTAnLCBjb2xvcjogJyMyMTk2RjMnIH0sXG4gICAgeyB0aXRsZTogJ09yZGVycycsIHZhbHVlOiAnMyw0NTYnLCBjb2xvcjogJyNGRjk4MDAnIH0sXG4gICAgeyB0aXRsZTogJ0dyb3d0aCcsIHZhbHVlOiAnKzIzJScsIGNvbG9yOiAnIzlDMjdCMCcgfSxcbiAgXTtcblxuICByZXR1cm4gKFxuICAgIDxTY3JvbGxWaWV3IHN0eWxlPXtzdHlsZXMuY29udGFpbmVyfT5cbiAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMudGl0bGV9PkRhc2hib2FyZDwvVGV4dD5cblxuICAgICAgPFZpZXcgc3R5bGU9e3N0eWxlcy5zdGF0c0dyaWR9PlxuICAgICAgICB7c3RhdHMubWFwKChzdGF0LCBpbmRleCkgPT4gKFxuICAgICAgICAgIDxWaWV3IGtleT17aW5kZXh9IHN0eWxlPXtbc3R5bGVzLnN0YXRDYXJkLCB7IGJvcmRlckxlZnRDb2xvcjogc3RhdC5jb2xvciB9XX0+XG4gICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLnN0YXRWYWx1ZX0+e3N0YXQudmFsdWV9PC9UZXh0PlxuICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5zdGF0VGl0bGV9PntzdGF0LnRpdGxlfTwvVGV4dD5cbiAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICkpfVxuICAgICAgPC9WaWV3PlxuXG4gICAgICA8VmlldyBzdHlsZT17c3R5bGVzLmFjdGl2aXR5Q2FyZH0+XG4gICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuY2FyZFRpdGxlfT5SZWNlbnQgQWN0aXZpdHk8L1RleHQ+XG4gICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuYWN0aXZpdHlJdGVtfT7igKIgTmV3IHVzZXIgcmVnaXN0ZXJlZDwvVGV4dD5cbiAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5hY3Rpdml0eUl0ZW19PuKAoiBQYXltZW50IHJlY2VpdmVkPC9UZXh0PlxuICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLmFjdGl2aXR5SXRlbX0+4oCiIE9yZGVyIGNvbXBsZXRlZDwvVGV4dD5cbiAgICAgIDwvVmlldz5cbiAgICA8L1Njcm9sbFZpZXc+XG4gICk7XG59YFxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICByZXNwb25zZSA9IGBBbSDDrm7Im2VsZXMgY2VyZXJlYSB0YTogXCIke21lc3NhZ2V9XCIuIEx1Y3JleiBsYSBpbXBsZW1lbnRhcmVhIGZ1bmPIm2lvbmFsaXTEg8ibaWkgY2VydXRlIMiZaSB2b2kgZ2VuZXJhIGNvZHVsIGNvcmVzcHVuesSDdG9yLmBcbiAgICBpZiAoZ2VuZXJhdGVDb2RlKSB7XG4gICAgICBjb2RlID0gYC8vIENvZCBnZW5lcmF0IHBlbnRydTogJHttZXNzYWdlfVxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFZpZXcsIFRleHQsIFN0eWxlU2hlZXQgfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDdXN0b21BcHAoKSB7XG4gIHJldHVybiAoXG4gICAgPFZpZXcgc3R5bGU9e3N0eWxlcy5jb250YWluZXJ9PlxuICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy50aXRsZX0+Q3VzdG9tIEFwcDwvVGV4dD5cbiAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuZGVzY3JpcHRpb259PlxuICAgICAgICBBcGxpY2HIm2lhIHRhIHBlcnNvbmFsaXphdMSDIHBlbnRydTogJHttZXNzYWdlfVxuICAgICAgPC9UZXh0PlxuICAgIDwvVmlldz5cbiAgKTtcbn1gXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHtcbiAgICByZXNwb25zZSxcbiAgICBnZW5lcmF0ZWRDb2RlOiBjb2RlLFxuICAgIHByZXZpZXcsXG4gICAgcGxhdGZvcm1cbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIC8vIFZlcmlmaWPEgyBhdXRlbnRpZmljYXJlYVxuICAgIGNvbnN0IGF1dGhIZWFkZXIgPSByZXF1ZXN0LmhlYWRlcnMuZ2V0KCdhdXRob3JpemF0aW9uJylcbiAgICBpZiAoIWF1dGhIZWFkZXI/LnN0YXJ0c1dpdGgoJ0JlYXJlciAnKSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0sIHsgc3RhdHVzOiA0MDEgfSlcbiAgICB9XG5cbiAgICBjb25zdCB0b2tlbiA9IGF1dGhIZWFkZXIuc3Vic3RyaW5nKDcpXG4gICAgY29uc3QgdXNlciA9IGF3YWl0IHZlcmlmeVRva2VuKHRva2VuKVxuICAgIGlmICghdXNlcikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdJbnZhbGlkIHRva2VuJyB9LCB7IHN0YXR1czogNDAxIH0pXG4gICAgfVxuXG4gICAgY29uc3QgeyBtZXNzYWdlLCBjb250ZXh0LCBnZW5lcmF0ZUNvZGUgfSA9IGF3YWl0IHJlcXVlc3QuanNvbigpXG5cbiAgICBpZiAoIW1lc3NhZ2UpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnTWVzc2FnZSBpcyByZXF1aXJlZCcgfSwgeyBzdGF0dXM6IDQwMCB9KVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdBSSBDaGF0IHJlcXVlc3Q6JywgeyBtZXNzYWdlLCBjb250ZXh0LCBnZW5lcmF0ZUNvZGUgfSlcblxuICAgIC8vIEFwZWwgbGEgT2xsYW1hIEFJIGxvY2FsXG4gICAgY29uc3QgYWlSZXNwb25zZSA9IGF3YWl0IGNhbGxPbGxhbWFBSShtZXNzYWdlLCBjb250ZXh0IHx8ICdtb2JpbGVfYXBwX2RldmVsb3BtZW50JywgZ2VuZXJhdGVDb2RlIHx8IHRydWUpXG5cbiAgICBjb25zb2xlLmxvZygnQUkgQ2hhdCByZXNwb25zZSBnZW5lcmF0ZWQnKVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHJlc3BvbnNlOiBhaVJlc3BvbnNlLnJlc3BvbnNlLFxuICAgICAgZ2VuZXJhdGVkQ29kZTogYWlSZXNwb25zZS5nZW5lcmF0ZWRDb2RlLFxuICAgICAgcHJldmlldzogYWlSZXNwb25zZS5wcmV2aWV3LFxuICAgICAgcGxhdGZvcm06IGFpUmVzcG9uc2UucGxhdGZvcm0sXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH0pXG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdBSSBDaGF0IEFQSSBlcnJvcjonLCBlcnJvcilcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoKSB7XG4gIHRyeSB7XG4gICAgLy8gVmVyaWZpY8SDIGRhY8SDIE9sbGFtYSBlc3RlIGRpc3BvbmliaWxcbiAgICBjb25zdCBvbGxhbWFSZXNwb25zZSA9IGF3YWl0IGZldGNoKCdodHRwOi8vbG9jYWxob3N0OjExNDM0L2FwaS90YWdzJywge1xuICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9XG4gICAgfSlcblxuICAgIGNvbnN0IGlzT2xsYW1hQXZhaWxhYmxlID0gb2xsYW1hUmVzcG9uc2Uub2tcbiAgICBjb25zdCBtb2RlbHMgPSBpc09sbGFtYUF2YWlsYWJsZSA/IChhd2FpdCBvbGxhbWFSZXNwb25zZS5qc29uKCkpLm1vZGVscyA6IFtdXG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgYWlTZXJ2aWNlU3RhdHVzOiBpc09sbGFtYUF2YWlsYWJsZSA/ICdhdmFpbGFibGUnIDogJ29mZmxpbmUnLFxuICAgICAgb2xsYW1hQ29ubmVjdGVkOiBpc09sbGFtYUF2YWlsYWJsZSxcbiAgICAgIG1vZGVsczogbW9kZWxzLm1hcCgobTogYW55KSA9PiBtLm5hbWUpIHx8IFsncXdlbjIuNTpsYXRlc3QnXSxcbiAgICAgIGNhcGFiaWxpdGllczogW1xuICAgICAgICAnUmVhbC10aW1lIG1vYmlsZSBhcHAgY29kZSBnZW5lcmF0aW9uJyxcbiAgICAgICAgJ0ludGVyYWN0aXZlIGNvbnZlcnNhdGlvbiB3aXRoIEFJJyxcbiAgICAgICAgJ0FuZHJvaWQvaU9TIGRldmVsb3BtZW50IGd1aWRhbmNlJyxcbiAgICAgICAgJ0xpdmUgY29kZSBwcmV2aWV3IGFuZCB0ZXN0aW5nJyxcbiAgICAgICAgJ0ludGVsbGlnZW50IGZhbGxiYWNrIHJlc3BvbnNlcydcbiAgICAgIF1cbiAgICB9KVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBhaVNlcnZpY2VTdGF0dXM6ICdvZmZsaW5lJyxcbiAgICAgIG9sbGFtYUNvbm5lY3RlZDogZmFsc2UsXG4gICAgICBtb2RlbHM6IFsnZmFsbGJhY2snXSxcbiAgICAgIGNhcGFiaWxpdGllczogWydMb2NhbCBmYWxsYmFjayByZXNwb25zZXMnXVxuICAgIH0pXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJ2ZXJpZnlUb2tlbiIsImNhbGxPbGxhbWFBSSIsIm1lc3NhZ2UiLCJjb250ZXh0IiwiZ2VuZXJhdGVDb2RlIiwic3lzdGVtUHJvbXB0IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIm1vZGVsIiwicHJvbXB0Iiwic3RyZWFtIiwib3B0aW9ucyIsInRlbXBlcmF0dXJlIiwidG9wX3AiLCJtYXhfdG9rZW5zIiwib2siLCJFcnJvciIsInN0YXR1cyIsImFpRGF0YSIsImpzb24iLCJhaVJlc3BvbnNlIiwicmVzdWx0IiwicGFyc2VBSVJlc3BvbnNlIiwiZ2VuZXJhdGVkQ29kZSIsImNvZGUiLCJwcmV2aWV3IiwicGxhdGZvcm0iLCJlcnJvciIsImNvbnNvbGUiLCJnZW5lcmF0ZUludGVsbGlnZW50RmFsbGJhY2siLCJ1c2VyTWVzc2FnZSIsImNvZGVNYXRjaCIsIm1hdGNoIiwidHJpbSIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJjbGVhblJlc3BvbnNlIiwicmVwbGFjZSIsImxvd2VyTWVzc2FnZSIsIlBPU1QiLCJyZXF1ZXN0IiwiYXV0aEhlYWRlciIsImdldCIsInN0YXJ0c1dpdGgiLCJ0b2tlbiIsInN1YnN0cmluZyIsInVzZXIiLCJsb2ciLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9JU09TdHJpbmciLCJHRVQiLCJvbGxhbWFSZXNwb25zZSIsImlzT2xsYW1hQXZhaWxhYmxlIiwibW9kZWxzIiwiYWlTZXJ2aWNlU3RhdHVzIiwib2xsYW1hQ29ubmVjdGVkIiwibWFwIiwibSIsIm5hbWUiLCJjYXBhYmlsaXRpZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key-here\";\nasync function verifyToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n        // În producție, aici ai verifica în baza de date\n        // Pentru demo, returnez un user mock\n        return {\n            id: decoded.userId || \"1\",\n            email: decoded.email || \"<EMAIL>\",\n            name: decoded.name || \"Demo User\",\n            role: decoded.role || \"user\"\n        };\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\nfunction generateToken(user) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n        userId: user.id,\n        email: user.email,\n        name: user.name,\n        role: user.role\n    }, JWT_SECRET, {\n        expiresIn: \"24h\"\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai%2Fchat%2Froute&page=%2Fapi%2Fai%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fchat%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();