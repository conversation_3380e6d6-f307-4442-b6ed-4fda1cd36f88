"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/health/route";
exports.ids = ["app/api/ai/health/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai%2Fhealth%2Froute&page=%2Fapi%2Fai%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fhealth%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai%2Fhealth%2Froute&page=%2Fapi%2Fai%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fhealth%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_claudiu_Desktop_proiecte_androidweb_src_app_api_ai_health_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/health/route.ts */ \"(rsc)/./src/app/api/ai/health/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/health/route\",\n        pathname: \"/api/ai/health\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/health/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/api/ai/health/route.ts\",\n    nextConfigOutput,\n    userland: _home_claudiu_Desktop_proiecte_androidweb_src_app_api_ai_health_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/ai/health/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai%2Fhealth%2Froute&page=%2Fapi%2Fai%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fhealth%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/ai/health/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/ai/health/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_ollama_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/ollama-service */ \"(rsc)/./src/lib/ollama-service.ts\");\n\n\nasync function GET(request) {\n    try {\n        const ollamaService = new _lib_ollama_service__WEBPACK_IMPORTED_MODULE_1__.OllamaService();\n        const isHealthy = await ollamaService.checkHealth();\n        if (isHealthy) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: \"healthy\",\n                message: \"Ollama AI service is running and ready\",\n                timestamp: new Date().toISOString()\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: \"unhealthy\",\n                message: \"Ollama AI service is not available or no models are loaded\",\n                timestamp: new Date().toISOString()\n            }, {\n                status: 503\n            });\n        }\n    } catch (error) {\n        console.error(\"Health check error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: \"error\",\n            message: \"Failed to check Ollama service health\",\n            error: error instanceof Error ? error.message : \"Unknown error\",\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9haS9oZWFsdGgvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVEO0FBQ0g7QUFFN0MsZUFBZUUsSUFBSUMsT0FBb0I7SUFDNUMsSUFBSTtRQUNGLE1BQU1DLGdCQUFnQixJQUFJSCw4REFBYUE7UUFDdkMsTUFBTUksWUFBWSxNQUFNRCxjQUFjRSxXQUFXO1FBRWpELElBQUlELFdBQVc7WUFDYixPQUFPTCxxREFBWUEsQ0FBQ08sSUFBSSxDQUFDO2dCQUN2QkMsUUFBUTtnQkFDUkMsU0FBUztnQkFDVEMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ25DO1FBQ0YsT0FBTztZQUNMLE9BQU9aLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7Z0JBQ3ZCQyxRQUFRO2dCQUNSQyxTQUFTO2dCQUNUQyxXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDbkMsR0FBRztnQkFBRUosUUFBUTtZQUFJO1FBQ25CO0lBQ0YsRUFBRSxPQUFPSyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyx1QkFBdUJBO1FBQ3JDLE9BQU9iLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7WUFDdkJDLFFBQVE7WUFDUkMsU0FBUztZQUNUSSxPQUFPQSxpQkFBaUJFLFFBQVFGLE1BQU1KLE9BQU8sR0FBRztZQUNoREMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1FBQ25DLEdBQUc7WUFBRUosUUFBUTtRQUFJO0lBQ25CO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmRyb2lkd2ViLWVudGVycHJpc2UvLi9zcmMvYXBwL2FwaS9haS9oZWFsdGgvcm91dGUudHM/YmU2MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSBcIm5leHQvc2VydmVyXCJcbmltcG9ydCB7IE9sbGFtYVNlcnZpY2UgfSBmcm9tIFwiQC9saWIvb2xsYW1hLXNlcnZpY2VcIlxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3Qgb2xsYW1hU2VydmljZSA9IG5ldyBPbGxhbWFTZXJ2aWNlKClcbiAgICBjb25zdCBpc0hlYWx0aHkgPSBhd2FpdCBvbGxhbWFTZXJ2aWNlLmNoZWNrSGVhbHRoKClcbiAgICBcbiAgICBpZiAoaXNIZWFsdGh5KSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdGF0dXM6IFwiaGVhbHRoeVwiLFxuICAgICAgICBtZXNzYWdlOiBcIk9sbGFtYSBBSSBzZXJ2aWNlIGlzIHJ1bm5pbmcgYW5kIHJlYWR5XCIsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KVxuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdGF0dXM6IFwidW5oZWFsdGh5XCIsXG4gICAgICAgIG1lc3NhZ2U6IFwiT2xsYW1hIEFJIHNlcnZpY2UgaXMgbm90IGF2YWlsYWJsZSBvciBubyBtb2RlbHMgYXJlIGxvYWRlZFwiLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSwgeyBzdGF0dXM6IDUwMyB9KVxuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKFwiSGVhbHRoIGNoZWNrIGVycm9yOlwiLCBlcnJvcilcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3RhdHVzOiBcImVycm9yXCIsXG4gICAgICBtZXNzYWdlOiBcIkZhaWxlZCB0byBjaGVjayBPbGxhbWEgc2VydmljZSBoZWFsdGhcIixcbiAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiVW5rbm93biBlcnJvclwiLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9LCB7IHN0YXR1czogNTAwIH0pXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJPbGxhbWFTZXJ2aWNlIiwiR0VUIiwicmVxdWVzdCIsIm9sbGFtYVNlcnZpY2UiLCJpc0hlYWx0aHkiLCJjaGVja0hlYWx0aCIsImpzb24iLCJzdGF0dXMiLCJtZXNzYWdlIiwidGltZXN0YW1wIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiZXJyb3IiLCJjb25zb2xlIiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/health/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ollama-service.ts":
/*!***********************************!*\
  !*** ./src/lib/ollama-service.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaService: () => (/* binding */ OllamaService)\n/* harmony export */ });\nclass OllamaService {\n    constructor(){\n        this.baseUrl = process.env.OLLAMA_BASE_URL || \"http://localhost:11434\";\n        this.model = process.env.OLLAMA_MODEL || \"qwen2.5:7b\";\n    }\n    async checkHealth() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/tags`);\n            if (!response.ok) return false;\n            const data = await response.json();\n            // Check if any model is available\n            if (!data.models || data.models.length === 0) {\n                console.log(\"No models available in Ollama\");\n                return false;\n            }\n            // Try to find preferred model first\n            let availableModel = data.models.find((model)=>model.name.includes(\"qwen2.5\") || model.name.includes(\"qwen\"));\n            // If preferred model not found, use any available model\n            if (!availableModel) {\n                availableModel = data.models[0];\n                this.model = availableModel.name;\n                console.log(`Using available model: ${this.model}`);\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Ollama health check failed:\", error);\n            return false;\n        }\n    }\n    async generateAppArchitecture(request) {\n        const prompt = `You are an expert mobile app architect. Create a detailed architecture for a ${request.platform} mobile application.\n\nApp Details:\n- Name: ${request.name}\n- Description: ${request.description}\n- Platform: ${request.platform}\n- Features: ${request.features.join(\", \")}\n- UI Style: ${request.uiStyle || \"Modern\"}\n- Color Scheme: ${request.colorScheme || \"Blue\"}\n- Target Audience: ${request.targetAudience || \"General\"}\n\nGenerate a comprehensive architecture including:\n1. Main screens and their purposes\n2. Navigation structure\n3. Data flow patterns\n4. Required components\n5. API endpoints needed\n6. Database schema\n7. Authentication strategy\n8. Styling approach\n\nRespond in JSON format with the following structure:\n{\n  \"screens\": [\"screen1\", \"screen2\", ...],\n  \"navigation\": \"navigation pattern description\",\n  \"dataFlow\": \"data flow description\",\n  \"components\": [\"component1\", \"component2\", ...],\n  \"apis\": [\"api1\", \"api2\", ...],\n  \"database\": \"database schema description\",\n  \"authentication\": \"auth strategy description\",\n  \"styling\": \"styling approach description\"\n}`;\n        try {\n            const response = await this.callOllama(prompt);\n            return this.parseArchitectureResponse(response);\n        } catch (error) {\n            console.error(\"Failed to generate architecture:\", error);\n            // Fallback architecture\n            return this.getFallbackArchitecture(request);\n        }\n    }\n    async generateCode(prompt) {\n        try {\n            return await this.callOllama(prompt);\n        } catch (error) {\n            console.error(\"Failed to generate code:\", error);\n            throw new Error(\"Code generation failed\");\n        }\n    }\n    async callOllama(prompt) {\n        const response = await fetch(`${this.baseUrl}/api/generate`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                model: this.model,\n                prompt,\n                stream: false,\n                options: {\n                    temperature: 0.7,\n                    top_p: 0.9,\n                    max_tokens: 4000\n                }\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`Ollama API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.response;\n    }\n    parseArchitectureResponse(response) {\n        try {\n            // Try to extract JSON from the response\n            const jsonMatch = response.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                const parsed = JSON.parse(jsonMatch[0]);\n                return {\n                    screens: Array.isArray(parsed.screens) ? parsed.screens : [],\n                    navigation: parsed.navigation || \"Tab-based navigation\",\n                    dataFlow: parsed.dataFlow || \"Redux/Context pattern\",\n                    components: Array.isArray(parsed.components) ? parsed.components : [],\n                    apis: Array.isArray(parsed.apis) ? parsed.apis : [],\n                    database: parsed.database || \"SQLite local storage\",\n                    authentication: parsed.authentication || \"JWT token-based\",\n                    styling: parsed.styling || \"Component-based styling\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Failed to parse architecture response:\", error);\n        }\n        // Fallback parsing from text\n        return this.parseArchitectureFromText(response);\n    }\n    parseArchitectureFromText(response) {\n        return {\n            screens: this.extractListFromText(response, \"screens?\"),\n            navigation: this.extractSectionFromText(response, \"navigation\") || \"Tab-based navigation\",\n            dataFlow: this.extractSectionFromText(response, \"data flow\") || \"State management pattern\",\n            components: this.extractListFromText(response, \"components?\"),\n            apis: this.extractListFromText(response, \"api|endpoint\"),\n            database: this.extractSectionFromText(response, \"database\") || \"Local storage\",\n            authentication: this.extractSectionFromText(response, \"auth\") || \"Token-based authentication\",\n            styling: this.extractSectionFromText(response, \"styl\") || \"Modern component styling\"\n        };\n    }\n    extractListFromText(text, keyword) {\n        const regex = new RegExp(`${keyword}[^\\\\n]*:?[^\\\\n]*\\\\n([\\\\s\\\\S]*?)(?=\\\\n\\\\n|\\\\n[A-Z]|$)`, \"i\");\n        const match = text.match(regex);\n        if (match) {\n            return match[1].split(\"\\n\").map((line)=>line.replace(/^[-*•]\\s*/, \"\").trim()).filter((line)=>line.length > 0).slice(0, 10) // Limit to 10 items\n            ;\n        }\n        return [];\n    }\n    extractSectionFromText(text, keyword) {\n        const regex = new RegExp(`${keyword}[^\\\\n]*:?[^\\\\n]*\\\\n([^\\\\n]+)`, \"i\");\n        const match = text.match(regex);\n        return match ? match[1].trim() : \"\";\n    }\n    getFallbackArchitecture(request) {\n        const baseScreens = [\n            \"Home\",\n            \"Profile\",\n            \"Settings\"\n        ];\n        const featureScreens = request.features.map((feature)=>feature.charAt(0).toUpperCase() + feature.slice(1).replace(/[^a-zA-Z]/g, \"\"));\n        return {\n            screens: [\n                ...baseScreens,\n                ...featureScreens\n            ],\n            navigation: request.platform === \"IOS\" ? \"UINavigationController with TabBar\" : \"Navigation Drawer with Bottom Navigation\",\n            dataFlow: \"MVVM with Repository pattern\",\n            components: [\n                \"Header\",\n                \"Button\",\n                \"Input\",\n                \"Card\",\n                \"List\",\n                \"Modal\"\n            ],\n            apis: [\n                \"/auth\",\n                \"/user\",\n                \"/data\",\n                \"/upload\"\n            ],\n            database: request.platform === \"IOS\" ? \"Core Data\" : \"Room Database\",\n            authentication: \"OAuth 2.0 with JWT tokens\",\n            styling: request.platform === \"IOS\" ? \"SwiftUI styling\" : \"Material Design 3\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ollama-service.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai%2Fhealth%2Froute&page=%2Fapi%2Fai%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fhealth%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();