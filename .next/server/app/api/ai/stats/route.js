"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/stats/route";
exports.ids = ["app/api/ai/stats/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai%2Fstats%2Froute&page=%2Fapi%2Fai%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fstats%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai%2Fstats%2Froute&page=%2Fapi%2Fai%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fstats%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_claudiu_Desktop_proiecte_androidweb_src_app_api_ai_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/stats/route.ts */ \"(rsc)/./src/app/api/ai/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/stats/route\",\n        pathname: \"/api/ai/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/stats/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/api/ai/stats/route.ts\",\n    nextConfigOutput,\n    userland: _home_claudiu_Desktop_proiecte_androidweb_src_app_api_ai_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/ai/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai%2Fstats%2Froute&page=%2Fapi%2Fai%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fstats%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/ai/stats/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/ai/stats/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_app_generation_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/app-generation-service */ \"(rsc)/./src/lib/app-generation-service.ts\");\n/* harmony import */ var _lib_json_storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/json-storage */ \"(rsc)/./src/lib/json-storage.ts\");\n\n\n\n/**\n * GET /api/ai/stats\n * Get AI evolution statistics and metrics\n */ async function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const modelId = searchParams.get(\"modelId\");\n        const period = searchParams.get(\"period\") || \"30d\"; // Default to 30 days\n        const metric = searchParams.get(\"metric\");\n        const storage = new _lib_json_storage__WEBPACK_IMPORTED_MODULE_2__.JsonStorage();\n        const generationService = new _lib_app_generation_service__WEBPACK_IMPORTED_MODULE_1__.AppGenerationService();\n        // Get basic generation stats\n        const generationStats = generationService.getGenerationStats();\n        // Get AI learning data\n        const learningData = storage.getAll(\"ai_learning_data\");\n        // Get AI models\n        const models = storage.getAll(\"ai_models\");\n        // Get evolution metrics\n        const evolutionMetrics = storage.getAll(\"ai_evolution_metrics\");\n        // Filter by model if specified\n        const filteredMetrics = modelId ? evolutionMetrics.filter((m)=>m.model_id === modelId) : evolutionMetrics;\n        // Filter by metric type if specified\n        const filteredByMetric = metric ? filteredMetrics.filter((m)=>m.metric_type === metric) : filteredMetrics;\n        // Calculate period start date\n        const periodStart = calculatePeriodStart(period);\n        // Filter by period\n        const filteredByPeriod = filteredByMetric.filter((m)=>new Date(m.recorded_at) >= periodStart);\n        // Group metrics by type for visualization\n        const metricsGroupedByType = groupMetricsByType(filteredByPeriod);\n        // Calculate improvement trends\n        const improvementTrends = calculateImprovementTrends(filteredByPeriod);\n        // Calculate learning velocity\n        const learningVelocity = calculateLearningVelocity(learningData, periodStart);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            stats: {\n                generation: generationStats,\n                models: {\n                    total: models.length,\n                    active: models.filter((m)=>m.status === \"active\").length,\n                    list: models.map((m)=>({\n                            id: m.id,\n                            name: m.name,\n                            type: m.type,\n                            status: m.status,\n                            performance_score: m.performance_score,\n                            total_generations: m.total_generations,\n                            success_rate: m.success_rate\n                        }))\n                },\n                learning: {\n                    totalInteractions: learningData.length,\n                    successRate: calculateSuccessRate(learningData),\n                    recentInteractions: learningData.filter((d)=>new Date(d.created_at) >= periodStart).length,\n                    velocity: learningVelocity\n                },\n                evolution: {\n                    metrics: metricsGroupedByType,\n                    trends: improvementTrends\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to get AI stats:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to retrieve AI statistics\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * POST /api/ai/stats\n * Record new AI evolution metrics\n */ async function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate request body\n        if (!body.model_id || !body.metric_type || body.metric_value === undefined) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing required fields: model_id, metric_type, metric_value\"\n            }, {\n                status: 400\n            });\n        }\n        const storage = new _lib_json_storage__WEBPACK_IMPORTED_MODULE_2__.JsonStorage();\n        // Get previous metric value for comparison if available\n        const previousMetrics = storage.find(\"ai_evolution_metrics\", (m)=>m.model_id === body.model_id && m.metric_type === body.metric_type).sort((a, b)=>new Date(b.recorded_at).getTime() - new Date(a.recorded_at).getTime());\n        const previousValue = previousMetrics.length > 0 ? previousMetrics[0].metric_value : null;\n        // Calculate improvement percentage if previous value exists\n        let improvementPercentage = null;\n        if (previousValue !== null) {\n            improvementPercentage = (body.metric_value - previousValue) / previousValue * 100;\n        }\n        // Insert new metric\n        const newMetric = storage.insert(\"ai_evolution_metrics\", {\n            model_id: body.model_id,\n            metric_type: body.metric_type,\n            metric_value: body.metric_value,\n            comparison_baseline: previousValue,\n            improvement_percentage: improvementPercentage,\n            recorded_at: new Date().toISOString()\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            metric: newMetric,\n            message: \"AI evolution metric recorded successfully\"\n        });\n    } catch (error) {\n        console.error(\"Failed to record AI metric:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to record AI metric\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * Calculate period start date based on period string\n */ function calculatePeriodStart(period) {\n    const now = new Date();\n    const match = period.match(/^(\\d+)([dwmy])$/);\n    if (!match) {\n        // Default to 30 days if invalid format\n        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n    }\n    const value = parseInt(match[1]);\n    const unit = match[2];\n    switch(unit){\n        case \"d\":\n            return new Date(now.getTime() - value * 24 * 60 * 60 * 1000);\n        case \"w\":\n            return new Date(now.getTime() - value * 7 * 24 * 60 * 60 * 1000);\n        case \"m\":\n            const newMonth = now.getMonth() - value;\n            return new Date(now.getFullYear(), newMonth, now.getDate());\n        case \"y\":\n            return new Date(now.getFullYear() - value, now.getMonth(), now.getDate());\n        default:\n            return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n    }\n}\n/**\n * Group metrics by type for visualization\n */ function groupMetricsByType(metrics) {\n    const grouped = {};\n    for (const metric of metrics){\n        if (!grouped[metric.metric_type]) {\n            grouped[metric.metric_type] = [];\n        }\n        grouped[metric.metric_type].push({\n            value: metric.metric_value,\n            date: metric.recorded_at,\n            improvement: metric.improvement_percentage\n        });\n    }\n    // Sort each group by date\n    for(const type in grouped){\n        grouped[type].sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime());\n    }\n    return grouped;\n}\n/**\n * Calculate improvement trends\n */ function calculateImprovementTrends(metrics) {\n    const trends = {};\n    const typeGroups = {};\n    // Group by metric type\n    for (const metric of metrics){\n        if (!typeGroups[metric.metric_type]) {\n            typeGroups[metric.metric_type] = [];\n        }\n        typeGroups[metric.metric_type].push(metric);\n    }\n    // Calculate average improvement for each type\n    for(const type in typeGroups){\n        const metricsWithImprovement = typeGroups[type].filter((m)=>m.improvement_percentage !== null);\n        if (metricsWithImprovement.length > 0) {\n            const sum = metricsWithImprovement.reduce((acc, m)=>acc + m.improvement_percentage, 0);\n            trends[type] = sum / metricsWithImprovement.length;\n        } else {\n            trends[type] = 0;\n        }\n    }\n    return trends;\n}\n/**\n * Calculate success rate from learning data\n */ function calculateSuccessRate(learningData) {\n    if (learningData.length === 0) return 0;\n    const successfulInteractions = learningData.filter((d)=>d.success).length;\n    return successfulInteractions / learningData.length * 100;\n}\n/**\n * Calculate learning velocity (interactions per day)\n */ function calculateLearningVelocity(learningData, periodStart) {\n    const recentData = learningData.filter((d)=>new Date(d.created_at) >= periodStart);\n    if (recentData.length === 0) return 0;\n    const now = new Date();\n    const daysDiff = (now.getTime() - periodStart.getTime()) / (24 * 60 * 60 * 1000);\n    return recentData.length / daysDiff;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/app-generation-service.ts":
/*!*******************************************!*\
  !*** ./src/lib/app-generation-service.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppGenerationService: () => (/* binding */ AppGenerationService)\n/* harmony export */ });\n/* harmony import */ var _json_storage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./json-storage */ \"(rsc)/./src/lib/json-storage.ts\");\n/* harmony import */ var _ollama_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ollama-service */ \"(rsc)/./src/lib/ollama-service.ts\");\n\n\n/**\n * Real service for managing app generation with AI\n */ class AppGenerationService {\n    constructor(){\n        this.storage = new _json_storage__WEBPACK_IMPORTED_MODULE_0__.JsonStorage();\n        this.ollamaService = new _ollama_service__WEBPACK_IMPORTED_MODULE_1__.OllamaService();\n        this.storage.seedInitialData();\n    }\n    /**\n   * Generate a new mobile application\n   */ async generateApp(userId, request) {\n        const startTime = Date.now();\n        // Create initial app record\n        const app = this.storage.insert(\"generated_apps\", {\n            userId,\n            name: request.name,\n            description: request.description,\n            platform: request.platform,\n            status: \"GENERATING\",\n            generatedCode: {},\n            architecture: null,\n            features: request.features,\n            metadata: {\n                generationTime: 0,\n                linesOfCode: 0,\n                filesGenerated: 0,\n                aiModelUsed: \"unknown\"\n            }\n        });\n        try {\n            // Check if AI is available\n            const isAIAvailable = await this.ollamaService.checkHealth();\n            let architecture;\n            let generatedCode = {};\n            let aiModelUsed = \"fallback\";\n            if (isAIAvailable) {\n                console.log(\"\\uD83E\\uDD16 Using real AI for generation\");\n                // Generate architecture with AI\n                architecture = await this.ollamaService.generateAppArchitecture({\n                    name: request.name,\n                    description: request.description,\n                    platform: request.platform,\n                    features: request.features,\n                    uiStyle: request.uiStyle || \"Modern\",\n                    colorScheme: request.colorScheme || \"Blue\",\n                    targetAudience: request.targetAudience || \"General\"\n                });\n                // Generate code files with AI\n                generatedCode = await this.generateCodeFiles(request, architecture);\n                aiModelUsed = \"qwen2.5-coder\";\n            } else {\n                console.log(\"⚡ Using professional fallback generation\");\n                // Use professional fallback generation\n                architecture = this.generateFallbackArchitecture(request);\n                generatedCode = this.generateFallbackCode(request, architecture);\n                aiModelUsed = \"professional-fallback\";\n            }\n            // Calculate metrics\n            const linesOfCode = Object.values(generatedCode).reduce((total, code)=>{\n                return total + code.split(\"\\n\").length;\n            }, 0);\n            const filesGenerated = Object.keys(generatedCode).length;\n            const generationTime = Date.now() - startTime;\n            // Update app with results\n            const updatedApp = this.storage.update(\"generated_apps\", app.id, {\n                status: \"COMPLETED\",\n                generatedCode,\n                architecture,\n                metadata: {\n                    generationTime,\n                    linesOfCode,\n                    filesGenerated,\n                    aiModelUsed\n                }\n            });\n            // Log learning data\n            this.logLearningData(aiModelUsed, request, {\n                success: true,\n                generationTime,\n                linesOfCode,\n                filesGenerated\n            });\n            return updatedApp;\n        } catch (error) {\n            console.error(\"App generation failed:\", error);\n            // Update app with error status\n            const failedApp = this.storage.update(\"generated_apps\", app.id, {\n                status: \"FAILED\",\n                metadata: {\n                    ...app.metadata,\n                    generationTime: Date.now() - startTime,\n                    error: error instanceof Error ? error.message : \"Unknown error\"\n                }\n            });\n            // Log learning data for failure\n            this.logLearningData(\"unknown\", request, {\n                success: false,\n                error: error instanceof Error ? error.message : \"Unknown error\"\n            });\n            throw error;\n        }\n    }\n    /**\n   * Generate code files using AI or fallback\n   */ async generateCodeFiles(request, architecture) {\n        const files = {};\n        switch(request.platform){\n            case \"ANDROID\":\n                files[\"MainActivity.kt\"] = await this.generateAndroidMainActivity(request, architecture);\n                files[\"build.gradle\"] = this.generateAndroidBuildGradle(request);\n                files[\"AndroidManifest.xml\"] = this.generateAndroidManifest(request);\n                files[\"strings.xml\"] = this.generateAndroidStrings(request);\n                files[\"activity_main.xml\"] = this.generateAndroidLayout(request);\n                break;\n            case \"IOS\":\n                files[\"ContentView.swift\"] = await this.generateIOSContentView(request, architecture);\n                files[\"App.swift\"] = this.generateIOSApp(request);\n                files[\"Info.plist\"] = this.generateIOSInfoPlist(request);\n                break;\n            case \"CROSS_PLATFORM\":\n                files[\"App.tsx\"] = await this.generateReactNativeApp(request, architecture);\n                files[\"package.json\"] = this.generateReactNativePackageJson(request);\n                files[\"metro.config.js\"] = this.generateReactNativeMetroConfig();\n                break;\n        }\n        return files;\n    }\n    /**\n   * Generate professional fallback architecture\n   */ generateFallbackArchitecture(request) {\n        return {\n            name: request.name,\n            platform: request.platform,\n            architecture: \"MVVM\",\n            navigation: request.platform === \"ANDROID\" ? \"Navigation Component\" : request.platform === \"IOS\" ? \"SwiftUI Navigation\" : \"React Navigation\",\n            database: request.platform === \"ANDROID\" ? \"Room\" : request.platform === \"IOS\" ? \"Core Data\" : \"AsyncStorage\",\n            networking: \"REST API with Retrofit/URLSession/Axios\",\n            authentication: \"JWT Token Based\",\n            features: request.features,\n            screens: this.generateScreensForFeatures(request.features),\n            components: this.generateComponentsForFeatures(request.features)\n        };\n    }\n    /**\n   * Generate professional fallback code\n   */ generateFallbackCode(request, architecture) {\n        const files = {};\n        switch(request.platform){\n            case \"ANDROID\":\n                files[\"MainActivity.kt\"] = this.generateProfessionalAndroidCode(request, architecture);\n                files[\"build.gradle\"] = this.generateAndroidBuildGradle(request);\n                files[\"AndroidManifest.xml\"] = this.generateAndroidManifest(request);\n                break;\n            case \"IOS\":\n                files[\"ContentView.swift\"] = this.generateProfessionalIOSCode(request, architecture);\n                files[\"App.swift\"] = this.generateIOSApp(request);\n                break;\n            case \"CROSS_PLATFORM\":\n                files[\"App.tsx\"] = this.generateProfessionalReactNativeCode(request, architecture);\n                files[\"package.json\"] = this.generateReactNativePackageJson(request);\n                break;\n        }\n        return files;\n    }\n    /**\n   * Generate screens based on features\n   */ generateScreensForFeatures(features) {\n        const screens = [\n            \"HomeScreen\",\n            \"ProfileScreen\"\n        ];\n        if (features.includes(\"authentication\") || features.includes(\"user-authentication\")) {\n            screens.push(\"LoginScreen\", \"RegisterScreen\");\n        }\n        if (features.includes(\"settings\")) {\n            screens.push(\"SettingsScreen\");\n        }\n        if (features.includes(\"notifications\") || features.includes(\"push-notifications\")) {\n            screens.push(\"NotificationsScreen\");\n        }\n        return screens;\n    }\n    /**\n   * Generate components based on features\n   */ generateComponentsForFeatures(features) {\n        const components = [\n            \"Header\",\n            \"Footer\",\n            \"LoadingSpinner\"\n        ];\n        if (features.includes(\"user-authentication\")) {\n            components.push(\"LoginForm\", \"UserAvatar\");\n        }\n        if (features.includes(\"real-time-sync\")) {\n            components.push(\"SyncIndicator\");\n        }\n        return components;\n    }\n    /**\n   * Log learning data for AI improvement\n   */ logLearningData(modelId, request, result) {\n        this.storage.insert(\"ai_learning_data\", {\n            model_id: modelId,\n            interaction_type: \"app-generation\",\n            input_data: {\n                platform: request.platform,\n                features: request.features,\n                description: request.description\n            },\n            output_data: result,\n            processing_time: result.generationTime || 0,\n            success: result.success || false\n        });\n    }\n    /**\n   * Get all generated apps for a user\n   */ getUserApps(userId) {\n        return this.storage.find(\"generated_apps\", (app)=>app.userId === userId);\n    }\n    /**\n   * Get app by ID\n   */ getAppById(appId) {\n        return this.storage.findById(\"generated_apps\", appId);\n    }\n    /**\n   * Get generation statistics\n   */ getGenerationStats() {\n        const apps = this.storage.getAll(\"generated_apps\");\n        const learningData = this.storage.getAll(\"ai_learning_data\");\n        return {\n            totalApps: apps.length,\n            completedApps: apps.filter((app)=>app.status === \"COMPLETED\").length,\n            failedApps: apps.filter((app)=>app.status === \"FAILED\").length,\n            averageGenerationTime: apps.reduce((sum, app)=>sum + (app.metadata.generationTime || 0), 0) / apps.length,\n            totalLinesGenerated: apps.reduce((sum, app)=>sum + (app.metadata.linesOfCode || 0), 0),\n            platformDistribution: {\n                android: apps.filter((app)=>app.platform === \"ANDROID\").length,\n                ios: apps.filter((app)=>app.platform === \"IOS\").length,\n                crossPlatform: apps.filter((app)=>app.platform === \"CROSS_PLATFORM\").length\n            },\n            aiLearningInteractions: learningData.length\n        };\n    }\n    // Professional code generation methods\n    async generateAndroidMainActivity(request, architecture) {\n        try {\n            const prompt = `Generate a complete MainActivity.kt for ${request.name} with features: ${request.features.join(\", \")}`;\n            return await this.ollamaService.generateCode(prompt);\n        } catch (error) {\n            return this.generateProfessionalAndroidCode(request, architecture);\n        }\n    }\n    generateProfessionalAndroidCode(request, architecture) {\n        const packageName = `com.androidweb.${request.name.toLowerCase().replace(/\\s+/g, \"\")}`;\n        return `package ${packageName}\n\nimport android.os.Bundle\nimport androidx.appcompat.app.AppCompatActivity\nimport androidx.navigation.findNavController\nimport androidx.navigation.ui.AppBarConfiguration\nimport androidx.navigation.ui.setupActionBarWithNavController\nimport androidx.navigation.ui.setupWithNavController\nimport com.google.android.material.bottomnavigation.BottomNavigationView\nimport androidx.lifecycle.ViewModelProvider\nimport androidx.room.Room\n\n/**\n * ${request.name} - Professional Android Application\n * Generated by AndroidWeb Enterprise Platform\n *\n * Features: ${request.features.join(\", \")}\n * Architecture: ${architecture.architecture}\n * Database: ${architecture.database}\n */\nclass MainActivity : AppCompatActivity() {\n\n    private lateinit var appBarConfiguration: AppBarConfiguration\n    private lateinit var viewModel: MainViewModel\n\n    override fun onCreate(savedInstanceState: Bundle?) {\n        super.onCreate(savedInstanceState)\n        setContentView(R.layout.activity_main)\n\n        // Initialize ViewModel\n        viewModel = ViewModelProvider(this)[MainViewModel::class.java]\n\n        // Setup Navigation\n        setupNavigation()\n\n        // Initialize Services\n        initializeServices()\n\n        // Setup Authentication if required\n        ${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? \"setupAuthentication()\" : \"// No authentication required\"}\n\n        // Setup Push Notifications if required\n        ${request.features.includes(\"notifications\") || request.features.includes(\"push-notifications\") ? \"setupPushNotifications()\" : \"// No push notifications required\"}\n    }\n\n    private fun setupNavigation() {\n        val navView: BottomNavigationView = findViewById(R.id.nav_view)\n        val navController = findNavController(R.id.nav_host_fragment)\n\n        appBarConfiguration = AppBarConfiguration(\n            setOf(${architecture.screens.slice(0, 3).map((screen)=>`R.id.navigation_${screen.toLowerCase().replace(\"screen\", \"\")}`).join(\", \")})\n        )\n\n        setupActionBarWithNavController(navController, appBarConfiguration)\n        navView.setupWithNavController(navController)\n    }\n\n    private fun initializeServices() {\n        // Initialize database\n        val database = Room.databaseBuilder(\n            applicationContext,\n            AppDatabase::class.java,\n            \"${request.name.toLowerCase()}_database\"\n        ).build()\n\n        // Initialize repository\n        val repository = AppRepository(database.appDao())\n\n        // Initialize network service\n        val networkService = NetworkService.getInstance()\n\n        // Setup real-time sync if required\n        ${request.features.includes(\"real-time-sync\") ? \"setupRealTimeSync(repository, networkService)\" : \"// No real-time sync required\"}\n    }\n\n    ${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? `\n    private fun setupAuthentication() {\n        // Initialize Firebase Auth or custom auth\n        val authService = AuthenticationService.getInstance()\n\n        // Check if user is logged in\n        if (!authService.isUserLoggedIn()) {\n            // Navigate to login screen\n            startActivity(Intent(this, LoginActivity::class.java))\n            finish()\n        }\n    }` : \"\"}\n\n    ${request.features.includes(\"notifications\") || request.features.includes(\"push-notifications\") ? `\n    private fun setupPushNotifications() {\n        // Initialize FCM or custom notification service\n        val notificationService = NotificationService.getInstance()\n        notificationService.initialize(this)\n\n        // Request notification permissions (Android 13+)\n        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {\n            requestPermissions(arrayOf(Manifest.permission.POST_NOTIFICATIONS), 1001)\n        }\n    }` : \"\"}\n\n    ${request.features.includes(\"real-time-sync\") ? `\n    private fun setupRealTimeSync(repository: AppRepository, networkService: NetworkService) {\n        // Setup WebSocket or Firebase Realtime Database\n        val syncService = SyncService(repository, networkService)\n        syncService.startSync()\n\n        // Observe connectivity changes\n        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager\n        val networkCallback = object : ConnectivityManager.NetworkCallback() {\n            override fun onAvailable(network: Network) {\n                syncService.resumeSync()\n            }\n\n            override fun onLost(network: Network) {\n                syncService.pauseSync()\n            }\n        }\n\n        connectivityManager.registerDefaultNetworkCallback(networkCallback)\n    }` : \"\"}\n\n    override fun onSupportNavigateUp(): Boolean {\n        val navController = findNavController(R.id.nav_host_fragment)\n        return navController.navigateUp(appBarConfiguration) || super.onSupportNavigateUp()\n    }\n\n    override fun onDestroy() {\n        super.onDestroy()\n        // Cleanup resources\n        viewModel.cleanup()\n    }\n}\n\n/**\n * Main ViewModel for ${request.name}\n */\nclass MainViewModel : ViewModel() {\n    private val _isLoading = MutableLiveData<Boolean>()\n    val isLoading: LiveData<Boolean> = _isLoading\n\n    private val _errorMessage = MutableLiveData<String>()\n    val errorMessage: LiveData<String> = _errorMessage\n\n    fun cleanup() {\n        // Cleanup resources\n    }\n}`;\n    }\n    generateAndroidBuildGradle(request) {\n        return `plugins {\n    id 'com.android.application'\n    id 'org.jetbrains.kotlin.android'\n    ${request.features.includes(\"notifications\") || request.features.includes(\"push-notifications\") ? \"id 'com.google.gms.google-services'\" : \"\"}\n    id 'kotlin-kapt'\n}\n\nandroid {\n    namespace 'com.androidweb.${request.name.toLowerCase().replace(/\\s+/g, \"\")}'\n    compileSdk 34\n\n    defaultConfig {\n        applicationId \"com.androidweb.${request.name.toLowerCase().replace(/\\s+/g, \"\")}\"\n        minSdk 24\n        targetSdk 34\n        versionCode 1\n        versionName \"1.0\"\n\n        testInstrumentationRunner \"androidx.test.runner.AndroidJUnitRunner\"\n    }\n\n    buildTypes {\n        release {\n            minifyEnabled false\n            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'\n        }\n    }\n\n    compileOptions {\n        sourceCompatibility JavaVersion.VERSION_1_8\n        targetCompatibility JavaVersion.VERSION_1_8\n    }\n\n    kotlinOptions {\n        jvmTarget = '1.8'\n    }\n\n    buildFeatures {\n        viewBinding true\n        dataBinding true\n    }\n}\n\ndependencies {\n    implementation 'androidx.core:core-ktx:1.12.0'\n    implementation 'androidx.appcompat:appcompat:1.6.1'\n    implementation 'com.google.android.material:material:1.11.0'\n    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'\n\n    // Navigation\n    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.6'\n    implementation 'androidx.navigation:navigation-ui-ktx:2.7.6'\n\n    // ViewModel and LiveData\n    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'\n    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'\n\n    ${request.features.includes(\"database\") || request.features.includes(\"offline-mode\") ? `\n    // Room Database\n    implementation 'androidx.room:room-runtime:2.6.1'\n    implementation 'androidx.room:room-ktx:2.6.1'\n    kapt 'androidx.room:room-compiler:2.6.1'` : \"\"}\n\n    ${request.features.includes(\"networking\") || request.features.includes(\"api\") ? `\n    // Networking\n    implementation 'com.squareup.retrofit2:retrofit:2.9.0'\n    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'\n    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'` : \"\"}\n\n    ${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? `\n    // Authentication\n    implementation 'androidx.biometric:biometric:1.1.0'` : \"\"}\n\n    ${request.features.includes(\"notifications\") || request.features.includes(\"push-notifications\") ? `\n    // Firebase\n    implementation platform('com.google.firebase:firebase-bom:32.7.0')\n    implementation 'com.google.firebase:firebase-messaging-ktx'\n    implementation 'com.google.firebase:firebase-analytics-ktx'` : \"\"}\n\n    ${request.features.includes(\"real-time-sync\") ? `\n    // WebSocket\n    implementation 'org.java-websocket:Java-WebSocket:1.5.3'` : \"\"}\n\n    // Testing\n    testImplementation 'junit:junit:4.13.2'\n    androidTestImplementation 'androidx.test.ext:junit:1.1.5'\n    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'\n}`;\n    }\n    generateAndroidManifest(request) {\n        return `<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<manifest xmlns:android=\"http://schemas.android.com/apk/res/android\"\n    xmlns:tools=\"http://schemas.android.com/tools\">\n\n    <!-- Network permissions -->\n    <uses-permission android:name=\"android.permission.INTERNET\" />\n    <uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />\n\n    ${request.features.includes(\"notifications\") || request.features.includes(\"push-notifications\") ? `\n    <!-- Notification permissions -->\n    <uses-permission android:name=\"android.permission.POST_NOTIFICATIONS\" />\n    <uses-permission android:name=\"android.permission.WAKE_LOCK\" />` : \"\"}\n\n    ${request.features.includes(\"file-upload\") || request.features.includes(\"camera\") ? `\n    <!-- Storage and camera permissions -->\n    <uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />\n    <uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />\n    <uses-permission android:name=\"android.permission.CAMERA\" />` : \"\"}\n\n    ${request.features.includes(\"location\") ? `\n    <!-- Location permissions -->\n    <uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\" />\n    <uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\" />` : \"\"}\n\n    <application\n        android:allowBackup=\"true\"\n        android:dataExtractionRules=\"@xml/data_extraction_rules\"\n        android:fullBackupContent=\"@xml/backup_rules\"\n        android:icon=\"@mipmap/ic_launcher\"\n        android:label=\"@string/app_name\"\n        android:roundIcon=\"@mipmap/ic_launcher_round\"\n        android:supportsRtl=\"true\"\n        android:theme=\"@style/Theme.${request.name.replace(/\\s+/g, \"\")}\"\n        tools:targetApi=\"31\">\n\n        <activity\n            android:name=\".MainActivity\"\n            android:exported=\"true\"\n            android:label=\"@string/app_name\"\n            android:theme=\"@style/Theme.${request.name.replace(/\\s+/g, \"\")}.NoActionBar\">\n            <intent-filter>\n                <action android:name=\"android.intent.action.MAIN\" />\n                <category android:name=\"android.intent.category.LAUNCHER\" />\n            </intent-filter>\n        </activity>\n\n        ${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? `\n        <activity\n            android:name=\".auth.LoginActivity\"\n            android:exported=\"false\"\n            android:theme=\"@style/Theme.${request.name.replace(/\\s+/g, \"\")}.NoActionBar\" />\n\n        <activity\n            android:name=\".auth.RegisterActivity\"\n            android:exported=\"false\"\n            android:theme=\"@style/Theme.${request.name.replace(/\\s+/g, \"\")}.NoActionBar\" />` : \"\"}\n\n        ${request.features.includes(\"notifications\") || request.features.includes(\"push-notifications\") ? `\n        <service\n            android:name=\".services.NotificationService\"\n            android:exported=\"false\">\n            <intent-filter>\n                <action android:name=\"com.google.firebase.MESSAGING_EVENT\" />\n            </intent-filter>\n        </service>` : \"\"}\n\n        ${request.features.includes(\"real-time-sync\") ? `\n        <service\n            android:name=\".services.SyncService\"\n            android:exported=\"false\" />` : \"\"}\n\n    </application>\n\n</manifest>`;\n    }\n    generateAndroidStrings(request) {\n        return `<resources>\n    <string name=\"app_name\">${request.name}</string>\n    <string name=\"app_description\">${request.description}</string>\n\n    <!-- Navigation -->\n    <string name=\"title_home\">Home</string>\n    <string name=\"title_dashboard\">Dashboard</string>\n    <string name=\"title_profile\">Profile</string>\n    <string name=\"title_settings\">Settings</string>\n\n    ${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? `\n    <!-- Authentication -->\n    <string name=\"login_title\">Login</string>\n    <string name=\"register_title\">Register</string>\n    <string name=\"email_hint\">Email</string>\n    <string name=\"password_hint\">Password</string>\n    <string name=\"login_button\">Login</string>\n    <string name=\"register_button\">Register</string>\n    <string name=\"forgot_password\">Forgot Password?</string>` : \"\"}\n\n    ${request.features.includes(\"notifications\") || request.features.includes(\"push-notifications\") ? `\n    <!-- Notifications -->\n    <string name=\"notification_title\">New Notification</string>\n    <string name=\"notification_channel_name\">App Notifications</string>\n    <string name=\"notification_channel_description\">Notifications from ${request.name}</string>` : \"\"}\n\n    <!-- Common -->\n    <string name=\"loading\">Loading...</string>\n    <string name=\"error_generic\">Something went wrong. Please try again.</string>\n    <string name=\"error_network\">Network error. Please check your connection.</string>\n    <string name=\"retry\">Retry</string>\n    <string name=\"cancel\">Cancel</string>\n    <string name=\"ok\">OK</string>\n    <string name=\"save\">Save</string>\n    <string name=\"delete\">Delete</string>\n    <string name=\"edit\">Edit</string>\n\n</resources>`;\n    }\n    generateAndroidLayout(request) {\n        return `<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<androidx.constraintlayout.widget.ConstraintLayout xmlns:android=\"http://schemas.android.com/apk/res/android\"\n    xmlns:app=\"http://schemas.android.com/apk/res-auto\"\n    android:id=\"@+id/container\"\n    android:layout_width=\"match_parent\"\n    android:layout_height=\"match_parent\">\n\n    <com.google.android.material.bottomnavigation.BottomNavigationView\n        android:id=\"@+id/nav_view\"\n        android:layout_width=\"0dp\"\n        android:layout_height=\"wrap_content\"\n        android:layout_marginStart=\"0dp\"\n        android:layout_marginEnd=\"0dp\"\n        android:background=\"?android:attr/windowBackground\"\n        app:layout_constraintBottom_toBottomOf=\"parent\"\n        app:layout_constraintLeft_toLeftOf=\"parent\"\n        app:layout_constraintRight_toRightOf=\"parent\"\n        app:menu=\"@menu/bottom_nav_menu\" />\n\n    <fragment\n        android:id=\"@+id/nav_host_fragment\"\n        android:name=\"androidx.navigation.fragment.NavHostFragment\"\n        android:layout_width=\"match_parent\"\n        android:layout_height=\"0dp\"\n        app:defaultNavHost=\"true\"\n        app:layout_constraintBottom_toTopOf=\"@id/nav_view\"\n        app:layout_constraintLeft_toLeftOf=\"parent\"\n        app:layout_constraintRight_toRightOf=\"parent\"\n        app:layout_constraintTop_toTopOf=\"parent\"\n        app:navGraph=\"@navigation/mobile_navigation\" />\n\n</androidx.constraintlayout.widget.ConstraintLayout>`;\n    }\n    // iOS Code Generation Methods\n    async generateIOSContentView(request, architecture) {\n        try {\n            const prompt = `Generate a SwiftUI ContentView for ${request.name} with features: ${request.features.join(\", \")}`;\n            return await this.ollamaService.generateCode(prompt);\n        } catch (error) {\n            return this.generateProfessionalIOSCode(request, architecture);\n        }\n    }\n    generateProfessionalIOSCode(request, architecture) {\n        return `import SwiftUI\nimport Combine\n\n/**\n * ${request.name} - Professional iOS Application\n * Generated by AndroidWeb Enterprise Platform\n *\n * Features: ${request.features.join(\", \")}\n * Architecture: ${architecture.architecture}\n * Database: ${architecture.database}\n */\n\nstruct ContentView: View {\n    @StateObject private var viewModel = MainViewModel()\n    @State private var selectedTab = 0\n\n    var body: some View {\n        TabView(selection: $selectedTab) {\n            HomeView()\n                .tabItem {\n                    Image(systemName: \"house\")\n                    Text(\"Home\")\n                }\n                .tag(0)\n\n            DashboardView()\n                .tabItem {\n                    Image(systemName: \"chart.bar\")\n                    Text(\"Dashboard\")\n                }\n                .tag(1)\n\n            ProfileView()\n                .tabItem {\n                    Image(systemName: \"person\")\n                    Text(\"Profile\")\n                }\n                .tag(2)\n\n            ${request.features.includes(\"settings\") ? `\n            SettingsView()\n                .tabItem {\n                    Image(systemName: \"gear\")\n                    Text(\"Settings\")\n                }\n                .tag(3)` : \"\"}\n        }\n        .onAppear {\n            viewModel.initialize()\n        }\n        ${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? `\n        .sheet(isPresented: $viewModel.showLogin) {\n            LoginView()\n        }` : \"\"}\n    }\n}\n\n// MARK: - Main ViewModel\nclass MainViewModel: ObservableObject {\n    @Published var isLoading = false\n    @Published var errorMessage: String?\n    @Published var showLogin = false\n\n    private var cancellables = Set<AnyCancellable>()\n\n    func initialize() {\n        ${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? `\n        // Check authentication status\n        if !AuthenticationService.shared.isLoggedIn {\n            showLogin = true\n        }` : \"\"}\n\n        ${request.features.includes(\"notifications\") || request.features.includes(\"push-notifications\") ? `\n        // Setup push notifications\n        NotificationService.shared.requestPermission()` : \"\"}\n\n        ${request.features.includes(\"real-time-sync\") ? `\n        // Start real-time sync\n        SyncService.shared.startSync()` : \"\"}\n    }\n}\n\n// MARK: - Views\nstruct HomeView: View {\n    var body: some View {\n        NavigationView {\n            VStack {\n                Text(\"Welcome to ${request.name}\")\n                    .font(.largeTitle)\n                    .padding()\n\n                Text(\"${request.description}\")\n                    .font(.body)\n                    .multilineTextAlignment(.center)\n                    .padding()\n\n                Spacer()\n            }\n            .navigationTitle(\"Home\")\n        }\n    }\n}\n\nstruct DashboardView: View {\n    var body: some View {\n        NavigationView {\n            VStack {\n                Text(\"Dashboard\")\n                    .font(.largeTitle)\n                    .padding()\n\n                // Add dashboard content here\n\n                Spacer()\n            }\n            .navigationTitle(\"Dashboard\")\n        }\n    }\n}\n\nstruct ProfileView: View {\n    var body: some View {\n        NavigationView {\n            VStack {\n                Text(\"Profile\")\n                    .font(.largeTitle)\n                    .padding()\n\n                // Add profile content here\n\n                Spacer()\n            }\n            .navigationTitle(\"Profile\")\n        }\n    }\n}\n\n${request.features.includes(\"settings\") ? `\nstruct SettingsView: View {\n    var body: some View {\n        NavigationView {\n            List {\n                Section(\"General\") {\n                    NavigationLink(\"Account\", destination: Text(\"Account Settings\"))\n                    NavigationLink(\"Privacy\", destination: Text(\"Privacy Settings\"))\n                    NavigationLink(\"Notifications\", destination: Text(\"Notification Settings\"))\n                }\n\n                Section(\"About\") {\n                    HStack {\n                        Text(\"Version\")\n                        Spacer()\n                        Text(\"1.0.0\")\n                            .foregroundColor(.secondary)\n                    }\n                }\n            }\n            .navigationTitle(\"Settings\")\n        }\n    }\n}` : \"\"}\n\n${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? `\nstruct LoginView: View {\n    @State private var email = \"\"\n    @State private var password = \"\"\n    @Environment(\\\\.dismiss) private var dismiss\n\n    var body: some View {\n        NavigationView {\n            VStack(spacing: 20) {\n                Text(\"Login to ${request.name}\")\n                    .font(.largeTitle)\n                    .padding()\n\n                TextField(\"Email\", text: $email)\n                    .textFieldStyle(RoundedBorderTextFieldStyle())\n                    .keyboardType(.emailAddress)\n                    .autocapitalization(.none)\n\n                SecureField(\"Password\", text: $password)\n                    .textFieldStyle(RoundedBorderTextFieldStyle())\n\n                Button(\"Login\") {\n                    // Handle login\n                    dismiss()\n                }\n                .buttonStyle(.borderedProminent)\n                .disabled(email.isEmpty || password.isEmpty)\n\n                Spacer()\n            }\n            .padding()\n            .navigationTitle(\"Login\")\n            .navigationBarTitleDisplayMode(.inline)\n            .toolbar {\n                ToolbarItem(placement: .navigationBarTrailing) {\n                    Button(\"Cancel\") {\n                        dismiss()\n                    }\n                }\n            }\n        }\n    }\n}` : \"\"}\n\n#Preview {\n    ContentView()\n}`;\n    }\n    generateIOSApp(request) {\n        return `import SwiftUI\n\n@main\nstruct ${request.name.replace(/\\s+/g, \"\")}App: App {\n    var body: some Scene {\n        WindowGroup {\n            ContentView()\n        }\n    }\n}`;\n    }\n    generateIOSInfoPlist(request) {\n        return `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">\n<plist version=\"1.0\">\n<dict>\n    <key>CFBundleDevelopmentRegion</key>\n    <string>$(DEVELOPMENT_LANGUAGE)</string>\n    <key>CFBundleDisplayName</key>\n    <string>${request.name}</string>\n    <key>CFBundleExecutable</key>\n    <string>$(EXECUTABLE_NAME)</string>\n    <key>CFBundleIdentifier</key>\n    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>\n    <key>CFBundleInfoDictionaryVersion</key>\n    <string>6.0</string>\n    <key>CFBundleName</key>\n    <string>$(PRODUCT_NAME)</string>\n    <key>CFBundlePackageType</key>\n    <string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>\n    <key>CFBundleShortVersionString</key>\n    <string>1.0</string>\n    <key>CFBundleVersion</key>\n    <string>1</string>\n    <key>LSRequiresIPhoneOS</key>\n    <true/>\n    <key>UIApplicationSceneManifest</key>\n    <dict>\n        <key>UIApplicationSupportsMultipleScenes</key>\n        <true/>\n    </dict>\n    <key>UIApplicationSupportsIndirectInputEvents</key>\n    <true/>\n    <key>UILaunchScreen</key>\n    <dict/>\n    <key>UIRequiredDeviceCapabilities</key>\n    <array>\n        <string>armv7</string>\n    </array>\n    <key>UISupportedInterfaceOrientations</key>\n    <array>\n        <string>UIInterfaceOrientationPortrait</string>\n        <string>UIInterfaceOrientationLandscapeLeft</string>\n        <string>UIInterfaceOrientationLandscapeRight</string>\n    </array>\n    <key>UISupportedInterfaceOrientations~ipad</key>\n    <array>\n        <string>UIInterfaceOrientationPortrait</string>\n        <string>UIInterfaceOrientationPortraitUpsideDown</string>\n        <string>UIInterfaceOrientationLandscapeLeft</string>\n        <string>UIInterfaceOrientationLandscapeRight</string>\n    </array>\n    ${request.features.includes(\"camera\") || request.features.includes(\"file-upload\") ? `\n    <key>NSCameraUsageDescription</key>\n    <string>${request.name} needs camera access to capture photos</string>\n    <key>NSPhotoLibraryUsageDescription</key>\n    <string>${request.name} needs photo library access to select images</string>` : \"\"}\n    ${request.features.includes(\"location\") ? `\n    <key>NSLocationWhenInUseUsageDescription</key>\n    <string>${request.name} needs location access to provide location-based features</string>` : \"\"}\n    ${request.features.includes(\"notifications\") || request.features.includes(\"push-notifications\") ? `\n    <key>UIBackgroundModes</key>\n    <array>\n        <string>remote-notification</string>\n    </array>` : \"\"}\n</dict>\n</plist>`;\n    }\n    // React Native Code Generation Methods\n    async generateReactNativeApp(request, architecture) {\n        try {\n            const prompt = `Generate a React Native App.tsx for ${request.name} with features: ${request.features.join(\", \")}`;\n            return await this.ollamaService.generateCode(prompt);\n        } catch (error) {\n            return this.generateProfessionalReactNativeCode(request, architecture);\n        }\n    }\n    generateProfessionalReactNativeCode(request, architecture) {\n        return `import React, { useEffect, useState } from 'react';\nimport {\n  SafeAreaView,\n  ScrollView,\n  StatusBar,\n  StyleSheet,\n  Text,\n  View,\n  Alert,\n} from 'react-native';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { createStackNavigator } from '@react-navigation/stack';\nimport Icon from 'react-native-vector-icons/MaterialIcons';\n\n/**\n * ${request.name} - Professional React Native Application\n * Generated by AndroidWeb Enterprise Platform\n *\n * Features: ${request.features.join(\", \")}\n * Architecture: ${architecture.architecture}\n * Database: ${architecture.database}\n */\n\nconst Tab = createBottomTabNavigator();\nconst Stack = createStackNavigator();\n\n// Main App Component\nconst App = (): JSX.Element => {\n  const [isLoading, setIsLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  useEffect(() => {\n    initializeApp();\n  }, []);\n\n  const initializeApp = async () => {\n    try {\n      ${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? `\n      // Check authentication status\n      const authStatus = await checkAuthenticationStatus();\n      setIsAuthenticated(authStatus);` : \"\"}\n\n      ${request.features.includes(\"notifications\") || request.features.includes(\"push-notifications\") ? `\n      // Setup push notifications\n      await setupPushNotifications();` : \"\"}\n\n      ${request.features.includes(\"real-time-sync\") ? `\n      // Initialize real-time sync\n      await initializeRealTimeSync();` : \"\"}\n\n      setIsLoading(false);\n    } catch (error) {\n      console.error('App initialization failed:', error);\n      Alert.alert('Error', 'Failed to initialize app');\n      setIsLoading(false);\n    }\n  };\n\n  ${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? `\n  const checkAuthenticationStatus = async (): Promise<boolean> => {\n    // Implement authentication check\n    return false; // Replace with actual auth check\n  };` : \"\"}\n\n  ${request.features.includes(\"notifications\") || request.features.includes(\"push-notifications\") ? `\n  const setupPushNotifications = async () => {\n    // Implement push notification setup\n    console.log('Setting up push notifications...');\n  };` : \"\"}\n\n  ${request.features.includes(\"real-time-sync\") ? `\n  const initializeRealTimeSync = async () => {\n    // Implement real-time sync initialization\n    console.log('Initializing real-time sync...');\n  };` : \"\"}\n\n  if (isLoading) {\n    return (\n      <SafeAreaView style={styles.loadingContainer}>\n        <Text style={styles.loadingText}>Loading ${request.name}...</Text>\n      </SafeAreaView>\n    );\n  }\n\n  ${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? `\n  if (!isAuthenticated) {\n    return (\n      <NavigationContainer>\n        <Stack.Navigator>\n          <Stack.Screen name=\"Login\" component={LoginScreen} />\n          <Stack.Screen name=\"Register\" component={RegisterScreen} />\n        </Stack.Navigator>\n      </NavigationContainer>\n    );\n  }` : \"\"}\n\n  return (\n    <NavigationContainer>\n      <StatusBar barStyle=\"dark-content\" />\n      <Tab.Navigator\n        screenOptions={({ route }) => ({\n          tabBarIcon: ({ focused, color, size }) => {\n            let iconName = 'home';\n\n            switch (route.name) {\n              case 'Home':\n                iconName = 'home';\n                break;\n              case 'Dashboard':\n                iconName = 'dashboard';\n                break;\n              case 'Profile':\n                iconName = 'person';\n                break;\n              case 'Settings':\n                iconName = 'settings';\n                break;\n            }\n\n            return <Icon name={iconName} size={size} color={color} />;\n          },\n          tabBarActiveTintColor: '#007AFF',\n          tabBarInactiveTintColor: 'gray',\n        })}>\n        <Tab.Screen name=\"Home\" component={HomeScreen} />\n        <Tab.Screen name=\"Dashboard\" component={DashboardScreen} />\n        <Tab.Screen name=\"Profile\" component={ProfileScreen} />\n        ${request.features.includes(\"settings\") ? `\n        <Tab.Screen name=\"Settings\" component={SettingsScreen} />` : \"\"}\n      </Tab.Navigator>\n    </NavigationContainer>\n  );\n};\n\n// Screen Components\nconst HomeScreen = (): JSX.Element => {\n  return (\n    <SafeAreaView style={styles.container}>\n      <ScrollView contentInsetAdjustmentBehavior=\"automatic\">\n        <View style={styles.section}>\n          <Text style={styles.title}>Welcome to ${request.name}</Text>\n          <Text style={styles.description}>${request.description}</Text>\n        </View>\n      </ScrollView>\n    </SafeAreaView>\n  );\n};\n\nconst DashboardScreen = (): JSX.Element => {\n  return (\n    <SafeAreaView style={styles.container}>\n      <View style={styles.section}>\n        <Text style={styles.title}>Dashboard</Text>\n        <Text style={styles.description}>Your dashboard content goes here</Text>\n      </View>\n    </SafeAreaView>\n  );\n};\n\nconst ProfileScreen = (): JSX.Element => {\n  return (\n    <SafeAreaView style={styles.container}>\n      <View style={styles.section}>\n        <Text style={styles.title}>Profile</Text>\n        <Text style={styles.description}>Your profile information</Text>\n      </View>\n    </SafeAreaView>\n  );\n};\n\n${request.features.includes(\"settings\") ? `\nconst SettingsScreen = (): JSX.Element => {\n  return (\n    <SafeAreaView style={styles.container}>\n      <View style={styles.section}>\n        <Text style={styles.title}>Settings</Text>\n        <Text style={styles.description}>App settings and preferences</Text>\n      </View>\n    </SafeAreaView>\n  );\n};` : \"\"}\n\n${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? `\nconst LoginScreen = (): JSX.Element => {\n  return (\n    <SafeAreaView style={styles.container}>\n      <View style={styles.section}>\n        <Text style={styles.title}>Login</Text>\n        <Text style={styles.description}>Please login to continue</Text>\n      </View>\n    </SafeAreaView>\n  );\n};\n\nconst RegisterScreen = (): JSX.Element => {\n  return (\n    <SafeAreaView style={styles.container}>\n      <View style={styles.section}>\n        <Text style={styles.title}>Register</Text>\n        <Text style={styles.description}>Create a new account</Text>\n      </View>\n    </SafeAreaView>\n  );\n};` : \"\"}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#fff',\n  },\n  loadingContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    backgroundColor: '#fff',\n  },\n  loadingText: {\n    fontSize: 18,\n    color: '#333',\n  },\n  section: {\n    padding: 20,\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 10,\n  },\n  description: {\n    fontSize: 16,\n    color: '#666',\n    lineHeight: 24,\n  },\n});\n\nexport default App;`;\n    }\n    generateReactNativePackageJson(request) {\n        return `{\n  \"name\": \"${request.name.toLowerCase().replace(/\\s+/g, \"-\")}\",\n  \"version\": \"1.0.0\",\n  \"description\": \"${request.description}\",\n  \"main\": \"index.js\",\n  \"scripts\": {\n    \"android\": \"react-native run-android\",\n    \"ios\": \"react-native run-ios\",\n    \"start\": \"react-native start\",\n    \"test\": \"jest\",\n    \"lint\": \"eslint . --ext .js,.jsx,.ts,.tsx\"\n  },\n  \"dependencies\": {\n    \"react\": \"18.2.0\",\n    \"react-native\": \"0.73.2\",\n    \"@react-navigation/native\": \"^6.1.9\",\n    \"@react-navigation/bottom-tabs\": \"^6.5.11\",\n    \"@react-navigation/stack\": \"^6.3.20\",\n    \"react-native-screens\": \"^3.29.0\",\n    \"react-native-safe-area-context\": \"^4.8.2\",\n    \"react-native-vector-icons\": \"^10.0.3\",\n    ${request.features.includes(\"authentication\") || request.features.includes(\"user-authentication\") ? `\n    \"@react-native-async-storage/async-storage\": \"^1.21.0\",\n    \"react-native-keychain\": \"^8.1.3\",` : \"\"}\n    ${request.features.includes(\"networking\") || request.features.includes(\"api\") ? `\n    \"axios\": \"^1.6.5\",` : \"\"}\n    ${request.features.includes(\"notifications\") || request.features.includes(\"push-notifications\") ? `\n    \"@react-native-firebase/app\": \"^19.0.1\",\n    \"@react-native-firebase/messaging\": \"^19.0.1\",` : \"\"}\n    ${request.features.includes(\"real-time-sync\") ? `\n    \"socket.io-client\": \"^4.7.4\",` : \"\"}\n    ${request.features.includes(\"database\") || request.features.includes(\"offline-mode\") ? `\n    \"react-native-sqlite-storage\": \"^6.0.1\",` : \"\"}\n    ${request.features.includes(\"camera\") || request.features.includes(\"file-upload\") ? `\n    \"react-native-image-picker\": \"^7.1.0\",` : \"\"}\n    ${request.features.includes(\"location\") ? `\n    \"@react-native-community/geolocation\": \"^3.2.1\",` : \"\"}\n    \"react-native-gesture-handler\": \"^2.14.1\"\n  },\n  \"devDependencies\": {\n    \"@babel/core\": \"^7.20.0\",\n    \"@babel/preset-env\": \"^7.20.0\",\n    \"@babel/runtime\": \"^7.20.0\",\n    \"@react-native/eslint-config\": \"^0.73.1\",\n    \"@react-native/metro-config\": \"^0.73.2\",\n    \"@react-native/typescript-config\": \"^0.73.1\",\n    \"@types/react\": \"^18.2.6\",\n    \"@types/react-test-renderer\": \"^18.0.0\",\n    \"babel-jest\": \"^29.6.3\",\n    \"eslint\": \"^8.19.0\",\n    \"jest\": \"^29.6.3\",\n    \"metro-react-native-babel-preset\": \"0.76.8\",\n    \"prettier\": \"2.8.8\",\n    \"react-test-renderer\": \"18.2.0\",\n    \"typescript\": \"5.0.4\"\n  },\n  \"engines\": {\n    \"node\": \">=18\"\n  }\n}`;\n    }\n    generateReactNativeMetroConfig() {\n        return `const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');\n\n/**\n * Metro configuration\n * https://facebook.github.io/metro/docs/configuration\n */\nconst config = {};\n\nmodule.exports = mergeConfig(getDefaultConfig(__dirname), config);`;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/app-generation-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/json-storage.ts":
/*!*********************************!*\
  !*** ./src/lib/json-storage.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JsonStorage: () => (/* binding */ JsonStorage)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\n * A simple JSON-based storage system for the AndroidWeb Enterprise platform.\n * This is a real implementation that stores data in JSON files.\n */ class JsonStorage {\n    constructor(){\n        this.initialized = false;\n        this.dataDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"data\");\n        this.collections = new Map();\n        this.ensureDataDirectory();\n        this.initialize();\n    }\n    /**\n   * Ensure the data directory exists\n   */ ensureDataDirectory() {\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(this.dataDir)) {\n            fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(this.dataDir, {\n                recursive: true\n            });\n        }\n    }\n    /**\n   * Initialize the storage by loading all existing collections\n   */ initialize() {\n        if (this.initialized) return;\n        try {\n            // Create default collections if they don't exist\n            const defaultCollections = [\n                \"users\",\n                \"ai_models\",\n                \"generated_apps\",\n                \"ai_learning_data\",\n                \"app_generation_sessions\",\n                \"ai_evolution_metrics\",\n                \"user_feedback\"\n            ];\n            for (const collection of defaultCollections){\n                const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(this.dataDir, `${collection}.json`);\n                if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(filePath)) {\n                    fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(filePath, JSON.stringify([], null, 2));\n                }\n                const data = JSON.parse(fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(filePath, \"utf8\"));\n                this.collections.set(collection, data);\n            }\n            this.initialized = true;\n        } catch (error) {\n            console.error(\"Failed to initialize storage:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Save a collection to disk\n   */ saveCollection(collectionName) {\n        const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(this.dataDir, `${collectionName}.json`);\n        const data = this.collections.get(collectionName) || [];\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(filePath, JSON.stringify(data, null, 2));\n    }\n    /**\n   * Get all items from a collection\n   */ getAll(collectionName) {\n        return [\n            ...this.collections.get(collectionName) || []\n        ];\n    }\n    /**\n   * Find items in a collection by a filter function\n   */ find(collectionName, filterFn) {\n        const collection = this.collections.get(collectionName) || [];\n        return collection.filter(filterFn);\n    }\n    /**\n   * Find a single item in a collection by a filter function\n   */ findOne(collectionName, filterFn) {\n        const collection = this.collections.get(collectionName) || [];\n        return collection.find(filterFn) || null;\n    }\n    /**\n   * Find an item by ID\n   */ findById(collectionName, id) {\n        return this.findOne(collectionName, (item)=>item.id === id);\n    }\n    /**\n   * Insert an item into a collection\n   */ insert(collectionName, item) {\n        if (!item.id) {\n            item.id = this.generateId();\n        }\n        if (!item.createdAt) {\n            item.createdAt = new Date().toISOString();\n        }\n        if (!item.updatedAt) {\n            item.updatedAt = new Date().toISOString();\n        }\n        const collection = this.collections.get(collectionName) || [];\n        collection.push(item);\n        this.collections.set(collectionName, collection);\n        this.saveCollection(collectionName);\n        return {\n            ...item\n        };\n    }\n    /**\n   * Update an item in a collection\n   */ update(collectionName, id, updates) {\n        const collection = this.collections.get(collectionName) || [];\n        const index = collection.findIndex((item)=>item.id === id);\n        if (index === -1) {\n            return null;\n        }\n        const updatedItem = {\n            ...collection[index],\n            ...updates,\n            updatedAt: new Date().toISOString()\n        };\n        collection[index] = updatedItem;\n        this.collections.set(collectionName, collection);\n        this.saveCollection(collectionName);\n        return {\n            ...updatedItem\n        };\n    }\n    /**\n   * Delete an item from a collection\n   */ delete(collectionName, id) {\n        const collection = this.collections.get(collectionName) || [];\n        const index = collection.findIndex((item)=>item.id === id);\n        if (index === -1) {\n            return false;\n        }\n        collection.splice(index, 1);\n        this.collections.set(collectionName, collection);\n        this.saveCollection(collectionName);\n        return true;\n    }\n    /**\n   * Generate a unique ID\n   */ generateId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Seed initial data if collections are empty\n   */ seedInitialData() {\n        // Check if users collection is empty\n        const users = this.getAll(\"users\");\n        if (users.length === 0) {\n            // Add admin user\n            this.insert(\"users\", {\n                email: \"<EMAIL>\",\n                password: \"admin123\",\n                name: \"Admin User\",\n                role: \"ADMIN\",\n                plan: \"ENTERPRISE\"\n            });\n            // Add demo user\n            this.insert(\"users\", {\n                email: \"<EMAIL>\",\n                password: \"password123\",\n                name: \"Demo User\",\n                role: \"USER\",\n                plan: \"FREE\"\n            });\n        }\n        // Check if AI models collection is empty\n        const aiModels = this.getAll(\"ai_models\");\n        if (aiModels.length === 0) {\n            // Add AI models\n            this.insert(\"ai_models\", {\n                name: \"Qwen-2.5-Coder\",\n                type: \"code-generation\",\n                status: \"active\",\n                config: {\n                    model_path: \"qwen2.5-coder:latest\",\n                    temperature: 0.7,\n                    max_tokens: 4096,\n                    specialization: \"mobile-app-development\"\n                },\n                performance_score: 8.7,\n                total_generations: 124,\n                success_rate: 0.92\n            });\n            this.insert(\"ai_models\", {\n                name: \"Qwen-2.5-Chat\",\n                type: \"conversation\",\n                status: \"active\",\n                config: {\n                    model_path: \"qwen2.5:latest\",\n                    temperature: 0.8,\n                    max_tokens: 2048,\n                    specialization: \"user-interaction\"\n                },\n                performance_score: 9.1,\n                total_generations: 256,\n                success_rate: 0.95\n            });\n            this.insert(\"ai_models\", {\n                name: \"Llama-3.2-1B\",\n                type: \"fallback\",\n                status: \"active\",\n                config: {\n                    model_path: \"llama3.2:1b\",\n                    temperature: 0.7,\n                    max_tokens: 1024,\n                    specialization: \"general\"\n                },\n                performance_score: 6.5,\n                total_generations: 42,\n                success_rate: 0.85\n            });\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/json-storage.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ollama-service.ts":
/*!***********************************!*\
  !*** ./src/lib/ollama-service.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaService: () => (/* binding */ OllamaService)\n/* harmony export */ });\nclass OllamaService {\n    constructor(){\n        this.baseUrl = process.env.OLLAMA_BASE_URL || \"http://localhost:11434\";\n        this.model = process.env.OLLAMA_MODEL || \"qwen2.5:7b\";\n    }\n    async checkHealth() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/tags`);\n            if (!response.ok) return false;\n            const data = await response.json();\n            // Check if any model is available\n            if (!data.models || data.models.length === 0) {\n                console.log(\"No models available in Ollama\");\n                return false;\n            }\n            // Try to find preferred model first\n            let availableModel = data.models.find((model)=>model.name.includes(\"qwen2.5\") || model.name.includes(\"qwen\"));\n            // If preferred model not found, use any available model\n            if (!availableModel) {\n                availableModel = data.models[0];\n                this.model = availableModel.name;\n                console.log(`Using available model: ${this.model}`);\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Ollama health check failed:\", error);\n            return false;\n        }\n    }\n    async generateAppArchitecture(request) {\n        const prompt = `You are an expert mobile app architect. Create a detailed architecture for a ${request.platform} mobile application.\n\nApp Details:\n- Name: ${request.name}\n- Description: ${request.description}\n- Platform: ${request.platform}\n- Features: ${request.features.join(\", \")}\n- UI Style: ${request.uiStyle || \"Modern\"}\n- Color Scheme: ${request.colorScheme || \"Blue\"}\n- Target Audience: ${request.targetAudience || \"General\"}\n\nGenerate a comprehensive architecture including:\n1. Main screens and their purposes\n2. Navigation structure\n3. Data flow patterns\n4. Required components\n5. API endpoints needed\n6. Database schema\n7. Authentication strategy\n8. Styling approach\n\nRespond in JSON format with the following structure:\n{\n  \"screens\": [\"screen1\", \"screen2\", ...],\n  \"navigation\": \"navigation pattern description\",\n  \"dataFlow\": \"data flow description\",\n  \"components\": [\"component1\", \"component2\", ...],\n  \"apis\": [\"api1\", \"api2\", ...],\n  \"database\": \"database schema description\",\n  \"authentication\": \"auth strategy description\",\n  \"styling\": \"styling approach description\"\n}`;\n        try {\n            const response = await this.callOllama(prompt);\n            return this.parseArchitectureResponse(response);\n        } catch (error) {\n            console.error(\"Failed to generate architecture:\", error);\n            // Fallback architecture\n            return this.getFallbackArchitecture(request);\n        }\n    }\n    async generateCode(prompt) {\n        try {\n            return await this.callOllama(prompt);\n        } catch (error) {\n            console.error(\"Failed to generate code:\", error);\n            throw new Error(\"Code generation failed\");\n        }\n    }\n    async callOllama(prompt) {\n        const response = await fetch(`${this.baseUrl}/api/generate`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                model: this.model,\n                prompt,\n                stream: false,\n                options: {\n                    temperature: 0.7,\n                    top_p: 0.9,\n                    max_tokens: 4000\n                }\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`Ollama API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.response;\n    }\n    parseArchitectureResponse(response) {\n        try {\n            // Try to extract JSON from the response\n            const jsonMatch = response.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                const parsed = JSON.parse(jsonMatch[0]);\n                return {\n                    screens: Array.isArray(parsed.screens) ? parsed.screens : [],\n                    navigation: parsed.navigation || \"Tab-based navigation\",\n                    dataFlow: parsed.dataFlow || \"Redux/Context pattern\",\n                    components: Array.isArray(parsed.components) ? parsed.components : [],\n                    apis: Array.isArray(parsed.apis) ? parsed.apis : [],\n                    database: parsed.database || \"SQLite local storage\",\n                    authentication: parsed.authentication || \"JWT token-based\",\n                    styling: parsed.styling || \"Component-based styling\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Failed to parse architecture response:\", error);\n        }\n        // Fallback parsing from text\n        return this.parseArchitectureFromText(response);\n    }\n    parseArchitectureFromText(response) {\n        return {\n            screens: this.extractListFromText(response, \"screens?\"),\n            navigation: this.extractSectionFromText(response, \"navigation\") || \"Tab-based navigation\",\n            dataFlow: this.extractSectionFromText(response, \"data flow\") || \"State management pattern\",\n            components: this.extractListFromText(response, \"components?\"),\n            apis: this.extractListFromText(response, \"api|endpoint\"),\n            database: this.extractSectionFromText(response, \"database\") || \"Local storage\",\n            authentication: this.extractSectionFromText(response, \"auth\") || \"Token-based authentication\",\n            styling: this.extractSectionFromText(response, \"styl\") || \"Modern component styling\"\n        };\n    }\n    extractListFromText(text, keyword) {\n        const regex = new RegExp(`${keyword}[^\\\\n]*:?[^\\\\n]*\\\\n([\\\\s\\\\S]*?)(?=\\\\n\\\\n|\\\\n[A-Z]|$)`, \"i\");\n        const match = text.match(regex);\n        if (match) {\n            return match[1].split(\"\\n\").map((line)=>line.replace(/^[-*•]\\s*/, \"\").trim()).filter((line)=>line.length > 0).slice(0, 10) // Limit to 10 items\n            ;\n        }\n        return [];\n    }\n    extractSectionFromText(text, keyword) {\n        const regex = new RegExp(`${keyword}[^\\\\n]*:?[^\\\\n]*\\\\n([^\\\\n]+)`, \"i\");\n        const match = text.match(regex);\n        return match ? match[1].trim() : \"\";\n    }\n    getFallbackArchitecture(request) {\n        const baseScreens = [\n            \"Home\",\n            \"Profile\",\n            \"Settings\"\n        ];\n        const featureScreens = request.features.map((feature)=>feature.charAt(0).toUpperCase() + feature.slice(1).replace(/[^a-zA-Z]/g, \"\"));\n        return {\n            screens: [\n                ...baseScreens,\n                ...featureScreens\n            ],\n            navigation: request.platform === \"IOS\" ? \"UINavigationController with TabBar\" : \"Navigation Drawer with Bottom Navigation\",\n            dataFlow: \"MVVM with Repository pattern\",\n            components: [\n                \"Header\",\n                \"Button\",\n                \"Input\",\n                \"Card\",\n                \"List\",\n                \"Modal\"\n            ],\n            apis: [\n                \"/auth\",\n                \"/user\",\n                \"/data\",\n                \"/upload\"\n            ],\n            database: request.platform === \"IOS\" ? \"Core Data\" : \"Room Database\",\n            authentication: \"OAuth 2.0 with JWT tokens\",\n            styling: request.platform === \"IOS\" ? \"SwiftUI styling\" : \"Material Design 3\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ollama-service.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai%2Fstats%2Froute&page=%2Fapi%2Fai%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fstats%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();