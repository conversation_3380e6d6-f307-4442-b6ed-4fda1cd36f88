"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/apps/cleanup/route";
exports.ids = ["app/api/apps/cleanup/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fapps%2Fcleanup%2Froute&page=%2Fapi%2Fapps%2Fcleanup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapps%2Fcleanup%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fapps%2Fcleanup%2Froute&page=%2Fapi%2Fapps%2Fcleanup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapps%2Fcleanup%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_claudiu_Desktop_proiecte_androidweb_src_app_api_apps_cleanup_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/apps/cleanup/route.ts */ \"(rsc)/./src/app/api/apps/cleanup/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/apps/cleanup/route\",\n        pathname: \"/api/apps/cleanup\",\n        filename: \"route\",\n        bundlePath: \"app/api/apps/cleanup/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/api/apps/cleanup/route.ts\",\n    nextConfigOutput,\n    userland: _home_claudiu_Desktop_proiecte_androidweb_src_app_api_apps_cleanup_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/apps/cleanup/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fapps%2Fcleanup%2Froute&page=%2Fapi%2Fapps%2Fcleanup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapps%2Fcleanup%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/apps/cleanup/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/apps/cleanup/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst APPS_FILE = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"data\", \"generated_apps.json\");\nconst CLEANUP_LOG_FILE = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"data\", \"cleanup_log.json\");\nfunction loadApps() {\n    try {\n        if (fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(APPS_FILE)) {\n            const data = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(APPS_FILE, \"utf8\");\n            return JSON.parse(data);\n        }\n    } catch (error) {\n        console.error(\"Error loading apps:\", error);\n    }\n    return [];\n}\nfunction saveApps(apps) {\n    try {\n        fs__WEBPACK_IMPORTED_MODULE_1___default().writeFileSync(APPS_FILE, JSON.stringify(apps, null, 2));\n    } catch (error) {\n        console.error(\"Error saving apps:\", error);\n    }\n}\nfunction loadCleanupLog() {\n    try {\n        if (fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(CLEANUP_LOG_FILE)) {\n            const data = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(CLEANUP_LOG_FILE, \"utf8\");\n            return JSON.parse(data);\n        }\n    } catch (error) {\n        console.error(\"Error loading cleanup log:\", error);\n    }\n    return {\n        lastCleanup: \"\",\n        totalDeleted: 0,\n        cleanupHistory: []\n    };\n}\nfunction saveCleanupLog(log) {\n    try {\n        fs__WEBPACK_IMPORTED_MODULE_1___default().writeFileSync(CLEANUP_LOG_FILE, JSON.stringify(log, null, 2));\n    } catch (error) {\n        console.error(\"Error saving cleanup log:\", error);\n    }\n}\nfunction performCleanup() {\n    const allApps = loadApps();\n    const now = new Date();\n    const expiredApps = [];\n    const validApps = [];\n    allApps.forEach((app)=>{\n        // Add expiresAt if missing (7 days from creation)\n        if (!app.expiresAt && app.createdAt) {\n            const createdDate = new Date(app.createdAt);\n            app.expiresAt = new Date(createdDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString();\n        } else if (!app.expiresAt) {\n            // If no createdAt, consider it expired\n            app.expiresAt = new Date(now.getTime() - 1000).toISOString();\n        }\n        // Check if expired\n        if (new Date(app.expiresAt) <= now) {\n            expiredApps.push({\n                id: app.id,\n                name: app.name,\n                platform: app.platform,\n                createdAt: app.createdAt,\n                expiresAt: app.expiresAt\n            });\n        } else {\n            validApps.push(app);\n        }\n    });\n    // Save only valid apps\n    if (expiredApps.length > 0) {\n        saveApps(validApps);\n        console.log(`🗑️ Cleaned up ${expiredApps.length} expired apps`);\n        expiredApps.forEach((app)=>{\n            console.log(`   - ${app.name} (${app.platform}) - expired ${app.expiresAt}`);\n        });\n    }\n    return {\n        deletedCount: expiredApps.length,\n        deletedApps: expiredApps,\n        remainingApps: validApps\n    };\n}\nasync function GET(request) {\n    try {\n        const cleanupLog = loadCleanupLog();\n        const allApps = loadApps();\n        const now = new Date();\n        // Count expired apps without deleting\n        let expiredCount = 0;\n        let soonToExpireCount = 0;\n        allApps.forEach((app)=>{\n            if (!app.expiresAt && app.createdAt) {\n                const createdDate = new Date(app.createdAt);\n                app.expiresAt = new Date(createdDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString();\n            } else if (!app.expiresAt) {\n                app.expiresAt = new Date(now.getTime() - 1000).toISOString();\n            }\n            const expiryDate = new Date(app.expiresAt);\n            if (expiryDate <= now) {\n                expiredCount++;\n            } else if (expiryDate.getTime() - now.getTime() < 24 * 60 * 60 * 1000) {\n                soonToExpireCount++;\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            cleanupLog,\n            stats: {\n                totalApps: allApps.length,\n                expiredApps: expiredCount,\n                soonToExpireApps: soonToExpireCount,\n                validApps: allApps.length - expiredCount\n            }\n        });\n    } catch (error) {\n        console.error(\"Error getting cleanup status:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to get cleanup status\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const { action } = await request.json();\n        if (action !== \"cleanup\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid action\"\n            }, {\n                status: 400\n            });\n        }\n        const result = performCleanup();\n        const cleanupLog = loadCleanupLog();\n        // Update cleanup log\n        const cleanupEntry = {\n            timestamp: new Date().toISOString(),\n            deletedCount: result.deletedCount,\n            deletedApps: result.deletedApps\n        };\n        cleanupLog.lastCleanup = cleanupEntry.timestamp;\n        cleanupLog.totalDeleted += result.deletedCount;\n        cleanupLog.cleanupHistory.push(cleanupEntry);\n        // Keep only last 50 cleanup entries\n        if (cleanupLog.cleanupHistory.length > 50) {\n            cleanupLog.cleanupHistory = cleanupLog.cleanupHistory.slice(-50);\n        }\n        saveCleanupLog(cleanupLog);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Cleanup completed. Deleted ${result.deletedCount} expired apps.`,\n            result: {\n                deletedCount: result.deletedCount,\n                deletedApps: result.deletedApps,\n                remainingApps: result.remainingApps.length\n            },\n            cleanupLog\n        });\n    } catch (error) {\n        console.error(\"Error performing cleanup:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to perform cleanup\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        // Force cleanup of all apps (for testing/admin purposes)\n        const allApps = loadApps();\n        const cleanupLog = loadCleanupLog();\n        const cleanupEntry = {\n            timestamp: new Date().toISOString(),\n            deletedCount: allApps.length,\n            deletedApps: allApps.map((app)=>({\n                    id: app.id,\n                    name: app.name,\n                    platform: app.platform,\n                    createdAt: app.createdAt,\n                    expiresAt: app.expiresAt\n                }))\n        };\n        // Clear all apps\n        saveApps([]);\n        // Update cleanup log\n        cleanupLog.lastCleanup = cleanupEntry.timestamp;\n        cleanupLog.totalDeleted += cleanupEntry.deletedCount;\n        cleanupLog.cleanupHistory.push(cleanupEntry);\n        if (cleanupLog.cleanupHistory.length > 50) {\n            cleanupLog.cleanupHistory = cleanupLog.cleanupHistory.slice(-50);\n        }\n        saveCleanupLog(cleanupLog);\n        console.log(`🗑️ Force cleanup: Deleted all ${allApps.length} apps`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Force cleanup completed. Deleted all ${allApps.length} apps.`,\n            result: {\n                deletedCount: allApps.length,\n                deletedApps: cleanupEntry.deletedApps,\n                remainingApps: 0\n            }\n        });\n    } catch (error) {\n        console.error(\"Error performing force cleanup:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to perform force cleanup\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/apps/cleanup/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fapps%2Fcleanup%2Froute&page=%2Fapi%2Fapps%2Fcleanup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapps%2Fcleanup%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();