"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/apps/saved/route";
exports.ids = ["app/api/apps/saved/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fapps%2Fsaved%2Froute&page=%2Fapi%2Fapps%2Fsaved%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapps%2Fsaved%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fapps%2Fsaved%2Froute&page=%2Fapi%2Fapps%2Fsaved%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapps%2Fsaved%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_claudiu_Desktop_proiecte_androidweb_src_app_api_apps_saved_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/apps/saved/route.ts */ \"(rsc)/./src/app/api/apps/saved/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/apps/saved/route\",\n        pathname: \"/api/apps/saved\",\n        filename: \"route\",\n        bundlePath: \"app/api/apps/saved/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/api/apps/saved/route.ts\",\n    nextConfigOutput,\n    userland: _home_claudiu_Desktop_proiecte_androidweb_src_app_api_apps_saved_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/apps/saved/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fapps%2Fsaved%2Froute&page=%2Fapi%2Fapps%2Fsaved%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapps%2Fsaved%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/apps/saved/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/apps/saved/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst APPS_FILE = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"data\", \"generated_apps.json\");\nfunction loadApps() {\n    try {\n        if (fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(APPS_FILE)) {\n            const data = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(APPS_FILE, \"utf8\");\n            return JSON.parse(data);\n        }\n    } catch (error) {\n        console.error(\"Error loading apps:\", error);\n    }\n    return [];\n}\nfunction calculateDaysLeft(expiresAt) {\n    const now = new Date();\n    const expiry = new Date(expiresAt);\n    const diffTime = expiry.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const userId = searchParams.get(\"userId\") || \"default\";\n        const allApps = loadApps();\n        // Filter apps for user and add expiry dates if missing\n        const now = new Date();\n        const userApps = allApps.filter((app)=>app.userId === userId || userId === \"default\").map((app)=>{\n            // Add expiresAt if missing (7 days from creation)\n            if (!app.expiresAt && app.createdAt) {\n                const createdDate = new Date(app.createdAt);\n                app.expiresAt = new Date(createdDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString();\n            } else if (!app.expiresAt) {\n                // If no createdAt, set expiry to 7 days from now\n                app.expiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString();\n            }\n            return app;\n        }).filter((app)=>new Date(app.expiresAt) > now).map((app)=>({\n                ...app,\n                daysLeft: calculateDaysLeft(app.expiresAt)\n            })).sort((a, b)=>new Date(b.createdAt || b.id).getTime() - new Date(a.createdAt || a.id).getTime());\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            apps: userApps,\n            total: userApps.length\n        });\n    } catch (error) {\n        console.error(\"Error fetching saved apps:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch saved apps\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const appId = searchParams.get(\"appId\");\n        const userId = searchParams.get(\"userId\") || \"default\";\n        if (!appId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"App ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        const allApps = loadApps();\n        const updatedApps = allApps.filter((app)=>!(app.id === appId && (app.userId === userId || userId === \"default\")));\n        // Save updated apps\n        fs__WEBPACK_IMPORTED_MODULE_1___default().writeFileSync(APPS_FILE, JSON.stringify(updatedApps, null, 2));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"App deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting app:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to delete app\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/apps/saved/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fapps%2Fsaved%2Froute&page=%2Fapi%2Fapps%2Fsaved%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapps%2Fsaved%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();