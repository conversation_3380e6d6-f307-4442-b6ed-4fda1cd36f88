"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_claudiu_Desktop_proiecte_androidweb_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/login/route.ts */ \"(rsc)/./src/app/api/auth/login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/api/auth/login/route.ts\",\n    nextConfigOutput,\n    userland: _home_claudiu_Desktop_proiecte_androidweb_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/login/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/login/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/auth/login/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n/* harmony import */ var _lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database/prisma */ \"(rsc)/./src/lib/database/prisma.ts\");\n/* harmony import */ var _lib_auth_password__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth/password */ \"(rsc)/./src/lib/auth/password.ts\");\n/* harmony import */ var _lib_auth_jwt__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth/jwt */ \"(rsc)/./src/lib/auth/jwt.ts\");\n\n\n\n\n\n\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_4__.string().email(\"Invalid email address\").toLowerCase(),\n    password: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Password is required\"),\n    rememberMe: zod__WEBPACK_IMPORTED_MODULE_4__.boolean().optional().default(false)\n});\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const validatedData = loginSchema.parse(body);\n        const { email, password, rememberMe } = validatedData;\n        // Get client info\n        const ipAddress = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n        const userAgent = request.headers.get(\"user-agent\") || \"unknown\";\n        // Find user\n        const user = await _lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                subscriptions: {\n                    where: {\n                        status: \"ACTIVE\"\n                    },\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 1\n                }\n            }\n        });\n        if (!user) {\n            // Log failed login attempt\n            await (0,_lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.createAuditLog)(null, \"LOGIN_FAILED\", \"user\", {\n                email,\n                reason: \"user_not_found\"\n            }, ipAddress, userAgent);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid email or password\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is locked out\n        if (await (0,_lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.isUserLockedOut)(user.id)) {\n            await (0,_lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.createAuditLog)(user.id, \"LOGIN_BLOCKED\", \"user\", {\n                email,\n                reason: \"account_locked\"\n            }, ipAddress, userAgent);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Account is temporarily locked due to too many failed login attempts. Please try again later.\"\n            }, {\n                status: 423\n            });\n        }\n        // Verify password\n        const isPasswordValid = await (0,_lib_auth_password__WEBPACK_IMPORTED_MODULE_2__.verifyPassword)(password, user.passwordHash);\n        if (!isPasswordValid) {\n            // Increment login attempts\n            await (0,_lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.incrementLoginAttempts)(user.id);\n            // Log failed login attempt\n            await (0,_lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.createAuditLog)(user.id, \"LOGIN_FAILED\", \"user\", {\n                email,\n                reason: \"invalid_password\"\n            }, ipAddress, userAgent);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid email or password\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if account is active\n        if (user.subscriptionStatus === \"INACTIVE\") {\n            await (0,_lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.createAuditLog)(user.id, \"LOGIN_BLOCKED\", \"user\", {\n                email,\n                reason: \"account_inactive\"\n            }, ipAddress, userAgent);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Account is inactive. Please contact support.\"\n            }, {\n                status: 403\n            });\n        }\n        // Create session\n        const sessionDuration = rememberMe ? 30 * 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000 // 30 days or 7 days\n        ;\n        const sessionExpiresAt = new Date(Date.now() + sessionDuration);\n        const session = await (0,_lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.createUserSession)(user.id, \"\", sessionExpiresAt, ipAddress, userAgent);\n        // Generate tokens\n        const { accessToken, refreshToken } = (0,_lib_auth_jwt__WEBPACK_IMPORTED_MODULE_3__.generateTokenPair)(user, session.id);\n        // Update session with token\n        await _lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.session.update({\n            where: {\n                id: session.id\n            },\n            data: {\n                token: accessToken\n            }\n        });\n        // Update user last login and reset login attempts\n        await (0,_lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.updateUserLastLogin)(user.id, ipAddress);\n        // Create audit log\n        await (0,_lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.createAuditLog)(user.id, \"USER_LOGIN\", \"user\", {\n            email,\n            rememberMe,\n            sessionId: session.id\n        }, ipAddress, userAgent);\n        // Track usage\n        await (0,_lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.trackUsage)(user.id, \"user_login\", 1, {\n            source: \"web\",\n            rememberMe\n        }, ipAddress, userAgent);\n        // Get current subscription\n        const currentSubscription = user.subscriptions[0];\n        // Return user data (excluding sensitive information)\n        const userData = {\n            id: user.id,\n            email: user.email,\n            role: user.role,\n            subscriptionTier: user.subscriptionTier,\n            subscriptionStatus: user.subscriptionStatus,\n            profile: user.profile,\n            emailVerified: user.emailVerified,\n            lastLogin: user.lastLogin,\n            subscription: currentSubscription ? {\n                id: currentSubscription.id,\n                planId: currentSubscription.planId,\n                status: currentSubscription.status,\n                currentPeriodEnd: currentSubscription.currentPeriodEnd\n            } : null\n        };\n        // Set HTTP-only cookie for refresh token\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Login successful\",\n            user: userData,\n            accessToken\n        }, {\n            status: 200\n        });\n        response.cookies.set(\"refresh-token\", refreshToken, {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"strict\",\n            maxAge: sessionDuration / 1000,\n            path: \"/\"\n        });\n        return response;\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Validation failed\",\n                details: error.errors.map((err)=>({\n                        field: err.path.join(\".\"),\n                        message: err.message\n                    }))\n            }, {\n                status: 400\n            });\n        }\n        // Log error\n        await (0,_lib_database_prisma__WEBPACK_IMPORTED_MODULE_1__.createAuditLog)(null, \"LOGIN_ERROR\", \"user\", {\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, request.ip || \"unknown\", request.headers.get(\"user-agent\") || \"unknown\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/jwt.ts":
/*!*****************************!*\
  !*** ./src/lib/auth/jwt.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractTokenFromHeader: () => (/* binding */ extractTokenFromHeader),\n/* harmony export */   generateSecureToken: () => (/* binding */ generateSecureToken),\n/* harmony export */   generateTokenPair: () => (/* binding */ generateTokenPair),\n/* harmony export */   getTokenExpiration: () => (/* binding */ getTokenExpiration),\n/* harmony export */   getTokenTimeRemaining: () => (/* binding */ getTokenTimeRemaining),\n/* harmony export */   hashToken: () => (/* binding */ hashToken),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   verifyAccessToken: () => (/* binding */ verifyAccessToken),\n/* harmony export */   verifyRefreshToken: () => (/* binding */ verifyRefreshToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n\nconst JWT_SECRET = process.env.JWT_SECRET;\nconst JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || JWT_SECRET + \"_refresh\";\nconst ACCESS_TOKEN_EXPIRES_IN = \"15m\";\nconst REFRESH_TOKEN_EXPIRES_IN = \"7d\";\nfunction generateTokenPair(user, sessionId) {\n    const payload = {\n        userId: user.id,\n        email: user.email,\n        role: user.role,\n        subscriptionTier: user.subscriptionTier,\n        subscriptionStatus: user.subscriptionStatus,\n        sessionId\n    };\n    const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: ACCESS_TOKEN_EXPIRES_IN,\n        issuer: \"androidweb-enterprise\",\n        audience: \"androidweb-users\"\n    });\n    const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n        userId: user.id,\n        sessionId\n    }, JWT_REFRESH_SECRET, {\n        expiresIn: REFRESH_TOKEN_EXPIRES_IN,\n        issuer: \"androidweb-enterprise\",\n        audience: \"androidweb-users\"\n    });\n    return {\n        accessToken,\n        refreshToken\n    };\n}\nfunction verifyAccessToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET, {\n            issuer: \"androidweb-enterprise\",\n            audience: \"androidweb-users\"\n        });\n        return decoded;\n    } catch (error) {\n        console.error(\"Access token verification failed:\", error);\n        return null;\n    }\n}\nfunction verifyRefreshToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_REFRESH_SECRET, {\n            issuer: \"androidweb-enterprise\",\n            audience: \"androidweb-users\"\n        });\n        return decoded;\n    } catch (error) {\n        console.error(\"Refresh token verification failed:\", error);\n        return null;\n    }\n}\nfunction extractTokenFromHeader(authHeader) {\n    if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n        return null;\n    }\n    return authHeader.substring(7);\n}\nfunction getTokenExpiration(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().decode(token);\n        if (decoded && decoded.exp) {\n            return new Date(decoded.exp * 1000);\n        }\n        return null;\n    } catch (error) {\n        return null;\n    }\n}\nfunction isTokenExpired(token) {\n    const expiration = getTokenExpiration(token);\n    if (!expiration) return true;\n    return expiration.getTime() < Date.now();\n}\nfunction getTokenTimeRemaining(token) {\n    const expiration = getTokenExpiration(token);\n    if (!expiration) return 0;\n    return Math.max(0, expiration.getTime() - Date.now());\n}\n// Security utilities\nfunction generateSecureToken(length = 32) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\nfunction hashToken(token) {\n    const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n    return crypto.createHash(\"sha256\").update(token).digest(\"hex\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/jwt.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/password.ts":
/*!**********************************!*\
  !*** ./src/lib/auth/password.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateEmailVerificationToken: () => (/* binding */ generateEmailVerificationToken),\n/* harmony export */   generatePasswordResetToken: () => (/* binding */ generatePasswordResetToken),\n/* harmony export */   generateSecurePassword: () => (/* binding */ generateSecurePassword),\n/* harmony export */   getPasswordResetExpiration: () => (/* binding */ getPasswordResetExpiration),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isPasswordResetTokenExpired: () => (/* binding */ isPasswordResetTokenExpired),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SALT_ROUNDS = 12;\nconst MIN_PASSWORD_LENGTH = 8;\nconst MAX_PASSWORD_LENGTH = 128;\nasync function hashPassword(password) {\n    if (!password || password.length < MIN_PASSWORD_LENGTH) {\n        throw new Error(`Password must be at least ${MIN_PASSWORD_LENGTH} characters long`);\n    }\n    if (password.length > MAX_PASSWORD_LENGTH) {\n        throw new Error(`Password must be no more than ${MAX_PASSWORD_LENGTH} characters long`);\n    }\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, SALT_ROUNDS);\n}\nasync function verifyPassword(password, hash) {\n    if (!password || !hash) {\n        return false;\n    }\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n    } catch (error) {\n        console.error(\"Password verification error:\", error);\n        return false;\n    }\n}\nfunction validatePassword(password) {\n    const errors = [];\n    let score = 0;\n    // Length check\n    if (!password) {\n        errors.push(\"Password is required\");\n        return {\n            isValid: false,\n            errors,\n            strength: \"weak\",\n            score: 0\n        };\n    }\n    if (password.length < MIN_PASSWORD_LENGTH) {\n        errors.push(`Password must be at least ${MIN_PASSWORD_LENGTH} characters long`);\n    } else {\n        score += 1;\n    }\n    if (password.length > MAX_PASSWORD_LENGTH) {\n        errors.push(`Password must be no more than ${MAX_PASSWORD_LENGTH} characters long`);\n    }\n    // Character variety checks\n    const hasLowercase = /[a-z]/.test(password);\n    const hasUppercase = /[A-Z]/.test(password);\n    const hasNumbers = /\\d/.test(password);\n    const hasSpecialChars = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password);\n    if (!hasLowercase) {\n        errors.push(\"Password must contain at least one lowercase letter\");\n    } else {\n        score += 1;\n    }\n    if (!hasUppercase) {\n        errors.push(\"Password must contain at least one uppercase letter\");\n    } else {\n        score += 1;\n    }\n    if (!hasNumbers) {\n        errors.push(\"Password must contain at least one number\");\n    } else {\n        score += 1;\n    }\n    if (!hasSpecialChars) {\n        errors.push(\"Password must contain at least one special character\");\n    } else {\n        score += 1;\n    }\n    // Additional strength checks\n    if (password.length >= 12) {\n        score += 1;\n    }\n    if (password.length >= 16) {\n        score += 1;\n    }\n    // Check for common patterns\n    const commonPatterns = [\n        /(.)\\1{2,}/,\n        /123456|654321|abcdef|qwerty|password|admin/i\n    ];\n    for (const pattern of commonPatterns){\n        if (pattern.test(password)) {\n            errors.push(\"Password contains common patterns and is not secure\");\n            score = Math.max(0, score - 2);\n            break;\n        }\n    }\n    // Determine strength\n    let strength;\n    if (score <= 2) {\n        strength = \"weak\";\n    } else if (score <= 4) {\n        strength = \"medium\";\n    } else if (score <= 6) {\n        strength = \"strong\";\n    } else {\n        strength = \"very-strong\";\n    }\n    return {\n        isValid: errors.length === 0,\n        errors,\n        strength,\n        score\n    };\n}\nfunction generateSecurePassword(length = 16) {\n    const lowercase = \"abcdefghijklmnopqrstuvwxyz\";\n    const uppercase = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n    const numbers = \"0123456789\";\n    const symbols = \"!@#$%^&*()_+-=[]{}|;:,.<>?\";\n    const allChars = lowercase + uppercase + numbers + symbols;\n    let password = \"\";\n    // Ensure at least one character from each category\n    password += lowercase[Math.floor(Math.random() * lowercase.length)];\n    password += uppercase[Math.floor(Math.random() * uppercase.length)];\n    password += numbers[Math.floor(Math.random() * numbers.length)];\n    password += symbols[Math.floor(Math.random() * symbols.length)];\n    // Fill the rest randomly\n    for(let i = 4; i < length; i++){\n        password += allChars[Math.floor(Math.random() * allChars.length)];\n    }\n    // Shuffle the password\n    return password.split(\"\").sort(()=>Math.random() - 0.5).join(\"\");\n}\nfunction generatePasswordResetToken() {\n    return crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(32).toString(\"hex\");\n}\nfunction generateEmailVerificationToken() {\n    return crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(32).toString(\"hex\");\n}\nfunction isPasswordResetTokenExpired(expiresAt) {\n    return new Date() > expiresAt;\n}\nfunction getPasswordResetExpiration() {\n    const expiration = new Date();\n    expiration.setHours(expiration.getHours() + 1) // 1 hour from now\n    ;\n    return expiration;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/password.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/prisma.ts":
/*!************************************!*\
  !*** ./src/lib/database/prisma.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkDatabaseHealth: () => (/* binding */ checkDatabaseHealth),\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   connectToDatabase: () => (/* binding */ connectToDatabase),\n/* harmony export */   createAuditLog: () => (/* binding */ createAuditLog),\n/* harmony export */   createUserSession: () => (/* binding */ createUserSession),\n/* harmony export */   disconnectFromDatabase: () => (/* binding */ disconnectFromDatabase),\n/* harmony export */   findUserByEmail: () => (/* binding */ findUserByEmail),\n/* harmony export */   findUserById: () => (/* binding */ findUserById),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   incrementLoginAttempts: () => (/* binding */ incrementLoginAttempts),\n/* harmony export */   invalidateAllUserSessions: () => (/* binding */ invalidateAllUserSessions),\n/* harmony export */   invalidateUserSession: () => (/* binding */ invalidateUserSession),\n/* harmony export */   isUserLockedOut: () => (/* binding */ isUserLockedOut),\n/* harmony export */   prisma: () => (/* binding */ prisma),\n/* harmony export */   recordSystemMetric: () => (/* binding */ recordSystemMetric),\n/* harmony export */   trackUsage: () => (/* binding */ trackUsage),\n/* harmony export */   updateUserLastLogin: () => (/* binding */ updateUserLastLogin),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   withTransaction: () => (/* binding */ withTransaction)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        \"query\",\n        \"error\",\n        \"warn\"\n    ] : 0,\n    errorFormat: \"pretty\"\n});\nif (true) globalForPrisma.prisma = prisma;\n// Database connection helper\nasync function connectToDatabase() {\n    try {\n        await prisma.$connect();\n        console.log(\"✅ Database connected successfully\");\n    } catch (error) {\n        console.error(\"❌ Database connection failed:\", error);\n        throw error;\n    }\n}\n// Graceful shutdown\nasync function disconnectFromDatabase() {\n    try {\n        await prisma.$disconnect();\n        console.log(\"✅ Database disconnected successfully\");\n    } catch (error) {\n        console.error(\"❌ Database disconnection failed:\", error);\n    }\n}\n// Health check\nasync function checkDatabaseHealth() {\n    try {\n        await prisma.$queryRaw`SELECT 1`;\n        return {\n            status: \"healthy\",\n            timestamp: new Date().toISOString()\n        };\n    } catch (error) {\n        return {\n            status: \"unhealthy\",\n            error: error instanceof Error ? error.message : \"Unknown error\",\n            timestamp: new Date().toISOString()\n        };\n    }\n}\n// Transaction helper\nasync function withTransaction(callback) {\n    return prisma.$transaction(callback);\n}\n// Audit logging helper\nasync function createAuditLog(userId, action, resource, details, ipAddress, userAgent) {\n    try {\n        await prisma.auditLog.create({\n            data: {\n                userId,\n                action,\n                resource,\n                details: details || {},\n                ipAddress,\n                userAgent\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to create audit log:\", error);\n    }\n}\n// Usage tracking helper\nasync function trackUsage(userId, actionType, resourceConsumed = 1, metadata, ipAddress, userAgent) {\n    try {\n        await prisma.usageTracking.create({\n            data: {\n                userId,\n                actionType,\n                resourceConsumed,\n                metadata: metadata || {},\n                ipAddress,\n                userAgent\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to track usage:\", error);\n    }\n}\n// System metrics helper\nasync function recordSystemMetric(metricType, metricValue, metadata) {\n    try {\n        await prisma.systemMetrics.create({\n            data: {\n                metricType,\n                metricValue,\n                metadata: metadata || {}\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to record system metric:\", error);\n    }\n}\n// User session management\nasync function createUserSession(userId, token, expiresAt, ipAddress, userAgent) {\n    return prisma.session.create({\n        data: {\n            userId,\n            token,\n            expiresAt,\n            ipAddress,\n            userAgent\n        }\n    });\n}\nasync function invalidateUserSession(token) {\n    return prisma.session.update({\n        where: {\n            token\n        },\n        data: {\n            isActive: false\n        }\n    });\n}\nasync function invalidateAllUserSessions(userId) {\n    return prisma.session.updateMany({\n        where: {\n            userId,\n            isActive: true\n        },\n        data: {\n            isActive: false\n        }\n    });\n}\nasync function cleanupExpiredSessions() {\n    return prisma.session.deleteMany({\n        where: {\n            OR: [\n                {\n                    expiresAt: {\n                        lt: new Date()\n                    }\n                },\n                {\n                    isActive: false\n                }\n            ]\n        }\n    });\n}\n// User management helpers\nasync function findUserByEmail(email) {\n    return prisma.user.findUnique({\n        where: {\n            email: email.toLowerCase()\n        },\n        include: {\n            subscriptions: {\n                where: {\n                    status: \"ACTIVE\"\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                take: 1\n            }\n        }\n    });\n}\nasync function findUserById(id) {\n    return prisma.user.findUnique({\n        where: {\n            id\n        },\n        include: {\n            subscriptions: {\n                where: {\n                    status: \"ACTIVE\"\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                take: 1\n            }\n        }\n    });\n}\nasync function updateUserLastLogin(userId, ipAddress) {\n    return prisma.user.update({\n        where: {\n            id: userId\n        },\n        data: {\n            lastLogin: new Date(),\n            loginAttempts: 0,\n            lockoutUntil: null\n        }\n    });\n}\nasync function incrementLoginAttempts(userId) {\n    const user = await prisma.user.findUnique({\n        where: {\n            id: userId\n        },\n        select: {\n            loginAttempts: true\n        }\n    });\n    const attempts = (user?.loginAttempts || 0) + 1;\n    const shouldLockout = attempts >= 5;\n    return prisma.user.update({\n        where: {\n            id: userId\n        },\n        data: {\n            loginAttempts: attempts,\n            lockoutUntil: shouldLockout ? new Date(Date.now() + 30 * 60 * 1000) : null\n        }\n    });\n}\nasync function isUserLockedOut(userId) {\n    const user = await prisma.user.findUnique({\n        where: {\n            id: userId\n        },\n        select: {\n            lockoutUntil: true\n        }\n    });\n    if (!user?.lockoutUntil) return false;\n    return new Date() < user.lockoutUntil;\n}\n// Password utilities\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nfunction validatePassword(password) {\n    const errors = [];\n    if (password.length < 8) {\n        errors.push(\"Password must be at least 8 characters long\");\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push(\"Password must contain at least one uppercase letter\");\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push(\"Password must contain at least one lowercase letter\");\n    }\n    if (!/\\d/.test(password)) {\n        errors.push(\"Password must contain at least one number\");\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push(\"Password must contain at least one special character\");\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();