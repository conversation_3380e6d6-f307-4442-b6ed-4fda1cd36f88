"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_claudiu_Desktop_proiecte_androidweb_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/login/route.ts */ \"(rsc)/./src/app/api/auth/login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/api/auth/login/route.ts\",\n    nextConfigOutput,\n    userland: _home_claudiu_Desktop_proiecte_androidweb_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/login/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/login/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/auth/login/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_database_simple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/database-simple */ \"(rsc)/./src/lib/database-simple.ts\");\n\n\n\n\n\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_4__.string().email(),\n    password: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1)\n});\nconst JWT_SECRET = \"your-super-secret-jwt-key\";\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { email, password } = loginSchema.parse(body);\n        const user = await _lib_database_simple__WEBPACK_IMPORTED_MODULE_3__.userDb.findByEmail(email.toLowerCase());\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid email or password\"\n            }, {\n                status: 401\n            });\n        }\n        const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, user.passwordHash);\n        if (!isPasswordValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid email or password\"\n            }, {\n                status: 401\n            });\n        }\n        await _lib_database_simple__WEBPACK_IMPORTED_MODULE_3__.userDb.update(user.id, {\n            lastLogin: new Date()\n        });\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_2___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, JWT_SECRET, {\n            expiresIn: \"7d\"\n        });\n        await _lib_database_simple__WEBPACK_IMPORTED_MODULE_3__.sessionDb.create({\n            userId: user.id,\n            token,\n            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n            isActive: true\n        });\n        const { passwordHash, ...userWithoutPassword } = user;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Login successful\",\n            user: userWithoutPassword,\n            accessToken: token\n        });\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database-simple.ts":
/*!************************************!*\
  !*** ./src/lib/database-simple.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbUtils: () => (/* binding */ dbUtils),\n/* harmony export */   projectDb: () => (/* binding */ projectDb),\n/* harmony export */   sessionDb: () => (/* binding */ sessionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb)\n/* harmony export */ });\n// In-memory storage\nconst users = new Map();\nconst projects = new Map();\nconst sessions = new Map();\n// Initialize with admin user\nconst initializeDatabase = ()=>{\n    // Use a pre-hashed password for admin123\n    const adminPasswordHash = \"$2a$12$qr2r3p3UQfxYWvk6piGDw.Xvdq0Ro9tr1Z/8M83eS4Lrp7MDtpDHK\";\n    const adminUser = {\n        id: \"admin_001\",\n        email: \"<EMAIL>\",\n        passwordHash: adminPasswordHash,\n        name: \"Administrator\",\n        role: \"ADMIN\",\n        subscriptionTier: \"ENTERPRISE\",\n        isEmailVerified: true,\n        totalAppsGenerated: 0,\n        createdAt: new Date()\n    };\n    users.set(adminUser.id, adminUser);\n    console.log(\"Database initialized with admin user:\", adminUser.email);\n};\n// User operations\nconst userDb = {\n    async findByEmail (email) {\n        for (const user of users.values()){\n            if (user.email === email) {\n                return user;\n            }\n        }\n        return null;\n    },\n    async findById (id) {\n        return users.get(id) || null;\n    },\n    async create (userData) {\n        const user = {\n            ...userData,\n            id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            createdAt: new Date()\n        };\n        users.set(user.id, user);\n        return user;\n    },\n    async update (id, updates) {\n        const user = users.get(id);\n        if (!user) return null;\n        const updatedUser = {\n            ...user,\n            ...updates\n        };\n        users.set(id, updatedUser);\n        return updatedUser;\n    },\n    async delete (id) {\n        return users.delete(id);\n    },\n    async list () {\n        return Array.from(users.values());\n    },\n    async count () {\n        return users.size;\n    }\n};\n// Project operations\nconst projectDb = {\n    async findById (id) {\n        return projects.get(id) || null;\n    },\n    async findByUserId (userId) {\n        return Array.from(projects.values()).filter((p)=>p.userId === userId);\n    },\n    async create (projectData) {\n        const now = new Date();\n        const project = {\n            ...projectData,\n            id: `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            createdAt: now,\n            updatedAt: now,\n            expiresAt: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)\n        };\n        projects.set(project.id, project);\n        return project;\n    },\n    async update (id, updates) {\n        const project = projects.get(id);\n        if (!project) return null;\n        const updatedProject = {\n            ...project,\n            ...updates,\n            updatedAt: new Date()\n        };\n        projects.set(id, updatedProject);\n        return updatedProject;\n    },\n    async delete (id) {\n        return projects.delete(id);\n    },\n    async list () {\n        return Array.from(projects.values());\n    },\n    async count () {\n        return projects.size;\n    },\n    async countByUserId (userId) {\n        return Array.from(projects.values()).filter((p)=>p.userId === userId).length;\n    },\n    async cleanup () {\n        const now = new Date();\n        let deletedCount = 0;\n        for (const [id, project] of projects.entries()){\n            if (project.expiresAt && project.expiresAt <= now) {\n                projects.delete(id);\n                deletedCount++;\n            }\n        }\n        return deletedCount;\n    }\n};\n// Session operations\nconst sessionDb = {\n    async findByToken (token) {\n        for (const session of sessions.values()){\n            if (session.token === token && session.isActive && session.expiresAt > new Date()) {\n                return session;\n            }\n        }\n        return null;\n    },\n    async create (sessionData) {\n        const session = {\n            ...sessionData,\n            id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            createdAt: new Date()\n        };\n        sessions.set(session.id, session);\n        return session;\n    },\n    async invalidate (token) {\n        for (const [id, session] of sessions.entries()){\n            if (session.token === token) {\n                sessions.set(id, {\n                    ...session,\n                    isActive: false\n                });\n                return true;\n            }\n        }\n        return false;\n    },\n    async invalidateAllForUser (userId) {\n        let count = 0;\n        for (const [id, session] of sessions.entries()){\n            if (session.userId === userId) {\n                sessions.set(id, {\n                    ...session,\n                    isActive: false\n                });\n                count++;\n            }\n        }\n        return count;\n    },\n    async cleanup () {\n        const now = new Date();\n        let deletedCount = 0;\n        for (const [id, session] of sessions.entries()){\n            if (!session.isActive || session.expiresAt <= now) {\n                sessions.delete(id);\n                deletedCount++;\n            }\n        }\n        return deletedCount;\n    }\n};\n// Utility functions\nconst dbUtils = {\n    async getStats () {\n        return {\n            users: users.size,\n            projects: projects.size,\n            sessions: sessions.size,\n            activeUsers: Array.from(users.values()).filter((u)=>u.lastLogin && u.lastLogin > new Date(Date.now() - 24 * 60 * 60 * 1000)).length,\n            projectsToday: Array.from(projects.values()).filter((p)=>p.createdAt > new Date(Date.now() - 24 * 60 * 60 * 1000)).length\n        };\n    },\n    async cleanup () {\n        const sessionsCleaned = await sessionDb.cleanup();\n        const projectsCleaned = await projectDb.cleanup();\n        console.log(`Database cleanup: ${sessionsCleaned} sessions, ${projectsCleaned} projects`);\n        return {\n            sessionsCleaned,\n            projectsCleaned\n        };\n    }\n};\n// Initialize database on import\ninitializeDatabase();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database-simple.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();