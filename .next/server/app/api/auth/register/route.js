"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/register/route";
exports.ids = ["app/api/auth/register/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = import("pg");;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_claudiu_Desktop_proiecte_androidweb_src_app_api_auth_register_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/register/route.ts */ \"(rsc)/./src/app/api/auth/register/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_home_claudiu_Desktop_proiecte_androidweb_src_app_api_auth_register_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_home_claudiu_Desktop_proiecte_androidweb_src_app_api_auth_register_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/register/route\",\n        pathname: \"/api/auth/register\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/register/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/api/auth/register/route.ts\",\n    nextConfigOutput,\n    userland: _home_claudiu_Desktop_proiecte_androidweb_src_app_api_auth_register_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/register/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/register/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/auth/register/route.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_utils_password__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils/password */ \"(rsc)/./src/lib/utils/password.ts\");\n/* harmony import */ var _lib_utils_audit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/audit */ \"(rsc)/./src/lib/utils/audit.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_database__WEBPACK_IMPORTED_MODULE_1__, _lib_utils_audit__WEBPACK_IMPORTED_MODULE_5__]);\n([_lib_database__WEBPACK_IMPORTED_MODULE_1__, _lib_utils_audit__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst registerSchema = zod__WEBPACK_IMPORTED_MODULE_6__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_6__.string().email(\"Invalid email address\").toLowerCase(),\n    password: zod__WEBPACK_IMPORTED_MODULE_6__.string().min(8, \"Password must be at least 8 characters\"),\n    name: zod__WEBPACK_IMPORTED_MODULE_6__.string().min(2, \"Name must be at least 2 characters\").optional(),\n    acceptTerms: zod__WEBPACK_IMPORTED_MODULE_6__.boolean().refine((val)=>val === true, \"You must accept the terms and conditions\")\n});\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const validatedData = registerSchema.parse(body);\n        const { email, password, name, acceptTerms } = validatedData;\n        // Get client info\n        const ipAddress = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n        const userAgent = request.headers.get(\"user-agent\") || \"unknown\";\n        // Validate password strength\n        const passwordValidation = (0,_lib_utils_password__WEBPACK_IMPORTED_MODULE_4__.validatePassword)(password);\n        if (!passwordValidation.isValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Password does not meet security requirements\",\n                details: passwordValidation.errors\n            }, {\n                status: 400\n            });\n        }\n        // Check if user already exists\n        const existingUser = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.db.user.findUnique({\n            where: {\n                email\n            }\n        });\n        if (existingUser) {\n            // Log failed registration attempt\n            await (0,_lib_utils_audit__WEBPACK_IMPORTED_MODULE_5__.createAuditLog)(null, \"REGISTRATION_FAILED\", \"user\", {\n                email,\n                reason: \"email_already_exists\"\n            }, ipAddress, userAgent);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"An account with this email already exists\"\n            }, {\n                status: 409\n            });\n        }\n        // Hash password\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, 12);\n        // Create user\n        const user = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.db.user.create({\n            data: {\n                email,\n                password: hashedPassword,\n                name: name || email.split(\"@\")[0],\n                role: \"USER\",\n                isEmailVerified: false,\n                totalAppsGenerated: 0\n            }\n        });\n        // Generate JWT token\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_3___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, process.env.JWT_SECRET || \"fallback-secret\", {\n            expiresIn: \"7d\"\n        });\n        // Create audit log\n        await (0,_lib_utils_audit__WEBPACK_IMPORTED_MODULE_5__.createAuditLog)(user.id, \"USER_REGISTERED\", \"user\", {\n            email\n        }, ipAddress, userAgent);\n        // Return user data (excluding sensitive information)\n        const userData = {\n            id: user.id,\n            email: user.email,\n            name: user.name,\n            role: user.role,\n            isEmailVerified: user.isEmailVerified,\n            totalAppsGenerated: user.totalAppsGenerated,\n            createdAt: user.createdAt\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Registration successful\",\n            user: userData,\n            accessToken\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Registration error:\", error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_7__.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Validation failed\",\n                details: error.errors.map((err)=>({\n                        field: err.path.join(\".\"),\n                        message: err.message\n                    }))\n            }, {\n                status: 400\n            });\n        }\n        // Log error\n        try {\n            await (0,_lib_utils_audit__WEBPACK_IMPORTED_MODULE_5__.createAuditLog)(null, \"REGISTRATION_ERROR\", \"user\", {\n                error: error instanceof Error ? error.message : \"Unknown error\"\n            }, request.ip || \"unknown\", request.headers.get(\"user-agent\") || \"unknown\");\n        } catch (auditError) {\n            console.error(\"Failed to log audit:\", auditError);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/register/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseService: () => (/* binding */ DatabaseService),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/**\n * Database connection and utilities\n * Fallback implementation without Prisma for now\n */ \n// Database connection pool\nlet pool = null;\nfunction getDatabase() {\n    if (!pool) {\n        pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n            host: \"localhost\",\n            port: 5432,\n            database: \"androidweb_enterprise\",\n            user: \"androidweb\",\n            password: \"androidweb123\",\n            max: 20,\n            idleTimeoutMillis: 30000,\n            connectionTimeoutMillis: 2000\n        });\n    }\n    return pool;\n}\n// Database operations\nclass DatabaseService {\n    constructor(){\n        this.pool = getDatabase();\n    }\n    // Initialize database tables\n    async initializeTables() {\n        const client = await this.pool.connect();\n        try {\n            // Create users table\n            await client.query(`\n        CREATE TABLE IF NOT EXISTS users (\n          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n          email VARCHAR(255) UNIQUE NOT NULL,\n          password VARCHAR(255) NOT NULL,\n          first_name VARCHAR(100),\n          last_name VARCHAR(100),\n          role VARCHAR(20) DEFAULT 'USER',\n          is_active BOOLEAN DEFAULT true,\n          email_verified BOOLEAN DEFAULT false,\n          created_at TIMESTAMP DEFAULT NOW(),\n          updated_at TIMESTAMP DEFAULT NOW()\n        )\n      `);\n            // Create projects table\n            await client.query(`\n        CREATE TABLE IF NOT EXISTS projects (\n          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n          name VARCHAR(255) NOT NULL,\n          description TEXT,\n          platform VARCHAR(20) NOT NULL,\n          template_type VARCHAR(50),\n          user_id UUID REFERENCES users(id) ON DELETE CASCADE,\n          source_code JSONB,\n          build_config JSONB,\n          status VARCHAR(20) DEFAULT 'DRAFT',\n          created_at TIMESTAMP DEFAULT NOW(),\n          updated_at TIMESTAMP DEFAULT NOW()\n        )\n      `);\n            // Create conversations table\n            await client.query(`\n        CREATE TABLE IF NOT EXISTS conversations (\n          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n          user_id UUID REFERENCES users(id) ON DELETE CASCADE,\n          project_id UUID REFERENCES projects(id) ON DELETE CASCADE,\n          messages JSONB DEFAULT '[]',\n          ai_context JSONB DEFAULT '{}',\n          created_at TIMESTAMP DEFAULT NOW(),\n          updated_at TIMESTAMP DEFAULT NOW()\n        )\n      `);\n            // Create subscriptions table\n            await client.query(`\n        CREATE TABLE IF NOT EXISTS subscriptions (\n          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n          user_id UUID REFERENCES users(id) ON DELETE CASCADE,\n          plan_type VARCHAR(20) NOT NULL,\n          status VARCHAR(20) DEFAULT 'ACTIVE',\n          stripe_subscription_id VARCHAR(255),\n          current_period_start TIMESTAMP,\n          current_period_end TIMESTAMP,\n          created_at TIMESTAMP DEFAULT NOW(),\n          updated_at TIMESTAMP DEFAULT NOW()\n        )\n      `);\n            // Create ai_learning_data table\n            await client.query(`\n        CREATE TABLE IF NOT EXISTS ai_learning_data (\n          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n          conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,\n          user_feedback INTEGER,\n          success_metrics JSONB,\n          code_quality_score DECIMAL(3,2),\n          user_satisfaction DECIMAL(3,2),\n          learning_patterns JSONB,\n          processing_time INTEGER,\n          tokens_used INTEGER,\n          created_at TIMESTAMP DEFAULT NOW()\n        )\n      `);\n            console.log(\"✅ Database tables initialized successfully\");\n        } catch (error) {\n            console.error(\"❌ Failed to initialize database tables:\", error);\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    // User operations\n    async createUser(userData) {\n        const client = await this.pool.connect();\n        try {\n            const result = await client.query(`INSERT INTO users (email, password, first_name, last_name, role, is_active, email_verified)\n         VALUES ($1, $2, $3, $4, $5, $6, $7)\n         RETURNING *`, [\n                userData.email,\n                userData.password,\n                userData.firstName,\n                userData.lastName,\n                userData.role,\n                userData.isActive,\n                userData.emailVerified\n            ]);\n            return this.mapUserRow(result.rows[0]);\n        } finally{\n            client.release();\n        }\n    }\n    async findUserByEmail(email) {\n        const client = await this.pool.connect();\n        try {\n            const result = await client.query(\"SELECT * FROM users WHERE email = $1\", [\n                email\n            ]);\n            return result.rows.length > 0 ? this.mapUserRow(result.rows[0]) : null;\n        } finally{\n            client.release();\n        }\n    }\n    async findUserById(id) {\n        const client = await this.pool.connect();\n        try {\n            const result = await client.query(\"SELECT * FROM users WHERE id = $1\", [\n                id\n            ]);\n            return result.rows.length > 0 ? this.mapUserRow(result.rows[0]) : null;\n        } finally{\n            client.release();\n        }\n    }\n    // Project operations\n    async createProject(projectData) {\n        const client = await this.pool.connect();\n        try {\n            const result = await client.query(`INSERT INTO projects (name, description, platform, template_type, user_id, source_code, build_config, status)\n         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)\n         RETURNING *`, [\n                projectData.name,\n                projectData.description,\n                projectData.platform,\n                projectData.templateType,\n                projectData.userId,\n                JSON.stringify(projectData.sourceCode),\n                JSON.stringify(projectData.buildConfig),\n                projectData.status\n            ]);\n            return this.mapProjectRow(result.rows[0]);\n        } finally{\n            client.release();\n        }\n    }\n    async findProjectsByUserId(userId) {\n        const client = await this.pool.connect();\n        try {\n            const result = await client.query(\"SELECT * FROM projects WHERE user_id = $1 ORDER BY created_at DESC\", [\n                userId\n            ]);\n            return result.rows.map((row)=>this.mapProjectRow(row));\n        } finally{\n            client.release();\n        }\n    }\n    async updateProject(id, updates) {\n        const client = await this.pool.connect();\n        try {\n            const setClause = [];\n            const values = [];\n            let paramIndex = 1;\n            Object.entries(updates).forEach(([key, value])=>{\n                if (key === \"sourceCode\" || key === \"buildConfig\") {\n                    setClause.push(`${this.camelToSnake(key)} = $${paramIndex}`);\n                    values.push(JSON.stringify(value));\n                } else if (key !== \"id\" && key !== \"createdAt\") {\n                    setClause.push(`${this.camelToSnake(key)} = $${paramIndex}`);\n                    values.push(value);\n                }\n                paramIndex++;\n            });\n            if (setClause.length === 0) return null;\n            setClause.push(`updated_at = NOW()`);\n            values.push(id);\n            const result = await client.query(`UPDATE projects SET ${setClause.join(\", \")} WHERE id = $${paramIndex} RETURNING *`, values);\n            return result.rows.length > 0 ? this.mapProjectRow(result.rows[0]) : null;\n        } finally{\n            client.release();\n        }\n    }\n    // Helper methods\n    mapUserRow(row) {\n        return {\n            id: row.id,\n            email: row.email,\n            password: row.password,\n            firstName: row.first_name,\n            lastName: row.last_name,\n            role: row.role,\n            isActive: row.is_active,\n            emailVerified: row.email_verified,\n            createdAt: row.created_at,\n            updatedAt: row.updated_at\n        };\n    }\n    mapProjectRow(row) {\n        return {\n            id: row.id,\n            name: row.name,\n            description: row.description,\n            platform: row.platform,\n            templateType: row.template_type,\n            userId: row.user_id,\n            sourceCode: row.source_code,\n            buildConfig: row.build_config,\n            status: row.status,\n            createdAt: row.created_at,\n            updatedAt: row.updated_at\n        };\n    }\n    camelToSnake(str) {\n        return str.replace(/[A-Z]/g, (letter)=>`_${letter.toLowerCase()}`);\n    }\n    // Health check\n    async checkHealth() {\n        try {\n            const client = await this.pool.connect();\n            await client.query(\"SELECT 1\");\n            client.release();\n            return true;\n        } catch (error) {\n            console.error(\"Database health check failed:\", error);\n            return false;\n        }\n    }\n}\n// Export singleton instance\nconst db = new DatabaseService();\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/audit.ts":
/*!********************************!*\
  !*** ./src/lib/utils/audit.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupOldLogs: () => (/* binding */ cleanupOldLogs),\n/* harmony export */   createAuditLog: () => (/* binding */ createAuditLog),\n/* harmony export */   getAuditLogs: () => (/* binding */ getAuditLogs),\n/* harmony export */   getSecurityEvents: () => (/* binding */ getSecurityEvents),\n/* harmony export */   getSystemStats: () => (/* binding */ getSystemStats),\n/* harmony export */   getUserActivity: () => (/* binding */ getUserActivity)\n/* harmony export */ });\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_database__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_database__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nasync function createAuditLog(userId, action, category, data = {}, ipAddress = \"unknown\", userAgent = \"unknown\") {\n    try {\n        await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.auditLog.create({\n            data: {\n                userId,\n                action,\n                category,\n                data,\n                ipAddress,\n                userAgent,\n                timestamp: new Date()\n            }\n        });\n    } catch (error) {\n        // Log to console if database logging fails\n        console.error(\"Failed to create audit log:\", {\n            userId,\n            action,\n            category,\n            data,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\nasync function getAuditLogs(filters = {}) {\n    const { userId, action, category, startDate, endDate, limit = 50, offset = 0 } = filters;\n    const where = {};\n    if (userId) where.userId = userId;\n    if (action) where.action = action;\n    if (category) where.category = category;\n    if (startDate || endDate) {\n        where.timestamp = {};\n        if (startDate) where.timestamp.gte = startDate;\n        if (endDate) where.timestamp.lte = endDate;\n    }\n    const [logs, total] = await Promise.all([\n        _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.auditLog.findMany({\n            where,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        name: true,\n                        email: true\n                    }\n                }\n            },\n            orderBy: {\n                timestamp: \"desc\"\n            },\n            take: limit,\n            skip: offset\n        }),\n        _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.auditLog.count({\n            where\n        })\n    ]);\n    return {\n        logs,\n        total,\n        hasMore: offset + limit < total\n    };\n}\nasync function getSecurityEvents(userId, limit = 20) {\n    const where = {\n        category: \"security\"\n    };\n    if (userId) {\n        where.userId = userId;\n    }\n    return _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.auditLog.findMany({\n        where,\n        include: {\n            user: {\n                select: {\n                    id: true,\n                    name: true,\n                    email: true\n                }\n            }\n        },\n        orderBy: {\n            timestamp: \"desc\"\n        },\n        take: limit\n    });\n}\nasync function getUserActivity(userId, limit = 50) {\n    return _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.auditLog.findMany({\n        where: {\n            userId,\n            category: {\n                in: [\n                    \"user\",\n                    \"project\",\n                    \"ai\"\n                ]\n            }\n        },\n        orderBy: {\n            timestamp: \"desc\"\n        },\n        take: limit\n    });\n}\nasync function getSystemStats() {\n    const now = new Date();\n    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n    const [totalLogs, last24HoursLogs, last7DaysLogs, userRegistrations24h, appGenerations24h, securityEvents24h] = await Promise.all([\n        _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.auditLog.count(),\n        _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.auditLog.count({\n            where: {\n                timestamp: {\n                    gte: last24Hours\n                }\n            }\n        }),\n        _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.auditLog.count({\n            where: {\n                timestamp: {\n                    gte: last7Days\n                }\n            }\n        }),\n        _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.auditLog.count({\n            where: {\n                action: \"USER_REGISTERED\",\n                timestamp: {\n                    gte: last24Hours\n                }\n            }\n        }),\n        _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.auditLog.count({\n            where: {\n                action: \"APP_GENERATED\",\n                timestamp: {\n                    gte: last24Hours\n                }\n            }\n        }),\n        _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.auditLog.count({\n            where: {\n                category: \"security\",\n                timestamp: {\n                    gte: last24Hours\n                }\n            }\n        })\n    ]);\n    return {\n        totalLogs,\n        last24HoursLogs,\n        last7DaysLogs,\n        userRegistrations24h,\n        appGenerations24h,\n        securityEvents24h\n    };\n}\nasync function cleanupOldLogs(daysToKeep = 90) {\n    const cutoffDate = new Date();\n    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);\n    const result = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.auditLog.deleteMany({\n        where: {\n            timestamp: {\n                lt: cutoffDate\n            },\n            category: {\n                not: \"security\"\n            }\n        }\n    });\n    return result.count;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzL2F1ZGl0LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBbUM7QUFvQzVCLGVBQWVDLGVBQ3BCQyxNQUFxQixFQUNyQkMsTUFBbUIsRUFDbkJDLFFBQXVCLEVBQ3ZCQyxPQUFxQixDQUFDLENBQUMsRUFDdkJDLFlBQW9CLFNBQVMsRUFDN0JDLFlBQW9CLFNBQVM7SUFFN0IsSUFBSTtRQUNGLE1BQU1QLDZDQUFFQSxDQUFDUSxRQUFRLENBQUNDLE1BQU0sQ0FBQztZQUN2QkosTUFBTTtnQkFDSkg7Z0JBQ0FDO2dCQUNBQztnQkFDQUM7Z0JBQ0FDO2dCQUNBQztnQkFDQUcsV0FBVyxJQUFJQztZQUNqQjtRQUNGO0lBQ0YsRUFBRSxPQUFPQyxPQUFPO1FBQ2QsMkNBQTJDO1FBQzNDQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCO1lBQzNDVjtZQUNBQztZQUNBQztZQUNBQztZQUNBTyxPQUFPQSxpQkFBaUJFLFFBQVFGLE1BQU1HLE9BQU8sR0FBRztRQUNsRDtJQUNGO0FBQ0Y7QUFFTyxlQUFlQyxhQUNwQkMsVUFRSSxDQUFDLENBQUM7SUFFTixNQUFNLEVBQ0pmLE1BQU0sRUFDTkMsTUFBTSxFQUNOQyxRQUFRLEVBQ1JjLFNBQVMsRUFDVEMsT0FBTyxFQUNQQyxRQUFRLEVBQUUsRUFDVkMsU0FBUyxDQUFDLEVBQ1gsR0FBR0o7SUFFSixNQUFNSyxRQUFhLENBQUM7SUFFcEIsSUFBSXBCLFFBQVFvQixNQUFNcEIsTUFBTSxHQUFHQTtJQUMzQixJQUFJQyxRQUFRbUIsTUFBTW5CLE1BQU0sR0FBR0E7SUFDM0IsSUFBSUMsVUFBVWtCLE1BQU1sQixRQUFRLEdBQUdBO0lBQy9CLElBQUljLGFBQWFDLFNBQVM7UUFDeEJHLE1BQU1aLFNBQVMsR0FBRyxDQUFDO1FBQ25CLElBQUlRLFdBQVdJLE1BQU1aLFNBQVMsQ0FBQ2EsR0FBRyxHQUFHTDtRQUNyQyxJQUFJQyxTQUFTRyxNQUFNWixTQUFTLENBQUNjLEdBQUcsR0FBR0w7SUFDckM7SUFFQSxNQUFNLENBQUNNLE1BQU1DLE1BQU0sR0FBRyxNQUFNQyxRQUFRQyxHQUFHLENBQUM7UUFDdEM1Qiw2Q0FBRUEsQ0FBQ1EsUUFBUSxDQUFDcUIsUUFBUSxDQUFDO1lBQ25CUDtZQUNBUSxTQUFTO2dCQUNQQyxNQUFNO29CQUNKQyxRQUFRO3dCQUNOQyxJQUFJO3dCQUNKQyxNQUFNO3dCQUNOQyxPQUFPO29CQUNUO2dCQUNGO1lBQ0Y7WUFDQUMsU0FBUztnQkFDUDFCLFdBQVc7WUFDYjtZQUNBMkIsTUFBTWpCO1lBQ05rQixNQUFNakI7UUFDUjtRQUNBckIsNkNBQUVBLENBQUNRLFFBQVEsQ0FBQytCLEtBQUssQ0FBQztZQUFFakI7UUFBTTtLQUMzQjtJQUVELE9BQU87UUFDTEc7UUFDQUM7UUFDQWMsU0FBU25CLFNBQVNELFFBQVFNO0lBQzVCO0FBQ0Y7QUFFTyxlQUFlZSxrQkFDcEJ2QyxNQUFlLEVBQ2ZrQixRQUFnQixFQUFFO0lBRWxCLE1BQU1FLFFBQWE7UUFDakJsQixVQUFVO0lBQ1o7SUFFQSxJQUFJRixRQUFRO1FBQ1ZvQixNQUFNcEIsTUFBTSxHQUFHQTtJQUNqQjtJQUVBLE9BQU9GLDZDQUFFQSxDQUFDUSxRQUFRLENBQUNxQixRQUFRLENBQUM7UUFDMUJQO1FBQ0FRLFNBQVM7WUFDUEMsTUFBTTtnQkFDSkMsUUFBUTtvQkFDTkMsSUFBSTtvQkFDSkMsTUFBTTtvQkFDTkMsT0FBTztnQkFDVDtZQUNGO1FBQ0Y7UUFDQUMsU0FBUztZQUNQMUIsV0FBVztRQUNiO1FBQ0EyQixNQUFNakI7SUFDUjtBQUNGO0FBRU8sZUFBZXNCLGdCQUNwQnhDLE1BQWMsRUFDZGtCLFFBQWdCLEVBQUU7SUFFbEIsT0FBT3BCLDZDQUFFQSxDQUFDUSxRQUFRLENBQUNxQixRQUFRLENBQUM7UUFDMUJQLE9BQU87WUFDTHBCO1lBQ0FFLFVBQVU7Z0JBQ1J1QyxJQUFJO29CQUFDO29CQUFRO29CQUFXO2lCQUFLO1lBQy9CO1FBQ0Y7UUFDQVAsU0FBUztZQUNQMUIsV0FBVztRQUNiO1FBQ0EyQixNQUFNakI7SUFDUjtBQUNGO0FBRU8sZUFBZXdCO0lBQ3BCLE1BQU1DLE1BQU0sSUFBSWxDO0lBQ2hCLE1BQU1tQyxjQUFjLElBQUluQyxLQUFLa0MsSUFBSUUsT0FBTyxLQUFLLEtBQUssS0FBSyxLQUFLO0lBQzVELE1BQU1DLFlBQVksSUFBSXJDLEtBQUtrQyxJQUFJRSxPQUFPLEtBQUssSUFBSSxLQUFLLEtBQUssS0FBSztJQUU5RCxNQUFNLENBQ0pFLFdBQ0FDLGlCQUNBQyxlQUNBQyxzQkFDQUMsbUJBQ0FDLGtCQUNELEdBQUcsTUFBTTNCLFFBQVFDLEdBQUcsQ0FBQztRQUNwQjVCLDZDQUFFQSxDQUFDUSxRQUFRLENBQUMrQixLQUFLO1FBQ2pCdkMsNkNBQUVBLENBQUNRLFFBQVEsQ0FBQytCLEtBQUssQ0FBQztZQUNoQmpCLE9BQU87Z0JBQ0xaLFdBQVc7b0JBQ1RhLEtBQUt1QjtnQkFDUDtZQUNGO1FBQ0Y7UUFDQTlDLDZDQUFFQSxDQUFDUSxRQUFRLENBQUMrQixLQUFLLENBQUM7WUFDaEJqQixPQUFPO2dCQUNMWixXQUFXO29CQUNUYSxLQUFLeUI7Z0JBQ1A7WUFDRjtRQUNGO1FBQ0FoRCw2Q0FBRUEsQ0FBQ1EsUUFBUSxDQUFDK0IsS0FBSyxDQUFDO1lBQ2hCakIsT0FBTztnQkFDTG5CLFFBQVE7Z0JBQ1JPLFdBQVc7b0JBQ1RhLEtBQUt1QjtnQkFDUDtZQUNGO1FBQ0Y7UUFDQTlDLDZDQUFFQSxDQUFDUSxRQUFRLENBQUMrQixLQUFLLENBQUM7WUFDaEJqQixPQUFPO2dCQUNMbkIsUUFBUTtnQkFDUk8sV0FBVztvQkFDVGEsS0FBS3VCO2dCQUNQO1lBQ0Y7UUFDRjtRQUNBOUMsNkNBQUVBLENBQUNRLFFBQVEsQ0FBQytCLEtBQUssQ0FBQztZQUNoQmpCLE9BQU87Z0JBQ0xsQixVQUFVO2dCQUNWTSxXQUFXO29CQUNUYSxLQUFLdUI7Z0JBQ1A7WUFDRjtRQUNGO0tBQ0Q7SUFFRCxPQUFPO1FBQ0xHO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO0lBQ0Y7QUFDRjtBQUVPLGVBQWVDLGVBQWVDLGFBQXFCLEVBQUU7SUFDMUQsTUFBTUMsYUFBYSxJQUFJOUM7SUFDdkI4QyxXQUFXQyxPQUFPLENBQUNELFdBQVdFLE9BQU8sS0FBS0g7SUFFMUMsTUFBTUksU0FBUyxNQUFNNUQsNkNBQUVBLENBQUNRLFFBQVEsQ0FBQ3FELFVBQVUsQ0FBQztRQUMxQ3ZDLE9BQU87WUFDTFosV0FBVztnQkFDVG9ELElBQUlMO1lBQ047WUFDQXJELFVBQVU7Z0JBQ1IyRCxLQUFLO1lBQ1A7UUFDRjtJQUNGO0lBRUEsT0FBT0gsT0FBT3JCLEtBQUs7QUFDckIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmRyb2lkd2ViLWVudGVycHJpc2UvLi9zcmMvbGliL3V0aWxzL2F1ZGl0LnRzPzcxY2YiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGIgfSBmcm9tICdAL2xpYi9kYXRhYmFzZSdcblxuZXhwb3J0IHR5cGUgQXVkaXRBY3Rpb24gPSBcbiAgfCAnVVNFUl9SRUdJU1RFUkVEJ1xuICB8ICdVU0VSX0xPR0lOJ1xuICB8ICdVU0VSX0xPR09VVCdcbiAgfCAnUEFTU1dPUkRfQ0hBTkdFRCdcbiAgfCAnRU1BSUxfVkVSSUZJRUQnXG4gIHwgJ1NVQlNDUklQVElPTl9DUkVBVEVEJ1xuICB8ICdTVUJTQ1JJUFRJT05fVVBEQVRFRCdcbiAgfCAnU1VCU0NSSVBUSU9OX0NBTkNFTExFRCdcbiAgfCAnUFJPSkVDVF9DUkVBVEVEJ1xuICB8ICdQUk9KRUNUX1VQREFURUQnXG4gIHwgJ1BST0pFQ1RfREVMRVRFRCdcbiAgfCAnQVBQX0dFTkVSQVRFRCdcbiAgfCAnQUlfSU5URVJBQ1RJT04nXG4gIHwgJ0FETUlOX0FDVElPTidcbiAgfCAnU0VDVVJJVFlfRVZFTlQnXG4gIHwgJ1JFR0lTVFJBVElPTl9GQUlMRUQnXG4gIHwgJ0xPR0lOX0ZBSUxFRCdcbiAgfCAnUkVHSVNUUkFUSU9OX0VSUk9SJ1xuICB8ICdTWVNURU1fRVJST1InXG5cbmV4cG9ydCB0eXBlIEF1ZGl0Q2F0ZWdvcnkgPSBcbiAgfCAndXNlcidcbiAgfCAnc3Vic2NyaXB0aW9uJ1xuICB8ICdwcm9qZWN0J1xuICB8ICdhaSdcbiAgfCAnYWRtaW4nXG4gIHwgJ3NlY3VyaXR5J1xuICB8ICdzeXN0ZW0nXG5cbmV4cG9ydCBpbnRlcmZhY2UgQXVkaXRMb2dEYXRhIHtcbiAgW2tleTogc3RyaW5nXTogYW55XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjcmVhdGVBdWRpdExvZyhcbiAgdXNlcklkOiBzdHJpbmcgfCBudWxsLFxuICBhY3Rpb246IEF1ZGl0QWN0aW9uLFxuICBjYXRlZ29yeTogQXVkaXRDYXRlZ29yeSxcbiAgZGF0YTogQXVkaXRMb2dEYXRhID0ge30sXG4gIGlwQWRkcmVzczogc3RyaW5nID0gJ3Vua25vd24nLFxuICB1c2VyQWdlbnQ6IHN0cmluZyA9ICd1bmtub3duJ1xuKTogUHJvbWlzZTx2b2lkPiB7XG4gIHRyeSB7XG4gICAgYXdhaXQgZGIuYXVkaXRMb2cuY3JlYXRlKHtcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgdXNlcklkLFxuICAgICAgICBhY3Rpb24sXG4gICAgICAgIGNhdGVnb3J5LFxuICAgICAgICBkYXRhLFxuICAgICAgICBpcEFkZHJlc3MsXG4gICAgICAgIHVzZXJBZ2VudCxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgICAgfSxcbiAgICB9KVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIC8vIExvZyB0byBjb25zb2xlIGlmIGRhdGFiYXNlIGxvZ2dpbmcgZmFpbHNcbiAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gY3JlYXRlIGF1ZGl0IGxvZzonLCB7XG4gICAgICB1c2VySWQsXG4gICAgICBhY3Rpb24sXG4gICAgICBjYXRlZ29yeSxcbiAgICAgIGRhdGEsXG4gICAgICBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcidcbiAgICB9KVxuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRBdWRpdExvZ3MoXG4gIGZpbHRlcnM6IHtcbiAgICB1c2VySWQ/OiBzdHJpbmdcbiAgICBhY3Rpb24/OiBBdWRpdEFjdGlvblxuICAgIGNhdGVnb3J5PzogQXVkaXRDYXRlZ29yeVxuICAgIHN0YXJ0RGF0ZT86IERhdGVcbiAgICBlbmREYXRlPzogRGF0ZVxuICAgIGxpbWl0PzogbnVtYmVyXG4gICAgb2Zmc2V0PzogbnVtYmVyXG4gIH0gPSB7fVxuKSB7XG4gIGNvbnN0IHtcbiAgICB1c2VySWQsXG4gICAgYWN0aW9uLFxuICAgIGNhdGVnb3J5LFxuICAgIHN0YXJ0RGF0ZSxcbiAgICBlbmREYXRlLFxuICAgIGxpbWl0ID0gNTAsXG4gICAgb2Zmc2V0ID0gMFxuICB9ID0gZmlsdGVyc1xuXG4gIGNvbnN0IHdoZXJlOiBhbnkgPSB7fVxuXG4gIGlmICh1c2VySWQpIHdoZXJlLnVzZXJJZCA9IHVzZXJJZFxuICBpZiAoYWN0aW9uKSB3aGVyZS5hY3Rpb24gPSBhY3Rpb25cbiAgaWYgKGNhdGVnb3J5KSB3aGVyZS5jYXRlZ29yeSA9IGNhdGVnb3J5XG4gIGlmIChzdGFydERhdGUgfHwgZW5kRGF0ZSkge1xuICAgIHdoZXJlLnRpbWVzdGFtcCA9IHt9XG4gICAgaWYgKHN0YXJ0RGF0ZSkgd2hlcmUudGltZXN0YW1wLmd0ZSA9IHN0YXJ0RGF0ZVxuICAgIGlmIChlbmREYXRlKSB3aGVyZS50aW1lc3RhbXAubHRlID0gZW5kRGF0ZVxuICB9XG5cbiAgY29uc3QgW2xvZ3MsIHRvdGFsXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICBkYi5hdWRpdExvZy5maW5kTWFueSh7XG4gICAgICB3aGVyZSxcbiAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgdXNlcjoge1xuICAgICAgICAgIHNlbGVjdDoge1xuICAgICAgICAgICAgaWQ6IHRydWUsXG4gICAgICAgICAgICBuYW1lOiB0cnVlLFxuICAgICAgICAgICAgZW1haWw6IHRydWUsXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgICBvcmRlckJ5OiB7XG4gICAgICAgIHRpbWVzdGFtcDogJ2Rlc2MnLFxuICAgICAgfSxcbiAgICAgIHRha2U6IGxpbWl0LFxuICAgICAgc2tpcDogb2Zmc2V0LFxuICAgIH0pLFxuICAgIGRiLmF1ZGl0TG9nLmNvdW50KHsgd2hlcmUgfSksXG4gIF0pXG5cbiAgcmV0dXJuIHtcbiAgICBsb2dzLFxuICAgIHRvdGFsLFxuICAgIGhhc01vcmU6IG9mZnNldCArIGxpbWl0IDwgdG90YWwsXG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFNlY3VyaXR5RXZlbnRzKFxuICB1c2VySWQ/OiBzdHJpbmcsXG4gIGxpbWl0OiBudW1iZXIgPSAyMFxuKSB7XG4gIGNvbnN0IHdoZXJlOiBhbnkgPSB7XG4gICAgY2F0ZWdvcnk6ICdzZWN1cml0eScsXG4gIH1cblxuICBpZiAodXNlcklkKSB7XG4gICAgd2hlcmUudXNlcklkID0gdXNlcklkXG4gIH1cblxuICByZXR1cm4gZGIuYXVkaXRMb2cuZmluZE1hbnkoe1xuICAgIHdoZXJlLFxuICAgIGluY2x1ZGU6IHtcbiAgICAgIHVzZXI6IHtcbiAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgaWQ6IHRydWUsXG4gICAgICAgICAgbmFtZTogdHJ1ZSxcbiAgICAgICAgICBlbWFpbDogdHJ1ZSxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgfSxcbiAgICBvcmRlckJ5OiB7XG4gICAgICB0aW1lc3RhbXA6ICdkZXNjJyxcbiAgICB9LFxuICAgIHRha2U6IGxpbWl0LFxuICB9KVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VXNlckFjdGl2aXR5KFxuICB1c2VySWQ6IHN0cmluZyxcbiAgbGltaXQ6IG51bWJlciA9IDUwXG4pIHtcbiAgcmV0dXJuIGRiLmF1ZGl0TG9nLmZpbmRNYW55KHtcbiAgICB3aGVyZToge1xuICAgICAgdXNlcklkLFxuICAgICAgY2F0ZWdvcnk6IHtcbiAgICAgICAgaW46IFsndXNlcicsICdwcm9qZWN0JywgJ2FpJ10sXG4gICAgICB9LFxuICAgIH0sXG4gICAgb3JkZXJCeToge1xuICAgICAgdGltZXN0YW1wOiAnZGVzYycsXG4gICAgfSxcbiAgICB0YWtlOiBsaW1pdCxcbiAgfSlcbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFN5c3RlbVN0YXRzKCkge1xuICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXG4gIGNvbnN0IGxhc3QyNEhvdXJzID0gbmV3IERhdGUobm93LmdldFRpbWUoKSAtIDI0ICogNjAgKiA2MCAqIDEwMDApXG4gIGNvbnN0IGxhc3Q3RGF5cyA9IG5ldyBEYXRlKG5vdy5nZXRUaW1lKCkgLSA3ICogMjQgKiA2MCAqIDYwICogMTAwMClcblxuICBjb25zdCBbXG4gICAgdG90YWxMb2dzLFxuICAgIGxhc3QyNEhvdXJzTG9ncyxcbiAgICBsYXN0N0RheXNMb2dzLFxuICAgIHVzZXJSZWdpc3RyYXRpb25zMjRoLFxuICAgIGFwcEdlbmVyYXRpb25zMjRoLFxuICAgIHNlY3VyaXR5RXZlbnRzMjRoLFxuICBdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgIGRiLmF1ZGl0TG9nLmNvdW50KCksXG4gICAgZGIuYXVkaXRMb2cuY291bnQoe1xuICAgICAgd2hlcmU6IHtcbiAgICAgICAgdGltZXN0YW1wOiB7XG4gICAgICAgICAgZ3RlOiBsYXN0MjRIb3VycyxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgfSksXG4gICAgZGIuYXVkaXRMb2cuY291bnQoe1xuICAgICAgd2hlcmU6IHtcbiAgICAgICAgdGltZXN0YW1wOiB7XG4gICAgICAgICAgZ3RlOiBsYXN0N0RheXMsXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH0pLFxuICAgIGRiLmF1ZGl0TG9nLmNvdW50KHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIGFjdGlvbjogJ1VTRVJfUkVHSVNURVJFRCcsXG4gICAgICAgIHRpbWVzdGFtcDoge1xuICAgICAgICAgIGd0ZTogbGFzdDI0SG91cnMsXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH0pLFxuICAgIGRiLmF1ZGl0TG9nLmNvdW50KHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIGFjdGlvbjogJ0FQUF9HRU5FUkFURUQnLFxuICAgICAgICB0aW1lc3RhbXA6IHtcbiAgICAgICAgICBndGU6IGxhc3QyNEhvdXJzLFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICB9KSxcbiAgICBkYi5hdWRpdExvZy5jb3VudCh7XG4gICAgICB3aGVyZToge1xuICAgICAgICBjYXRlZ29yeTogJ3NlY3VyaXR5JyxcbiAgICAgICAgdGltZXN0YW1wOiB7XG4gICAgICAgICAgZ3RlOiBsYXN0MjRIb3VycyxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgfSksXG4gIF0pXG5cbiAgcmV0dXJuIHtcbiAgICB0b3RhbExvZ3MsXG4gICAgbGFzdDI0SG91cnNMb2dzLFxuICAgIGxhc3Q3RGF5c0xvZ3MsXG4gICAgdXNlclJlZ2lzdHJhdGlvbnMyNGgsXG4gICAgYXBwR2VuZXJhdGlvbnMyNGgsXG4gICAgc2VjdXJpdHlFdmVudHMyNGgsXG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNsZWFudXBPbGRMb2dzKGRheXNUb0tlZXA6IG51bWJlciA9IDkwKSB7XG4gIGNvbnN0IGN1dG9mZkRhdGUgPSBuZXcgRGF0ZSgpXG4gIGN1dG9mZkRhdGUuc2V0RGF0ZShjdXRvZmZEYXRlLmdldERhdGUoKSAtIGRheXNUb0tlZXApXG5cbiAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZGIuYXVkaXRMb2cuZGVsZXRlTWFueSh7XG4gICAgd2hlcmU6IHtcbiAgICAgIHRpbWVzdGFtcDoge1xuICAgICAgICBsdDogY3V0b2ZmRGF0ZSxcbiAgICAgIH0sXG4gICAgICBjYXRlZ29yeToge1xuICAgICAgICBub3Q6ICdzZWN1cml0eScsIC8vIEtlZXAgc2VjdXJpdHkgbG9ncyBsb25nZXJcbiAgICAgIH0sXG4gICAgfSxcbiAgfSlcblxuICByZXR1cm4gcmVzdWx0LmNvdW50XG59XG4iXSwibmFtZXMiOlsiZGIiLCJjcmVhdGVBdWRpdExvZyIsInVzZXJJZCIsImFjdGlvbiIsImNhdGVnb3J5IiwiZGF0YSIsImlwQWRkcmVzcyIsInVzZXJBZ2VudCIsImF1ZGl0TG9nIiwiY3JlYXRlIiwidGltZXN0YW1wIiwiRGF0ZSIsImVycm9yIiwiY29uc29sZSIsIkVycm9yIiwibWVzc2FnZSIsImdldEF1ZGl0TG9ncyIsImZpbHRlcnMiLCJzdGFydERhdGUiLCJlbmREYXRlIiwibGltaXQiLCJvZmZzZXQiLCJ3aGVyZSIsImd0ZSIsImx0ZSIsImxvZ3MiLCJ0b3RhbCIsIlByb21pc2UiLCJhbGwiLCJmaW5kTWFueSIsImluY2x1ZGUiLCJ1c2VyIiwic2VsZWN0IiwiaWQiLCJuYW1lIiwiZW1haWwiLCJvcmRlckJ5IiwidGFrZSIsInNraXAiLCJjb3VudCIsImhhc01vcmUiLCJnZXRTZWN1cml0eUV2ZW50cyIsImdldFVzZXJBY3Rpdml0eSIsImluIiwiZ2V0U3lzdGVtU3RhdHMiLCJub3ciLCJsYXN0MjRIb3VycyIsImdldFRpbWUiLCJsYXN0N0RheXMiLCJ0b3RhbExvZ3MiLCJsYXN0MjRIb3Vyc0xvZ3MiLCJsYXN0N0RheXNMb2dzIiwidXNlclJlZ2lzdHJhdGlvbnMyNGgiLCJhcHBHZW5lcmF0aW9uczI0aCIsInNlY3VyaXR5RXZlbnRzMjRoIiwiY2xlYW51cE9sZExvZ3MiLCJkYXlzVG9LZWVwIiwiY3V0b2ZmRGF0ZSIsInNldERhdGUiLCJnZXREYXRlIiwicmVzdWx0IiwiZGVsZXRlTWFueSIsImx0Iiwibm90Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/audit.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/password.ts":
/*!***********************************!*\
  !*** ./src/lib/utils/password.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkPasswordStrength: () => (/* binding */ checkPasswordStrength),\n/* harmony export */   generateSecurePassword: () => (/* binding */ generateSecurePassword),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword)\n/* harmony export */ });\nfunction validatePassword(password) {\n    const errors = [];\n    let strength = \"weak\";\n    // Check minimum length\n    if (password.length < 8) {\n        errors.push(\"Password must be at least 8 characters long\");\n    }\n    // Check for uppercase letter\n    if (!/[A-Z]/.test(password)) {\n        errors.push(\"Password must contain at least one uppercase letter\");\n    }\n    // Check for lowercase letter\n    if (!/[a-z]/.test(password)) {\n        errors.push(\"Password must contain at least one lowercase letter\");\n    }\n    // Check for number\n    if (!/\\d/.test(password)) {\n        errors.push(\"Password must contain at least one number\");\n    }\n    // Check for special character\n    if (!/[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)) {\n        errors.push(\"Password must contain at least one special character\");\n    }\n    // Check for common patterns\n    const commonPatterns = [\n        /123456/,\n        /password/i,\n        /qwerty/i,\n        /admin/i,\n        /letmein/i,\n        /welcome/i\n    ];\n    for (const pattern of commonPatterns){\n        if (pattern.test(password)) {\n            errors.push(\"Password contains common patterns and is not secure\");\n            break;\n        }\n    }\n    // Determine strength\n    if (errors.length === 0) {\n        if (password.length >= 12 && /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)) {\n            strength = \"strong\";\n        } else if (password.length >= 10) {\n            strength = \"medium\";\n        }\n    }\n    return {\n        isValid: errors.length === 0,\n        errors,\n        strength\n    };\n}\nfunction generateSecurePassword(length = 16) {\n    const uppercase = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n    const lowercase = \"abcdefghijklmnopqrstuvwxyz\";\n    const numbers = \"0123456789\";\n    const symbols = \"!@#$%^&*()_+-=[]{}|;:,.<>?\";\n    const allChars = uppercase + lowercase + numbers + symbols;\n    let password = \"\";\n    // Ensure at least one character from each category\n    password += uppercase[Math.floor(Math.random() * uppercase.length)];\n    password += lowercase[Math.floor(Math.random() * lowercase.length)];\n    password += numbers[Math.floor(Math.random() * numbers.length)];\n    password += symbols[Math.floor(Math.random() * symbols.length)];\n    // Fill the rest randomly\n    for(let i = 4; i < length; i++){\n        password += allChars[Math.floor(Math.random() * allChars.length)];\n    }\n    // Shuffle the password\n    return password.split(\"\").sort(()=>Math.random() - 0.5).join(\"\");\n}\nfunction checkPasswordStrength(password) {\n    let score = 0;\n    const feedback = [];\n    // Length scoring\n    if (password.length >= 8) score += 1;\n    if (password.length >= 12) score += 1;\n    if (password.length >= 16) score += 1;\n    // Character variety scoring\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/\\d/.test(password)) score += 1;\n    if (/[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)) score += 1;\n    // Complexity scoring\n    if (password.length >= 8 && /[A-Z]/.test(password) && /[a-z]/.test(password) && /\\d/.test(password)) {\n        score += 1;\n    }\n    // Provide feedback\n    if (score < 3) {\n        feedback.push(\"Password is too weak\");\n        feedback.push(\"Use a mix of uppercase, lowercase, numbers, and symbols\");\n    } else if (score < 5) {\n        feedback.push(\"Password strength is moderate\");\n        feedback.push(\"Consider making it longer or adding more variety\");\n    } else if (score < 7) {\n        feedback.push(\"Password strength is good\");\n    } else {\n        feedback.push(\"Password strength is excellent\");\n    }\n    return {\n        score,\n        feedback\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzL3Bhc3N3b3JkLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQU1PLFNBQVNBLGlCQUFpQkMsUUFBZ0I7SUFDL0MsTUFBTUMsU0FBbUIsRUFBRTtJQUMzQixJQUFJQyxXQUF5QztJQUU3Qyx1QkFBdUI7SUFDdkIsSUFBSUYsU0FBU0csTUFBTSxHQUFHLEdBQUc7UUFDdkJGLE9BQU9HLElBQUksQ0FBQztJQUNkO0lBRUEsNkJBQTZCO0lBQzdCLElBQUksQ0FBQyxRQUFRQyxJQUFJLENBQUNMLFdBQVc7UUFDM0JDLE9BQU9HLElBQUksQ0FBQztJQUNkO0lBRUEsNkJBQTZCO0lBQzdCLElBQUksQ0FBQyxRQUFRQyxJQUFJLENBQUNMLFdBQVc7UUFDM0JDLE9BQU9HLElBQUksQ0FBQztJQUNkO0lBRUEsbUJBQW1CO0lBQ25CLElBQUksQ0FBQyxLQUFLQyxJQUFJLENBQUNMLFdBQVc7UUFDeEJDLE9BQU9HLElBQUksQ0FBQztJQUNkO0lBRUEsOEJBQThCO0lBQzlCLElBQUksQ0FBQyx3Q0FBd0NDLElBQUksQ0FBQ0wsV0FBVztRQUMzREMsT0FBT0csSUFBSSxDQUFDO0lBQ2Q7SUFFQSw0QkFBNEI7SUFDNUIsTUFBTUUsaUJBQWlCO1FBQ3JCO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBRUQsS0FBSyxNQUFNQyxXQUFXRCxlQUFnQjtRQUNwQyxJQUFJQyxRQUFRRixJQUFJLENBQUNMLFdBQVc7WUFDMUJDLE9BQU9HLElBQUksQ0FBQztZQUNaO1FBQ0Y7SUFDRjtJQUVBLHFCQUFxQjtJQUNyQixJQUFJSCxPQUFPRSxNQUFNLEtBQUssR0FBRztRQUN2QixJQUFJSCxTQUFTRyxNQUFNLElBQUksTUFBTSx3Q0FBd0NFLElBQUksQ0FBQ0wsV0FBVztZQUNuRkUsV0FBVztRQUNiLE9BQU8sSUFBSUYsU0FBU0csTUFBTSxJQUFJLElBQUk7WUFDaENELFdBQVc7UUFDYjtJQUNGO0lBRUEsT0FBTztRQUNMTSxTQUFTUCxPQUFPRSxNQUFNLEtBQUs7UUFDM0JGO1FBQ0FDO0lBQ0Y7QUFDRjtBQUVPLFNBQVNPLHVCQUF1Qk4sU0FBaUIsRUFBRTtJQUN4RCxNQUFNTyxZQUFZO0lBQ2xCLE1BQU1DLFlBQVk7SUFDbEIsTUFBTUMsVUFBVTtJQUNoQixNQUFNQyxVQUFVO0lBRWhCLE1BQU1DLFdBQVdKLFlBQVlDLFlBQVlDLFVBQVVDO0lBQ25ELElBQUliLFdBQVc7SUFFZixtREFBbUQ7SUFDbkRBLFlBQVlVLFNBQVMsQ0FBQ0ssS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUtQLFVBQVVQLE1BQU0sRUFBRTtJQUNuRUgsWUFBWVcsU0FBUyxDQUFDSSxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS04sVUFBVVIsTUFBTSxFQUFFO0lBQ25FSCxZQUFZWSxPQUFPLENBQUNHLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLTCxRQUFRVCxNQUFNLEVBQUU7SUFDL0RILFlBQVlhLE9BQU8sQ0FBQ0UsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUtKLFFBQVFWLE1BQU0sRUFBRTtJQUUvRCx5QkFBeUI7SUFDekIsSUFBSyxJQUFJZSxJQUFJLEdBQUdBLElBQUlmLFFBQVFlLElBQUs7UUFDL0JsQixZQUFZYyxRQUFRLENBQUNDLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLSCxTQUFTWCxNQUFNLEVBQUU7SUFDbkU7SUFFQSx1QkFBdUI7SUFDdkIsT0FBT0gsU0FBU21CLEtBQUssQ0FBQyxJQUFJQyxJQUFJLENBQUMsSUFBTUwsS0FBS0UsTUFBTSxLQUFLLEtBQUtJLElBQUksQ0FBQztBQUNqRTtBQUVPLFNBQVNDLHNCQUFzQnRCLFFBQWdCO0lBSXBELElBQUl1QixRQUFRO0lBQ1osTUFBTUMsV0FBcUIsRUFBRTtJQUU3QixpQkFBaUI7SUFDakIsSUFBSXhCLFNBQVNHLE1BQU0sSUFBSSxHQUFHb0IsU0FBUztJQUNuQyxJQUFJdkIsU0FBU0csTUFBTSxJQUFJLElBQUlvQixTQUFTO0lBQ3BDLElBQUl2QixTQUFTRyxNQUFNLElBQUksSUFBSW9CLFNBQVM7SUFFcEMsNEJBQTRCO0lBQzVCLElBQUksUUFBUWxCLElBQUksQ0FBQ0wsV0FBV3VCLFNBQVM7SUFDckMsSUFBSSxRQUFRbEIsSUFBSSxDQUFDTCxXQUFXdUIsU0FBUztJQUNyQyxJQUFJLEtBQUtsQixJQUFJLENBQUNMLFdBQVd1QixTQUFTO0lBQ2xDLElBQUksd0NBQXdDbEIsSUFBSSxDQUFDTCxXQUFXdUIsU0FBUztJQUVyRSxxQkFBcUI7SUFDckIsSUFBSXZCLFNBQVNHLE1BQU0sSUFBSSxLQUFLLFFBQVFFLElBQUksQ0FBQ0wsYUFBYSxRQUFRSyxJQUFJLENBQUNMLGFBQWEsS0FBS0ssSUFBSSxDQUFDTCxXQUFXO1FBQ25HdUIsU0FBUztJQUNYO0lBRUEsbUJBQW1CO0lBQ25CLElBQUlBLFFBQVEsR0FBRztRQUNiQyxTQUFTcEIsSUFBSSxDQUFDO1FBQ2RvQixTQUFTcEIsSUFBSSxDQUFDO0lBQ2hCLE9BQU8sSUFBSW1CLFFBQVEsR0FBRztRQUNwQkMsU0FBU3BCLElBQUksQ0FBQztRQUNkb0IsU0FBU3BCLElBQUksQ0FBQztJQUNoQixPQUFPLElBQUltQixRQUFRLEdBQUc7UUFDcEJDLFNBQVNwQixJQUFJLENBQUM7SUFDaEIsT0FBTztRQUNMb0IsU0FBU3BCLElBQUksQ0FBQztJQUNoQjtJQUVBLE9BQU87UUFBRW1CO1FBQU9DO0lBQVM7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmRyb2lkd2ViLWVudGVycHJpc2UvLi9zcmMvbGliL3V0aWxzL3Bhc3N3b3JkLnRzPzYxMjciXSwic291cmNlc0NvbnRlbnQiOlsiaW50ZXJmYWNlIFBhc3N3b3JkVmFsaWRhdGlvbiB7XG4gIGlzVmFsaWQ6IGJvb2xlYW5cbiAgZXJyb3JzOiBzdHJpbmdbXVxuICBzdHJlbmd0aDogJ3dlYWsnIHwgJ21lZGl1bScgfCAnc3Ryb25nJ1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdmFsaWRhdGVQYXNzd29yZChwYXNzd29yZDogc3RyaW5nKTogUGFzc3dvcmRWYWxpZGF0aW9uIHtcbiAgY29uc3QgZXJyb3JzOiBzdHJpbmdbXSA9IFtdXG4gIGxldCBzdHJlbmd0aDogJ3dlYWsnIHwgJ21lZGl1bScgfCAnc3Ryb25nJyA9ICd3ZWFrJ1xuXG4gIC8vIENoZWNrIG1pbmltdW0gbGVuZ3RoXG4gIGlmIChwYXNzd29yZC5sZW5ndGggPCA4KSB7XG4gICAgZXJyb3JzLnB1c2goJ1Bhc3N3b3JkIG11c3QgYmUgYXQgbGVhc3QgOCBjaGFyYWN0ZXJzIGxvbmcnKVxuICB9XG5cbiAgLy8gQ2hlY2sgZm9yIHVwcGVyY2FzZSBsZXR0ZXJcbiAgaWYgKCEvW0EtWl0vLnRlc3QocGFzc3dvcmQpKSB7XG4gICAgZXJyb3JzLnB1c2goJ1Bhc3N3b3JkIG11c3QgY29udGFpbiBhdCBsZWFzdCBvbmUgdXBwZXJjYXNlIGxldHRlcicpXG4gIH1cblxuICAvLyBDaGVjayBmb3IgbG93ZXJjYXNlIGxldHRlclxuICBpZiAoIS9bYS16XS8udGVzdChwYXNzd29yZCkpIHtcbiAgICBlcnJvcnMucHVzaCgnUGFzc3dvcmQgbXVzdCBjb250YWluIGF0IGxlYXN0IG9uZSBsb3dlcmNhc2UgbGV0dGVyJylcbiAgfVxuXG4gIC8vIENoZWNrIGZvciBudW1iZXJcbiAgaWYgKCEvXFxkLy50ZXN0KHBhc3N3b3JkKSkge1xuICAgIGVycm9ycy5wdXNoKCdQYXNzd29yZCBtdXN0IGNvbnRhaW4gYXQgbGVhc3Qgb25lIG51bWJlcicpXG4gIH1cblxuICAvLyBDaGVjayBmb3Igc3BlY2lhbCBjaGFyYWN0ZXJcbiAgaWYgKCEvWyFAIyQlXiYqKClfK1xcLT1cXFtcXF17fTsnOlwiXFxcXHwsLjw+XFwvP10vLnRlc3QocGFzc3dvcmQpKSB7XG4gICAgZXJyb3JzLnB1c2goJ1Bhc3N3b3JkIG11c3QgY29udGFpbiBhdCBsZWFzdCBvbmUgc3BlY2lhbCBjaGFyYWN0ZXInKVxuICB9XG5cbiAgLy8gQ2hlY2sgZm9yIGNvbW1vbiBwYXR0ZXJuc1xuICBjb25zdCBjb21tb25QYXR0ZXJucyA9IFtcbiAgICAvMTIzNDU2LyxcbiAgICAvcGFzc3dvcmQvaSxcbiAgICAvcXdlcnR5L2ksXG4gICAgL2FkbWluL2ksXG4gICAgL2xldG1laW4vaSxcbiAgICAvd2VsY29tZS9pXG4gIF1cblxuICBmb3IgKGNvbnN0IHBhdHRlcm4gb2YgY29tbW9uUGF0dGVybnMpIHtcbiAgICBpZiAocGF0dGVybi50ZXN0KHBhc3N3b3JkKSkge1xuICAgICAgZXJyb3JzLnB1c2goJ1Bhc3N3b3JkIGNvbnRhaW5zIGNvbW1vbiBwYXR0ZXJucyBhbmQgaXMgbm90IHNlY3VyZScpXG4gICAgICBicmVha1xuICAgIH1cbiAgfVxuXG4gIC8vIERldGVybWluZSBzdHJlbmd0aFxuICBpZiAoZXJyb3JzLmxlbmd0aCA9PT0gMCkge1xuICAgIGlmIChwYXNzd29yZC5sZW5ndGggPj0gMTIgJiYgL1shQCMkJV4mKigpXytcXC09XFxbXFxde307JzpcIlxcXFx8LC48PlxcLz9dLy50ZXN0KHBhc3N3b3JkKSkge1xuICAgICAgc3RyZW5ndGggPSAnc3Ryb25nJ1xuICAgIH0gZWxzZSBpZiAocGFzc3dvcmQubGVuZ3RoID49IDEwKSB7XG4gICAgICBzdHJlbmd0aCA9ICdtZWRpdW0nXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHtcbiAgICBpc1ZhbGlkOiBlcnJvcnMubGVuZ3RoID09PSAwLFxuICAgIGVycm9ycyxcbiAgICBzdHJlbmd0aFxuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZVNlY3VyZVBhc3N3b3JkKGxlbmd0aDogbnVtYmVyID0gMTYpOiBzdHJpbmcge1xuICBjb25zdCB1cHBlcmNhc2UgPSAnQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVonXG4gIGNvbnN0IGxvd2VyY2FzZSA9ICdhYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5eidcbiAgY29uc3QgbnVtYmVycyA9ICcwMTIzNDU2Nzg5J1xuICBjb25zdCBzeW1ib2xzID0gJyFAIyQlXiYqKClfKy09W117fXw7OiwuPD4/J1xuICBcbiAgY29uc3QgYWxsQ2hhcnMgPSB1cHBlcmNhc2UgKyBsb3dlcmNhc2UgKyBudW1iZXJzICsgc3ltYm9sc1xuICBsZXQgcGFzc3dvcmQgPSAnJ1xuICBcbiAgLy8gRW5zdXJlIGF0IGxlYXN0IG9uZSBjaGFyYWN0ZXIgZnJvbSBlYWNoIGNhdGVnb3J5XG4gIHBhc3N3b3JkICs9IHVwcGVyY2FzZVtNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiB1cHBlcmNhc2UubGVuZ3RoKV1cbiAgcGFzc3dvcmQgKz0gbG93ZXJjYXNlW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIGxvd2VyY2FzZS5sZW5ndGgpXVxuICBwYXNzd29yZCArPSBudW1iZXJzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIG51bWJlcnMubGVuZ3RoKV1cbiAgcGFzc3dvcmQgKz0gc3ltYm9sc1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBzeW1ib2xzLmxlbmd0aCldXG4gIFxuICAvLyBGaWxsIHRoZSByZXN0IHJhbmRvbWx5XG4gIGZvciAobGV0IGkgPSA0OyBpIDwgbGVuZ3RoOyBpKyspIHtcbiAgICBwYXNzd29yZCArPSBhbGxDaGFyc1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBhbGxDaGFycy5sZW5ndGgpXVxuICB9XG4gIFxuICAvLyBTaHVmZmxlIHRoZSBwYXNzd29yZFxuICByZXR1cm4gcGFzc3dvcmQuc3BsaXQoJycpLnNvcnQoKCkgPT4gTWF0aC5yYW5kb20oKSAtIDAuNSkuam9pbignJylcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrUGFzc3dvcmRTdHJlbmd0aChwYXNzd29yZDogc3RyaW5nKToge1xuICBzY29yZTogbnVtYmVyXG4gIGZlZWRiYWNrOiBzdHJpbmdbXVxufSB7XG4gIGxldCBzY29yZSA9IDBcbiAgY29uc3QgZmVlZGJhY2s6IHN0cmluZ1tdID0gW11cblxuICAvLyBMZW5ndGggc2NvcmluZ1xuICBpZiAocGFzc3dvcmQubGVuZ3RoID49IDgpIHNjb3JlICs9IDFcbiAgaWYgKHBhc3N3b3JkLmxlbmd0aCA+PSAxMikgc2NvcmUgKz0gMVxuICBpZiAocGFzc3dvcmQubGVuZ3RoID49IDE2KSBzY29yZSArPSAxXG5cbiAgLy8gQ2hhcmFjdGVyIHZhcmlldHkgc2NvcmluZ1xuICBpZiAoL1thLXpdLy50ZXN0KHBhc3N3b3JkKSkgc2NvcmUgKz0gMVxuICBpZiAoL1tBLVpdLy50ZXN0KHBhc3N3b3JkKSkgc2NvcmUgKz0gMVxuICBpZiAoL1xcZC8udGVzdChwYXNzd29yZCkpIHNjb3JlICs9IDFcbiAgaWYgKC9bIUAjJCVeJiooKV8rXFwtPVxcW1xcXXt9Oyc6XCJcXFxcfCwuPD5cXC8/XS8udGVzdChwYXNzd29yZCkpIHNjb3JlICs9IDFcblxuICAvLyBDb21wbGV4aXR5IHNjb3JpbmdcbiAgaWYgKHBhc3N3b3JkLmxlbmd0aCA+PSA4ICYmIC9bQS1aXS8udGVzdChwYXNzd29yZCkgJiYgL1thLXpdLy50ZXN0KHBhc3N3b3JkKSAmJiAvXFxkLy50ZXN0KHBhc3N3b3JkKSkge1xuICAgIHNjb3JlICs9IDFcbiAgfVxuXG4gIC8vIFByb3ZpZGUgZmVlZGJhY2tcbiAgaWYgKHNjb3JlIDwgMykge1xuICAgIGZlZWRiYWNrLnB1c2goJ1Bhc3N3b3JkIGlzIHRvbyB3ZWFrJylcbiAgICBmZWVkYmFjay5wdXNoKCdVc2UgYSBtaXggb2YgdXBwZXJjYXNlLCBsb3dlcmNhc2UsIG51bWJlcnMsIGFuZCBzeW1ib2xzJylcbiAgfSBlbHNlIGlmIChzY29yZSA8IDUpIHtcbiAgICBmZWVkYmFjay5wdXNoKCdQYXNzd29yZCBzdHJlbmd0aCBpcyBtb2RlcmF0ZScpXG4gICAgZmVlZGJhY2sucHVzaCgnQ29uc2lkZXIgbWFraW5nIGl0IGxvbmdlciBvciBhZGRpbmcgbW9yZSB2YXJpZXR5JylcbiAgfSBlbHNlIGlmIChzY29yZSA8IDcpIHtcbiAgICBmZWVkYmFjay5wdXNoKCdQYXNzd29yZCBzdHJlbmd0aCBpcyBnb29kJylcbiAgfSBlbHNlIHtcbiAgICBmZWVkYmFjay5wdXNoKCdQYXNzd29yZCBzdHJlbmd0aCBpcyBleGNlbGxlbnQnKVxuICB9XG5cbiAgcmV0dXJuIHsgc2NvcmUsIGZlZWRiYWNrIH1cbn1cbiJdLCJuYW1lcyI6WyJ2YWxpZGF0ZVBhc3N3b3JkIiwicGFzc3dvcmQiLCJlcnJvcnMiLCJzdHJlbmd0aCIsImxlbmd0aCIsInB1c2giLCJ0ZXN0IiwiY29tbW9uUGF0dGVybnMiLCJwYXR0ZXJuIiwiaXNWYWxpZCIsImdlbmVyYXRlU2VjdXJlUGFzc3dvcmQiLCJ1cHBlcmNhc2UiLCJsb3dlcmNhc2UiLCJudW1iZXJzIiwic3ltYm9scyIsImFsbENoYXJzIiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwiaSIsInNwbGl0Iiwic29ydCIsImpvaW4iLCJjaGVja1Bhc3N3b3JkU3RyZW5ndGgiLCJzY29yZSIsImZlZWRiYWNrIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/password.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/semver","vendor-chunks/next","vendor-chunks/jsonwebtoken","vendor-chunks/zod","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();