"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/projects/route";
exports.ids = ["app/api/projects/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_claudiu_Desktop_proiecte_androidweb_src_app_api_projects_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/projects/route.ts */ \"(rsc)/./src/app/api/projects/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/projects/route\",\n        pathname: \"/api/projects\",\n        filename: \"route\",\n        bundlePath: \"app/api/projects/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/api/projects/route.ts\",\n    nextConfigOutput,\n    userland: _home_claudiu_Desktop_proiecte_androidweb_src_app_api_projects_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/projects/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/projects/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/projects/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   cleanupExpiredProjects: () => (/* binding */ cleanupExpiredProjects)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_database_simple__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database-simple */ \"(rsc)/./src/lib/database-simple.ts\");\n\n\n\n\nconst createProjectSchema = zod__WEBPACK_IMPORTED_MODULE_3__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_3__.string().min(1, \"Project name is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_3__.string().min(10, \"Description must be at least 10 characters\"),\n    platform: zod__WEBPACK_IMPORTED_MODULE_3__[\"enum\"]([\n        \"ANDROID\",\n        \"IOS\",\n        \"CROSS_PLATFORM\"\n    ]),\n    features: zod__WEBPACK_IMPORTED_MODULE_3__.array(zod__WEBPACK_IMPORTED_MODULE_3__.string()).min(1, \"At least one feature is required\"),\n    templateType: zod__WEBPACK_IMPORTED_MODULE_3__.string().optional(),\n    generatedCode: zod__WEBPACK_IMPORTED_MODULE_3__.record(zod__WEBPACK_IMPORTED_MODULE_3__.string()).optional()\n});\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nasync function GET(request) {\n    try {\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader?.startsWith(\"Bearer \")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        let userId;\n        try {\n            const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n            userId = decoded.userId;\n        } catch (error) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Get projects from database\n        const userProjects = await _lib_database_simple__WEBPACK_IMPORTED_MODULE_2__.projectDb.findByUserId(userId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            projects: userProjects,\n            total: userProjects.length,\n            message: \"Projects retrieved successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error fetching projects:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch projects\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader?.startsWith(\"Bearer \")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        let userId;\n        try {\n            const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n            userId = decoded.userId;\n        } catch (error) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = createProjectSchema.parse(body);\n        // Create new project in database\n        const newProject = await _lib_database_simple__WEBPACK_IMPORTED_MODULE_2__.projectDb.create({\n            ...validatedData,\n            userId,\n            status: \"COMPLETED\",\n            version: \"1.0.0\",\n            generatedCode: validatedData.generatedCode || {},\n            templateType: validatedData.templateType || \"basic\"\n        });\n        console.log(\"Project created:\", newProject.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            project: newProject,\n            message: \"Project created successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error creating project:\", error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_4__.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Validation failed\",\n                details: error.errors.map((err)=>({\n                        field: err.path.join(\".\"),\n                        message: err.message\n                    }))\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create project\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        // Get user from token (simplified)\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader?.startsWith(\"Bearer \")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        let userId;\n        try {\n            const payload = JSON.parse(atob(token.split(\".\")[1]));\n            userId = payload.userId;\n        } catch (error) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const projectId = searchParams.get(\"id\");\n        if (!projectId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Project ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Find and remove project\n        const projectIndex = projects.findIndex((p)=>p.id === projectId && p.userId === userId);\n        if (projectIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Project not found\"\n            }, {\n                status: 404\n            });\n        }\n        const deletedProject = projects.splice(projectIndex, 1)[0];\n        console.log(\"Project deleted:\", deletedProject.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Project deleted successfully\",\n            deletedProject: {\n                id: deletedProject.id,\n                name: deletedProject.name\n            }\n        });\n    } catch (error) {\n        console.error(\"Error deleting project:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to delete project\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Auto-cleanup function (would be called by a cron job in production)\nasync function cleanupExpiredProjects() {\n    const now = new Date();\n    const expiredProjects = projects.filter((p)=>p.autoDeleteAt && new Date(p.autoDeleteAt) <= now);\n    expiredProjects.forEach((project)=>{\n        const index = projects.findIndex((p)=>p.id === project.id);\n        if (index !== -1) {\n            projects.splice(index, 1);\n            console.log(\"Auto-deleted expired project:\", project.id);\n        }\n    });\n    return expiredProjects.length;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/projects/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database-simple.ts":
/*!************************************!*\
  !*** ./src/lib/database-simple.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbUtils: () => (/* binding */ dbUtils),\n/* harmony export */   projectDb: () => (/* binding */ projectDb),\n/* harmony export */   sessionDb: () => (/* binding */ sessionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb)\n/* harmony export */ });\n// In-memory storage\nconst users = new Map();\nconst projects = new Map();\nconst sessions = new Map();\n// Initialize with admin user\nconst initializeDatabase = ()=>{\n    // Use a pre-hashed password for admin123\n    const adminPasswordHash = \"$2a$12$qr2r3p3UQfxYWvk6piGDw.Xvdq0Ro9tr1Z/8M83eS4Lrp7MDtpDHK\";\n    const adminUser = {\n        id: \"admin_001\",\n        email: \"<EMAIL>\",\n        passwordHash: adminPasswordHash,\n        name: \"Administrator\",\n        role: \"ADMIN\",\n        subscriptionTier: \"ENTERPRISE\",\n        isEmailVerified: true,\n        totalAppsGenerated: 0,\n        createdAt: new Date()\n    };\n    users.set(adminUser.id, adminUser);\n    console.log(\"Database initialized with admin user:\", adminUser.email);\n};\n// User operations\nconst userDb = {\n    async findByEmail (email) {\n        for (const user of users.values()){\n            if (user.email === email) {\n                return user;\n            }\n        }\n        return null;\n    },\n    async findById (id) {\n        return users.get(id) || null;\n    },\n    async create (userData) {\n        const user = {\n            ...userData,\n            id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            createdAt: new Date()\n        };\n        users.set(user.id, user);\n        return user;\n    },\n    async update (id, updates) {\n        const user = users.get(id);\n        if (!user) return null;\n        const updatedUser = {\n            ...user,\n            ...updates\n        };\n        users.set(id, updatedUser);\n        return updatedUser;\n    },\n    async delete (id) {\n        return users.delete(id);\n    },\n    async list () {\n        return Array.from(users.values());\n    },\n    async count () {\n        return users.size;\n    }\n};\n// Project operations\nconst projectDb = {\n    async findById (id) {\n        return projects.get(id) || null;\n    },\n    async findByUserId (userId) {\n        return Array.from(projects.values()).filter((p)=>p.userId === userId);\n    },\n    async create (projectData) {\n        const now = new Date();\n        const project = {\n            ...projectData,\n            id: `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            createdAt: now,\n            updatedAt: now,\n            expiresAt: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)\n        };\n        projects.set(project.id, project);\n        return project;\n    },\n    async update (id, updates) {\n        const project = projects.get(id);\n        if (!project) return null;\n        const updatedProject = {\n            ...project,\n            ...updates,\n            updatedAt: new Date()\n        };\n        projects.set(id, updatedProject);\n        return updatedProject;\n    },\n    async delete (id) {\n        return projects.delete(id);\n    },\n    async list () {\n        return Array.from(projects.values());\n    },\n    async count () {\n        return projects.size;\n    },\n    async countByUserId (userId) {\n        return Array.from(projects.values()).filter((p)=>p.userId === userId).length;\n    },\n    async cleanup () {\n        const now = new Date();\n        let deletedCount = 0;\n        for (const [id, project] of projects.entries()){\n            if (project.expiresAt && project.expiresAt <= now) {\n                projects.delete(id);\n                deletedCount++;\n            }\n        }\n        return deletedCount;\n    }\n};\n// Session operations\nconst sessionDb = {\n    async findByToken (token) {\n        for (const session of sessions.values()){\n            if (session.token === token && session.isActive && session.expiresAt > new Date()) {\n                return session;\n            }\n        }\n        return null;\n    },\n    async create (sessionData) {\n        const session = {\n            ...sessionData,\n            id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            createdAt: new Date()\n        };\n        sessions.set(session.id, session);\n        return session;\n    },\n    async invalidate (token) {\n        for (const [id, session] of sessions.entries()){\n            if (session.token === token) {\n                sessions.set(id, {\n                    ...session,\n                    isActive: false\n                });\n                return true;\n            }\n        }\n        return false;\n    },\n    async invalidateAllForUser (userId) {\n        let count = 0;\n        for (const [id, session] of sessions.entries()){\n            if (session.userId === userId) {\n                sessions.set(id, {\n                    ...session,\n                    isActive: false\n                });\n                count++;\n            }\n        }\n        return count;\n    },\n    async cleanup () {\n        const now = new Date();\n        let deletedCount = 0;\n        for (const [id, session] of sessions.entries()){\n            if (!session.isActive || session.expiresAt <= now) {\n                sessions.delete(id);\n                deletedCount++;\n            }\n        }\n        return deletedCount;\n    }\n};\n// Utility functions\nconst dbUtils = {\n    async getStats () {\n        return {\n            users: users.size,\n            projects: projects.size,\n            sessions: sessions.size,\n            activeUsers: Array.from(users.values()).filter((u)=>u.lastLogin && u.lastLogin > new Date(Date.now() - 24 * 60 * 60 * 1000)).length,\n            projectsToday: Array.from(projects.values()).filter((p)=>p.createdAt > new Date(Date.now() - 24 * 60 * 60 * 1000)).length\n        };\n    },\n    async cleanup () {\n        const sessionsCleaned = await sessionDb.cleanup();\n        const projectsCleaned = await projectDb.cleanup();\n        console.log(`Database cleanup: ${sessionsCleaned} sessions, ${projectsCleaned} projects`);\n        return {\n            sessionsCleaned,\n            projectsCleaned\n        };\n    }\n};\n// Initialize database on import\ninitializeDatabase();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database-simple.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/zod","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();