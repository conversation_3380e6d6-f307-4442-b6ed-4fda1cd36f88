/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/interactive-creation/page";
exports.ids = ["app/interactive-creation/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finteractive-creation%2Fpage&page=%2Finteractive-creation%2Fpage&appPaths=%2Finteractive-creation%2Fpage&pagePath=private-next-app-dir%2Finteractive-creation%2Fpage.tsx&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finteractive-creation%2Fpage&page=%2Finteractive-creation%2Fpage&appPaths=%2Finteractive-creation%2Fpage&pagePath=private-next-app-dir%2Finteractive-creation%2Fpage.tsx&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'interactive-creation',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/interactive-creation/page.tsx */ \"(rsc)/./src/app/interactive-creation/page.tsx\")), \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/home/<USER>/Desktop/proiecte/androidweb/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/interactive-creation/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/interactive-creation/page\",\n        pathname: \"/interactive-creation\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finteractive-creation%2Fpage&page=%2Finteractive-creation%2Fpage&appPaths=%2Finteractive-creation%2Fpage&pagePath=private-next-app-dir%2Finteractive-creation%2Fpage.tsx&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fcomponents%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fcomponents%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toaster.tsx */ \"(ssr)/./src/components/ui/toaster.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZjbGF1ZGl1JTJGRGVza3RvcCUyRnByb2llY3RlJTJGYW5kcm9pZHdlYiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZob21lJTJGY2xhdWRpdSUyRkRlc2t0b3AlMkZwcm9pZWN0ZSUyRmFuZHJvaWR3ZWIlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZob21lJTJGY2xhdWRpdSUyRkRlc2t0b3AlMkZwcm9pZWN0ZSUyRmFuZHJvaWR3ZWIlMkZzcmMlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlByb3ZpZGVycyUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZob21lJTJGY2xhdWRpdSUyRkRlc2t0b3AlMkZwcm9pZWN0ZSUyRmFuZHJvaWR3ZWIlMkZzcmMlMkZjb21wb25lbnRzJTJGdWklMkZ0b2FzdGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUEwSTtBQUMxSTtBQUNBLDBLQUF5SSIsInNvdXJjZXMiOlsid2VicGFjazovL2FuZHJvaWR3ZWItZW50ZXJwcmlzZS8/MjU3MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb3ZpZGVyc1wiXSAqLyBcIi9ob21lL2NsYXVkaXUvRGVza3RvcC9wcm9pZWN0ZS9hbmRyb2lkd2ViL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCIvaG9tZS9jbGF1ZGl1L0Rlc2t0b3AvcHJvaWVjdGUvYW5kcm9pZHdlYi9zcmMvY29tcG9uZW50cy91aS90b2FzdGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fcomponents%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp%2Finteractive-creation%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp%2Finteractive-creation%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/interactive-creation/page.tsx */ \"(ssr)/./src/app/interactive-creation/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZjbGF1ZGl1JTJGRGVza3RvcCUyRnByb2llY3RlJTJGYW5kcm9pZHdlYiUyRnNyYyUyRmFwcCUyRmludGVyYWN0aXZlLWNyZWF0aW9uJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBMQUFvSCIsInNvdXJjZXMiOlsid2VicGFjazovL2FuZHJvaWR3ZWItZW50ZXJwcmlzZS8/MGNlZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL2NsYXVkaXUvRGVza3RvcC9wcm9pZWN0ZS9hbmRyb2lkd2ViL3NyYy9hcHAvaW50ZXJhY3RpdmUtY3JlYXRpb24vcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp%2Finteractive-creation%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/interactive-creation/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/interactive-creation/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InteractiveCreationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction InteractiveCreationPage() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [livePreview, setLivePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentProject, setCurrentProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Mesaj de bun venit\n        const welcomeMessage = {\n            id: \"welcome\",\n            type: \"ai\",\n            content: \"Salut! Sunt AI-ul tău pentru crearea aplicațiilor mobile. Spune-mi ce vrei să construim \\xeempreună și voi genera codul \\xeen timp real pe măsură ce vorbim. Poți să-mi ceri să modific, să adaug funcții sau să optimizez orice parte din aplicație.\",\n            timestamp: new Date()\n        };\n        setMessages([\n            welcomeMessage\n        ]);\n        loadProjects();\n    }, []);\n    const loadProjects = async ()=>{\n        try {\n            const token = localStorage.getItem(\"accessToken\");\n            if (!token) return;\n            const response = await fetch(\"/api/projects\", {\n                headers: {\n                    \"Authorization\": `Bearer ${token}`\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setProjects(data.projects || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading projects:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isGenerating) return;\n        const userMessage = {\n            id: `user_${Date.now()}`,\n            type: \"user\",\n            content: inputMessage,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentInput = inputMessage;\n        setInputMessage(\"\");\n        setIsGenerating(true);\n        try {\n            // Apel REAL la AI prin API\n            const response = await fetch(\"/api/ai/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"accessToken\")}`\n                },\n                body: JSON.stringify({\n                    message: currentInput,\n                    context: \"mobile_app_development\",\n                    generateCode: true\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get AI response\");\n            }\n            const aiData = await response.json();\n            const aiMessage = {\n                id: `ai_${Date.now()}`,\n                type: \"ai\",\n                content: aiData.response || \"Am \\xeențeles cererea ta și lucrez la implementare.\",\n                timestamp: new Date(),\n                codeGenerated: aiData.generatedCode,\n                preview: aiData.preview || \"Cod generat pentru cererea ta\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n            if (aiData.generatedCode) {\n                updateLivePreview(aiData.generatedCode, aiData.platform || \"Android\");\n            }\n        } catch (error) {\n            console.error(\"AI Chat error:\", error);\n            // Fallback la răspuns local dacă AI-ul nu răspunde\n            const fallbackResponse = generateLocalResponse(currentInput);\n            const aiMessage = {\n                id: `ai_${Date.now()}`,\n                type: \"ai\",\n                content: fallbackResponse.content,\n                timestamp: new Date(),\n                codeGenerated: fallbackResponse.code,\n                preview: fallbackResponse.preview\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n            if (fallbackResponse.code) {\n                updateLivePreview(fallbackResponse.code, fallbackResponse.platform);\n            }\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const generateAIResponse = (userInput)=>{\n        const input = userInput.toLowerCase();\n        if (input.includes(\"login\") || input.includes(\"autentificare\")) {\n            return {\n                content: \"Perfect! Creez un sistem de login pentru tine. Voi genera un ecran de login cu validare și animații frumoase.\",\n                code: `// Login Screen Component\n@Composable\nfun LoginScreen() {\n    var email by remember { mutableStateOf(\"\") }\n    var password by remember { mutableStateOf(\"\") }\n    var isLoading by remember { mutableStateOf(false) }\n    \n    Column(\n        modifier = Modifier\n            .fillMaxSize()\n            .padding(24.dp),\n        horizontalAlignment = Alignment.CenterHorizontally,\n        verticalArrangement = Arrangement.Center\n    ) {\n        Text(\n            text = \"Welcome Back\",\n            style = MaterialTheme.typography.headlineLarge,\n            fontWeight = FontWeight.Bold\n        )\n        \n        Spacer(modifier = Modifier.height(32.dp))\n        \n        OutlinedTextField(\n            value = email,\n            onValueChange = { email = it },\n            label = { Text(\"Email\") },\n            modifier = Modifier.fillMaxWidth(),\n            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email)\n        )\n        \n        Spacer(modifier = Modifier.height(16.dp))\n        \n        OutlinedTextField(\n            value = password,\n            onValueChange = { password = it },\n            label = { Text(\"Password\") },\n            modifier = Modifier.fillMaxWidth(),\n            visualTransformation = PasswordVisualTransformation(),\n            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password)\n        )\n        \n        Spacer(modifier = Modifier.height(24.dp))\n        \n        Button(\n            onClick = { \n                isLoading = true\n                // Handle login logic\n            },\n            modifier = Modifier.fillMaxWidth(),\n            enabled = !isLoading\n        ) {\n            if (isLoading) {\n                CircularProgressIndicator(\n                    modifier = Modifier.size(16.dp),\n                    color = MaterialTheme.colorScheme.onPrimary\n                )\n            } else {\n                Text(\"Sign In\")\n            }\n        }\n    }\n}`,\n                platform: \"Android\",\n                preview: \"Login screen cu c\\xe2mpuri pentru email și parolă, buton de login cu indicator de \\xeencărcare\"\n            };\n        }\n        if (input.includes(\"dashboard\") || input.includes(\"home\")) {\n            return {\n                content: \"Creez un dashboard modern cu statistici și navigare rapidă. Voi adăuga carduri interactive și grafice.\",\n                code: `// Dashboard Screen\n@Composable\nfun DashboardScreen() {\n    LazyColumn(\n        modifier = Modifier.fillMaxSize(),\n        contentPadding = PaddingValues(16.dp),\n        verticalArrangement = Arrangement.spacedBy(16.dp)\n    ) {\n        item {\n            Text(\n                text = \"Dashboard\",\n                style = MaterialTheme.typography.headlineLarge,\n                fontWeight = FontWeight.Bold\n            )\n        }\n        \n        item {\n            LazyRow(\n                horizontalArrangement = Arrangement.spacedBy(12.dp)\n            ) {\n                items(3) { index ->\n                    StatsCard(\n                        title = when(index) {\n                            0 -> \"Total Users\"\n                            1 -> \"Revenue\"\n                            else -> \"Growth\"\n                        },\n                        value = when(index) {\n                            0 -> \"1,234\"\n                            1 -> \"$12,345\"\n                            else -> \"+23%\"\n                        },\n                        icon = when(index) {\n                            0 -> Icons.Default.People\n                            1 -> Icons.Default.AttachMoney\n                            else -> Icons.Default.TrendingUp\n                        }\n                    )\n                }\n            }\n        }\n        \n        item {\n            Card(\n                modifier = Modifier.fillMaxWidth()\n            ) {\n                Column(\n                    modifier = Modifier.padding(16.dp)\n                ) {\n                    Text(\n                        text = \"Recent Activity\",\n                        style = MaterialTheme.typography.titleMedium,\n                        fontWeight = FontWeight.SemiBold\n                    )\n                    Spacer(modifier = Modifier.height(12.dp))\n                    // Activity list here\n                }\n            }\n        }\n    }\n}\n\n@Composable\nfun StatsCard(title: String, value: String, icon: ImageVector) {\n    Card(\n        modifier = Modifier.width(120.dp)\n    ) {\n        Column(\n            modifier = Modifier.padding(12.dp),\n            horizontalAlignment = Alignment.CenterHorizontally\n        ) {\n            Icon(icon, contentDescription = null)\n            Spacer(modifier = Modifier.height(8.dp))\n            Text(value, fontWeight = FontWeight.Bold)\n            Text(title, fontSize = 12.sp)\n        }\n    }\n}`,\n                platform: \"Android\",\n                preview: \"Dashboard cu carduri de statistici, activitate recentă și navigare intuitivă\"\n            };\n        }\n        if (input.includes(\"chat\") || input.includes(\"mesaje\")) {\n            return {\n                content: \"Construiesc o interfață de chat \\xeen timp real cu bule de mesaje și funcții avansate.\",\n                code: `// Chat Screen\n@Composable\nfun ChatScreen() {\n    var messageText by remember { mutableStateOf(\"\") }\n    val messages = remember { mutableStateListOf<ChatMessage>() }\n    \n    Column(\n        modifier = Modifier.fillMaxSize()\n    ) {\n        // Chat messages\n        LazyColumn(\n            modifier = Modifier\n                .weight(1f)\n                .padding(horizontal = 16.dp),\n            reverseLayout = true\n        ) {\n            items(messages.reversed()) { message ->\n                ChatBubble(\n                    message = message,\n                    isCurrentUser = message.isFromCurrentUser\n                )\n            }\n        }\n        \n        // Message input\n        Row(\n            modifier = Modifier\n                .fillMaxWidth()\n                .padding(16.dp),\n            verticalAlignment = Alignment.CenterVertically\n        ) {\n            OutlinedTextField(\n                value = messageText,\n                onValueChange = { messageText = it },\n                modifier = Modifier.weight(1f),\n                placeholder = { Text(\"Type a message...\") },\n                shape = RoundedCornerShape(24.dp)\n            )\n            \n            Spacer(modifier = Modifier.width(8.dp))\n            \n            FloatingActionButton(\n                onClick = {\n                    if (messageText.isNotBlank()) {\n                        messages.add(\n                            ChatMessage(\n                                text = messageText,\n                                isFromCurrentUser = true,\n                                timestamp = System.currentTimeMillis()\n                            )\n                        )\n                        messageText = \"\"\n                    }\n                },\n                modifier = Modifier.size(48.dp)\n            ) {\n                Icon(Icons.Default.Send, contentDescription = \"Send\")\n            }\n        }\n    }\n}\n\n@Composable\nfun ChatBubble(message: ChatMessage, isCurrentUser: Boolean) {\n    Row(\n        modifier = Modifier\n            .fillMaxWidth()\n            .padding(vertical = 4.dp),\n        horizontalArrangement = if (isCurrentUser) Arrangement.End else Arrangement.Start\n    ) {\n        Card(\n            colors = CardDefaults.cardColors(\n                containerColor = if (isCurrentUser) \n                    MaterialTheme.colorScheme.primary \n                else \n                    MaterialTheme.colorScheme.surfaceVariant\n            ),\n            shape = RoundedCornerShape(\n                topStart = 16.dp,\n                topEnd = 16.dp,\n                bottomStart = if (isCurrentUser) 16.dp else 4.dp,\n                bottomEnd = if (isCurrentUser) 4.dp else 16.dp\n            )\n        ) {\n            Text(\n                text = message.text,\n                modifier = Modifier.padding(12.dp),\n                color = if (isCurrentUser) \n                    MaterialTheme.colorScheme.onPrimary \n                else \n                    MaterialTheme.colorScheme.onSurfaceVariant\n            )\n        }\n    }\n}`,\n                platform: \"Android\",\n                preview: \"Interfață de chat cu bule de mesaje, c\\xe2mp de input și buton de trimitere\"\n            };\n        }\n        // Răspuns generic\n        return {\n            content: `Înțeleg! Vrei să creez ${userInput}. Să încep să generez codul pentru această funcționalitate. Voi construi o implementare modernă și eficientă.`,\n            code: `// Generated code for: ${userInput}\n@Composable\nfun CustomComponent() {\n    // Implementation based on your request: ${userInput}\n    Column(\n        modifier = Modifier\n            .fillMaxSize()\n            .padding(16.dp)\n    ) {\n        Text(\n            text = \"Custom Feature: ${userInput}\",\n            style = MaterialTheme.typography.headlineMedium\n        )\n        \n        // Add more implementation here\n        Card(\n            modifier = Modifier\n                .fillMaxWidth()\n                .padding(vertical = 8.dp)\n        ) {\n            Text(\n                text = \"This feature is being implemented...\",\n                modifier = Modifier.padding(16.dp)\n            )\n        }\n    }\n}`,\n            platform: \"Android\",\n            preview: `Componentă personalizată pentru: ${userInput}`\n        };\n    };\n    const updateLivePreview = (code, platform)=>{\n        setLivePreview({\n            platform,\n            code: {\n                \"main.kt\": code\n            },\n            preview: \"Live preview updated with new code\"\n        });\n        setCurrentProject(`Project_${Date.now()}`);\n    };\n    // Fallback local pentru când AI-ul nu răspunde\n    const generateLocalResponse = (message)=>{\n        const lowerMessage = message.toLowerCase();\n        if (lowerMessage.includes(\"login\") || lowerMessage.includes(\"autentificare\")) {\n            return {\n                content: \"Creez o interfață de login cu autentificare securizată. Voi include c\\xe2mpuri pentru email/username și parolă, plus validare.\",\n                preview: \"Login Screen with authentication\",\n                platform: \"React Native\",\n                code: {\n                    \"LoginScreen.tsx\": `import React, { useState } from 'react';\nimport { View, Text, TextInput, TouchableOpacity, StyleSheet } from 'react-native';\n\nexport default function LoginScreen() {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n\n  const handleLogin = () => {\n    console.log('Login attempt:', { email, password });\n  };\n\n  return (\n    <View style={styles.container}>\n      <Text style={styles.title}>Welcome Back</Text>\n      <TextInput\n        style={styles.input}\n        placeholder=\"Email\"\n        value={email}\n        onChangeText={setEmail}\n        keyboardType=\"email-address\"\n      />\n      <TextInput\n        style={styles.input}\n        placeholder=\"Password\"\n        value={password}\n        onChangeText={setPassword}\n        secureTextEntry\n      />\n      <TouchableOpacity style={styles.button} onPress={handleLogin}>\n        <Text style={styles.buttonText}>Sign In</Text>\n      </TouchableOpacity>\n    </View>\n  );\n}`\n                }\n            };\n        } else if (lowerMessage.includes(\"dashboard\") || lowerMessage.includes(\"tablou\")) {\n            return {\n                content: \"Creez un dashboard cu metrici și date importante. Voi include carduri pentru statistici și grafice.\",\n                preview: \"Dashboard with metrics and data\",\n                platform: \"React Native\",\n                code: {\n                    \"Dashboard.tsx\": `import React from 'react';\nimport { View, Text, ScrollView, StyleSheet } from 'react-native';\n\nexport default function Dashboard() {\n  const stats = [\n    { title: 'Users', value: '1,247', color: '#4CAF50' },\n    { title: 'Revenue', value: '$12,890', color: '#2196F3' },\n  ];\n\n  return (\n    <ScrollView style={styles.container}>\n      <Text style={styles.title}>Dashboard</Text>\n      <View style={styles.statsGrid}>\n        {stats.map((stat, index) => (\n          <View key={index} style={styles.statCard}>\n            <Text style={styles.statValue}>{stat.value}</Text>\n            <Text style={styles.statTitle}>{stat.title}</Text>\n          </View>\n        ))}\n      </View>\n    </ScrollView>\n  );\n}`\n                }\n            };\n        } else {\n            return {\n                content: `Am înțeles cererea ta: \"${message}\". Lucrez la implementarea funcționalității cerute și voi genera codul corespunzător.`,\n                preview: \"Custom App\",\n                platform: \"React Native\",\n                code: {\n                    \"CustomApp.tsx\": `// Cod generat pentru: ${message}\nimport React from 'react';\nimport { View, Text, StyleSheet } from 'react-native';\n\nexport default function CustomApp() {\n  return (\n    <View style={styles.container}>\n      <Text style={styles.title}>Custom App</Text>\n      <Text style={styles.description}>\n        Aplicația ta personalizată\n      </Text>\n    </View>\n  );\n}`\n                }\n            };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Interactive Creation Studio\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"Creează aplicații mobile \\xeen timp real prin conversație cu AI-ul\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-[700px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Live Phone Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 17\n                                            }, this),\n                                            livePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                children: livePreview.platform\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-64 h-[500px] bg-gray-900 rounded-[2.5rem] p-2 shadow-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-full bg-white rounded-[2rem] overflow-hidden relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-6 bg-gray-100 flex items-center justify-between px-4 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"9:41\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-2 bg-green-500 rounded-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-2 bg-gray-300 rounded-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-2 bg-gray-300 rounded-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 p-4 h-[calc(100%-1.5rem)]\",\n                                                        children: livePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PhonePreview, {\n                                                            preview: livePreview.preview\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center h-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 614,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Start chatting to see your app\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 615,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 582,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-[700px] flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"AI Conversation\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 17\n                                            }, this),\n                                            isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                className: \"bg-blue-100 text-blue-800 animate-pulse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Generating...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"flex-1 flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 overflow-y-auto space-y-4 mb-4\",\n                                            children: [\n                                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `flex ${message.type === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `max-w-[80%] rounded-lg p-3 ${message.type === \"user\" ? \"bg-blue-600 text-white\" : \"bg-gray-100 text-gray-900\"}`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-1\",\n                                                                    children: [\n                                                                        message.type === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 656,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs opacity-75\",\n                                                                            children: message.timestamp.toLocaleTimeString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 660,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: message.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                message.codeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 p-2 bg-gray-900 rounded text-green-400 text-xs\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-3 w-3 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 667,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Code generated\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, message.id, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-start\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-100 rounded-lg p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                                            style: {\n                                                                                animationDelay: \"0.1s\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 681,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                                            style: {\n                                                                                animationDelay: \"0.2s\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 688,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    value: inputMessage,\n                                                    onChange: (e)=>setInputMessage(e.target.value),\n                                                    placeholder: \"Descrie ce vrei să construim...\",\n                                                    onKeyPress: (e)=>e.key === \"Enter\" && handleSendMessage(),\n                                                    disabled: isGenerating\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleSendMessage,\n                                                    disabled: !inputMessage.trim() || isGenerating,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-[700px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Generated Code & Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 720,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Test\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 724,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Export\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: livePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-900 rounded-lg p-4 h-64 overflow-y-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-green-400 text-xs\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        children: Object.values(livePreview.code)[0]\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3\",\n                                                        children: \"My Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 max-h-48 overflow-y-auto\",\n                                                        children: projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium text-sm\",\n                                                                                children: project.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                                lineNumber: 747,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: project.platform\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                                lineNumber: 748,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 746,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        className: \"text-xs\",\n                                                                        children: project.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 750,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, project.id, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-96\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: \"Start Creating\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"\\xcencepe o conversație cu AI-ul pentru a vedea codul generat \\xeen timp real\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 710,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                    lineNumber: 580,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n            lineNumber: 569,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n        lineNumber: 568,\n        columnNumber: 5\n    }, this);\n}\n// Componentă pentru preview-ul telefonului\nfunction PhonePreview({ preview }) {\n    if (preview.includes(\"Login\")) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold mb-4\",\n                        children: \"Welcome Back\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                        lineNumber: 783,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded p-2 text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 786,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 787,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                lineNumber: 785,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded p-2 text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 790,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: \"••••••••\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                lineNumber: 789,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full bg-blue-500 text-white py-2 rounded\",\n                                children: \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                lineNumber: 793,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                        lineNumber: 784,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                lineNumber: 782,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n            lineNumber: 781,\n            columnNumber: 7\n        }, this);\n    }\n    if (preview.includes(\"Dashboard\")) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-lg font-bold\",\n                    children: \"Dashboard\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                    lineNumber: 805,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-100 p-2 rounded text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-bold\",\n                                    children: \"1,234\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 808,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"Users\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 809,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 807,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-100 p-2 rounded text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-bold\",\n                                    children: \"$12K\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 812,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"Revenue\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 813,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 811,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                    lineNumber: 806,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-100 p-3 rounded\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium mb-2\",\n                            children: \"Recent Activity\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 817,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"• User registered\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"• Payment received\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 820,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"• New order\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 818,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                    lineNumber: 816,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n            lineNumber: 804,\n            columnNumber: 7\n        }, this);\n    }\n    if (preview.includes(\"Chat\")) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 space-y-2 mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-500 text-white p-2 rounded-lg text-xs max-w-[80%]\",\n                                children: \"Hello there!\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                lineNumber: 833,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 832,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-200 p-2 rounded-lg text-xs max-w-[80%]\",\n                                children: \"Hi! How can I help you?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 837,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-500 text-white p-2 rounded-lg text-xs max-w-[80%]\",\n                                children: \"Great app!\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                    lineNumber: 831,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 border rounded p-1 text-xs\",\n                            children: \"Type a message...\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-blue-500 text-white p-1 rounded\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                lineNumber: 851,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 850,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                    lineNumber: 848,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n            lineNumber: 830,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold mb-2\",\n                        children: \"Custom App\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                        lineNumber: 861,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: preview\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                        lineNumber: 862,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                lineNumber: 860,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 p-4 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-sm\",\n                    children: \"App Preview\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                    lineNumber: 865,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                lineNumber: 864,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n        lineNumber: 859,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/interactive-creation/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n// Temporary simplified providers until we install all dependencies\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/providers.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFJQSxtRUFBbUU7QUFDNUQsU0FBU0EsVUFBVSxFQUFFQyxRQUFRLEVBQWlDO0lBQ25FLHFCQUNFLDhEQUFDQztrQkFDRUQ7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5kcm9pZHdlYi1lbnRlcnByaXNlLy4vc3JjL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeD9iZTg3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuXG4vLyBUZW1wb3Jhcnkgc2ltcGxpZmllZCBwcm92aWRlcnMgdW50aWwgd2UgaW5zdGFsbCBhbGwgZGVwZW5kZW5jaWVzXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJkaXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL2FuZHJvaWR3ZWItZW50ZXJwcmlzZS8uL3NyYy9jb21wb25lbnRzL3VpL2lucHV0LnRzeD9jOTgzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXG4gIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7fVxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toast.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toast.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toast.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toast.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toast.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./src/components/ui/toast.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-toast.ts":
/*!********************************!*\
  !*** ./src/hooks/use-toast.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ \nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   camelToKebab: () => (/* binding */ camelToKebab),\n/* harmony export */   capitalize: () => (/* binding */ capitalize),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   downloadFile: () => (/* binding */ downloadFile),\n/* harmony export */   formatBytes: () => (/* binding */ formatBytes),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidUrl: () => (/* binding */ isValidUrl),\n/* harmony export */   kebabToCamel: () => (/* binding */ kebabToCamel),\n/* harmony export */   randomBetween: () => (/* binding */ randomBetween),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncate: () => (/* binding */ truncate)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        month: \"long\",\n        day: \"numeric\",\n        year: \"numeric\"\n    }).format(new Date(date));\n}\nfunction formatCurrency(amount, currency = \"USD\") {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency\n    }).format(amount / 100) // Stripe amounts are in cents\n    ;\n}\nfunction formatBytes(bytes, decimals = 2) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const dm = decimals < 0 ? 0 : decimals;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\",\n        \"TB\",\n        \"PB\",\n        \"EB\",\n        \"ZB\",\n        \"YB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + \" \" + sizes[i];\n}\nfunction slugify(str) {\n    return str.toLowerCase().replace(/[^\\w\\s-]/g, \"\").replace(/[\\s_-]+/g, \"-\").replace(/^-+|-+$/g, \"\");\n}\nfunction truncate(str, length) {\n    return str.length > length ? `${str.substring(0, length)}...` : str;\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((n)=>n[0]).join(\"\").toUpperCase().slice(0, 2);\n}\nfunction generateId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\nfunction debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction isValidUrl(url) {\n    try {\n        new URL(url);\n        return true;\n    } catch  {\n        return false;\n    }\n}\nfunction copyToClipboard(text) {\n    if (navigator.clipboard) {\n        return navigator.clipboard.writeText(text);\n    } else {\n        // Fallback for older browsers\n        const textArea = document.createElement(\"textarea\");\n        textArea.value = text;\n        document.body.appendChild(textArea);\n        textArea.focus();\n        textArea.select();\n        try {\n            document.execCommand(\"copy\");\n        } catch (err) {\n            console.error(\"Failed to copy text: \", err);\n        }\n        document.body.removeChild(textArea);\n        return Promise.resolve();\n    }\n}\nfunction downloadFile(data, filename, type = \"text/plain\") {\n    const blob = new Blob([\n        data\n    ], {\n        type\n    });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    window.URL.revokeObjectURL(url);\n}\nfunction getErrorMessage(error) {\n    if (error instanceof Error) return error.message;\n    return String(error);\n}\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\nfunction randomBetween(min, max) {\n    return Math.floor(Math.random() * (max - min + 1)) + min;\n}\nfunction capitalize(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\nfunction camelToKebab(str) {\n    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, \"$1-$2\").toLowerCase();\n}\nfunction kebabToCamel(str) {\n    return str.replace(/-([a-z])/g, (g)=>g[1].toUpperCase());\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f718ef6e11d4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5kcm9pZHdlYi1lbnRlcnByaXNlLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz81MWFlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjcxOGVmNmUxMWQ0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/interactive-creation/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/interactive-creation/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./src/components/ui/toaster.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"AndroidWeb Enterprise - AI Mobile Developer Platform\",\n    description: \"Create mobile applications through AI conversation. Enterprise-grade platform for Android and iOS development.\",\n    keywords: [\n        \"AI\",\n        \"mobile development\",\n        \"Android\",\n        \"iOS\",\n        \"enterprise\",\n        \"automation\"\n    ],\n    authors: [\n        {\n            name: \"AndroidWeb Enterprise Team\"\n        }\n    ],\n    creator: \"AndroidWeb Enterprise\",\n    publisher: \"AndroidWeb Enterprise\",\n    robots: {\n        index: true,\n        follow: true\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"https://androidweb-enterprise.com\",\n        title: \"AndroidWeb Enterprise - AI Mobile Developer Platform\",\n        description: \"Create mobile applications through AI conversation. Enterprise-grade platform for Android and iOS development.\",\n        siteName: \"AndroidWeb Enterprise\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"AndroidWeb Enterprise - AI Mobile Developer Platform\",\n        description: \"Create mobile applications through AI conversation. Enterprise-grade platform for Android and iOS development.\",\n        creator: \"@androidweb\"\n    },\n    viewport: {\n        width: \"device-width\",\n        initialScale: 1,\n        maximumScale: 1\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/layout.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/layout.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/layout.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/proiecte/androidweb/src/components/providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/proiecte/androidweb/src/components/ui/toaster.tsx#Toaster`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finteractive-creation%2Fpage&page=%2Finteractive-creation%2Fpage&appPaths=%2Finteractive-creation%2Fpage&pagePath=private-next-app-dir%2Finteractive-creation%2Fpage.tsx&appDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();