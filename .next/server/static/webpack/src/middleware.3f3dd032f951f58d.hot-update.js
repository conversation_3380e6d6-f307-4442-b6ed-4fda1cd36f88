"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n// Simplified middleware for now - will add full auth later\nfunction extractTokenFromHeader(authHeader) {\n    if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n        return null;\n    }\n    return authHeader.substring(7);\n}\nfunction canAccessRoute(userRole, route) {\n    // Simplified route access check\n    const adminRoutes = [\n        \"/admin\",\n        \"/super-admin\"\n    ];\n    if (adminRoutes.some((r)=>route.startsWith(r))) {\n        return userRole === \"ADMIN\" || userRole === \"SUPER_ADMIN\";\n    }\n    return true;\n}\n// Define route patterns\nconst AUTH_ROUTES = [\n    \"/login\",\n    \"/register\",\n    \"/forgot-password\",\n    \"/reset-password\"\n];\nconst PUBLIC_ROUTES = [\n    \"/\",\n    \"/pricing\",\n    \"/features\",\n    \"/contact\",\n    \"/terms\",\n    \"/privacy\"\n];\nconst API_PUBLIC_ROUTES = [\n    \"/api/auth/login\",\n    \"/api/auth/register\",\n    \"/api/auth/forgot-password\",\n    \"/api/auth/reset-password\"\n];\nconst ADMIN_ROUTES = [\n    \"/admin\"\n];\nconst SUPER_ADMIN_ROUTES = [\n    \"/super-admin\"\n];\n// Rate limiting configuration\nconst RATE_LIMIT_CONFIG = {\n    \"/api/auth/login\": {\n        requests: 5,\n        window: 15 * 60 * 1000\n    },\n    \"/api/auth/register\": {\n        requests: 3,\n        window: 60 * 60 * 1000\n    },\n    \"/api/auth/forgot-password\": {\n        requests: 3,\n        window: 60 * 60 * 1000\n    },\n    \"/api/\": {\n        requests: 100,\n        window: 60 * 1000\n    },\n    default: {\n        requests: 1000,\n        window: 60 * 1000\n    }\n};\n// In-memory rate limiting store (in production, use Redis)\nconst rateLimitStore = new Map();\nfunction isPublicRoute(pathname) {\n    return PUBLIC_ROUTES.includes(pathname) || pathname.startsWith(\"/api/public\") || API_PUBLIC_ROUTES.includes(pathname) || pathname.startsWith(\"/_next\") || pathname.startsWith(\"/favicon\") || pathname.includes(\".\");\n}\nfunction isAuthRoute(pathname) {\n    return AUTH_ROUTES.some((route)=>pathname.startsWith(route));\n}\nfunction isProtectedRoute(pathname) {\n    return pathname.startsWith(\"/dashboard\") || pathname.startsWith(\"/workspace\") || pathname.startsWith(\"/profile\") || pathname.startsWith(\"/settings\") || pathname.startsWith(\"/api/protected\") || ADMIN_ROUTES.some((route)=>pathname.startsWith(route)) || SUPER_ADMIN_ROUTES.some((route)=>pathname.startsWith(route));\n}\nfunction getRateLimitKey(request) {\n    const ip = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n    const pathname = request.nextUrl.pathname;\n    return `${ip}:${pathname}`;\n}\nfunction checkRateLimit(request) {\n    const key = getRateLimitKey(request);\n    const pathname = request.nextUrl.pathname;\n    // Find matching rate limit config\n    let config = RATE_LIMIT_CONFIG.default;\n    for (const [pattern, patternConfig] of Object.entries(RATE_LIMIT_CONFIG)){\n        if (pattern !== \"default\" && pathname.startsWith(pattern)) {\n            config = patternConfig;\n            break;\n        }\n    }\n    const now = Date.now();\n    const windowStart = now - config.window;\n    // Get or create rate limit entry\n    let entry = rateLimitStore.get(key);\n    if (!entry || entry.resetTime < windowStart) {\n        entry = {\n            count: 0,\n            resetTime: now + config.window\n        };\n        rateLimitStore.set(key, entry);\n    }\n    // Check if limit exceeded\n    if (entry.count >= config.requests) {\n        return {\n            allowed: false,\n            remaining: 0,\n            resetTime: entry.resetTime\n        };\n    }\n    // Increment counter\n    entry.count++;\n    rateLimitStore.set(key, entry);\n    return {\n        allowed: true,\n        remaining: config.requests - entry.count,\n        resetTime: entry.resetTime\n    };\n}\nfunction addSecurityHeaders(response) {\n    // Security headers\n    response.headers.set(\"X-Content-Type-Options\", \"nosniff\");\n    response.headers.set(\"X-Frame-Options\", \"DENY\");\n    response.headers.set(\"X-XSS-Protection\", \"1; mode=block\");\n    response.headers.set(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n    response.headers.set(\"Permissions-Policy\", \"camera=(), microphone=(), geolocation=()\");\n    // HSTS (only in production)\n    if (false) {}\n    return response;\n}\nfunction middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Check rate limiting\n    const rateLimit = checkRateLimit(request);\n    if (!rateLimit.allowed) {\n        const response = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: \"Too many requests\",\n            retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)\n        }), {\n            status: 429,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Retry-After\": Math.ceil((rateLimit.resetTime - Date.now()) / 1000).toString(),\n                \"X-RateLimit-Limit\": RATE_LIMIT_CONFIG.default.requests.toString(),\n                \"X-RateLimit-Remaining\": rateLimit.remaining.toString(),\n                \"X-RateLimit-Reset\": rateLimit.resetTime.toString()\n            }\n        });\n        return addSecurityHeaders(response);\n    }\n    // Add rate limit headers to response\n    const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    response.headers.set(\"X-RateLimit-Limit\", RATE_LIMIT_CONFIG.default.requests.toString());\n    response.headers.set(\"X-RateLimit-Remaining\", rateLimit.remaining.toString());\n    response.headers.set(\"X-RateLimit-Reset\", rateLimit.resetTime.toString());\n    // Skip middleware for public routes\n    if (isPublicRoute(pathname)) {\n        return addSecurityHeaders(response);\n    }\n    // Get token from header or cookie\n    const authHeader = request.headers.get(\"authorization\");\n    const token = extractTokenFromHeader(authHeader) || request.cookies.get(\"auth-token\")?.value;\n    // Verify token for protected routes\n    if (isProtectedRoute(pathname)) {\n        if (!token) {\n            if (pathname.startsWith(\"/api/\")) {\n                return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n                    error: \"Authentication required\"\n                }), {\n                    status: 401,\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/login\", request.url));\n        }\n        // For now, skip token verification - will implement later\n        const payload = {\n            role: \"USER\",\n            userId: \"temp\"\n        };\n        // const payload = verifyAccessToken(token)\n        // if (!payload) {\n        //   if (pathname.startsWith('/api/')) {\n        //     return new NextResponse(\n        //       JSON.stringify({ error: 'Invalid or expired token' }),\n        //       { status: 401, headers: { 'Content-Type': 'application/json' } }\n        //     )\n        //   }\n        //   return NextResponse.redirect(new URL('/login', request.url))\n        // }\n        // Check role-based access\n        if (!canAccessRoute(payload.role, pathname)) {\n            if (pathname.startsWith(\"/api/\")) {\n                return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n                    error: \"Insufficient permissions\"\n                }), {\n                    status: 403,\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/unauthorized\", request.url));\n        }\n        // Add user info to headers for API routes\n        if (pathname.startsWith(\"/api/\")) {\n            response.headers.set(\"X-User-Id\", payload.userId);\n            response.headers.set(\"X-User-Role\", payload.role);\n            response.headers.set(\"X-User-Subscription\", payload.subscriptionTier);\n        }\n    }\n    // Redirect authenticated users away from auth routes\n    if (isAuthRoute(pathname) && token) {\n        const payload = verifyAccessToken(token);\n        if (payload) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        }\n    }\n    return addSecurityHeaders(response);\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */ \"/((?!_next/static|_next/image|favicon.ico).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL21pZGRsZXdhcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVEO0FBRXZELDJEQUEyRDtBQUMzRCxTQUFTQyx1QkFBdUJDLFVBQXlCO0lBQ3ZELElBQUksQ0FBQ0EsY0FBYyxDQUFDQSxXQUFXQyxVQUFVLENBQUMsWUFBWTtRQUNwRCxPQUFPO0lBQ1Q7SUFDQSxPQUFPRCxXQUFXRSxTQUFTLENBQUM7QUFDOUI7QUFFQSxTQUFTQyxlQUFlQyxRQUFnQixFQUFFQyxLQUFhO0lBQ3JELGdDQUFnQztJQUNoQyxNQUFNQyxjQUFjO1FBQUM7UUFBVTtLQUFlO0lBQzlDLElBQUlBLFlBQVlDLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0gsTUFBTUosVUFBVSxDQUFDTyxLQUFLO1FBQzlDLE9BQU9KLGFBQWEsV0FBV0EsYUFBYTtJQUM5QztJQUNBLE9BQU87QUFDVDtBQUVBLHdCQUF3QjtBQUN4QixNQUFNSyxjQUFjO0lBQUM7SUFBVTtJQUFhO0lBQW9CO0NBQWtCO0FBQ2xGLE1BQU1DLGdCQUFnQjtJQUFDO0lBQUs7SUFBWTtJQUFhO0lBQVk7SUFBVTtDQUFXO0FBQ3RGLE1BQU1DLG9CQUFvQjtJQUFDO0lBQW1CO0lBQXNCO0lBQTZCO0NBQTJCO0FBQzVILE1BQU1DLGVBQWU7SUFBQztDQUFTO0FBQy9CLE1BQU1DLHFCQUFxQjtJQUFDO0NBQWU7QUFFM0MsOEJBQThCO0FBQzlCLE1BQU1DLG9CQUFvQjtJQUN4QixtQkFBbUI7UUFBRUMsVUFBVTtRQUFHQyxRQUFRLEtBQUssS0FBSztJQUFLO0lBQ3pELHNCQUFzQjtRQUFFRCxVQUFVO1FBQUdDLFFBQVEsS0FBSyxLQUFLO0lBQUs7SUFDNUQsNkJBQTZCO1FBQUVELFVBQVU7UUFBR0MsUUFBUSxLQUFLLEtBQUs7SUFBSztJQUNuRSxTQUFTO1FBQUVELFVBQVU7UUFBS0MsUUFBUSxLQUFLO0lBQUs7SUFDNUNDLFNBQVM7UUFBRUYsVUFBVTtRQUFNQyxRQUFRLEtBQUs7SUFBSztBQUMvQztBQUVBLDJEQUEyRDtBQUMzRCxNQUFNRSxpQkFBaUIsSUFBSUM7QUFFM0IsU0FBU0MsY0FBY0MsUUFBZ0I7SUFDckMsT0FBT1gsY0FBY1ksUUFBUSxDQUFDRCxhQUN2QkEsU0FBU3BCLFVBQVUsQ0FBQyxrQkFDcEJVLGtCQUFrQlcsUUFBUSxDQUFDRCxhQUMzQkEsU0FBU3BCLFVBQVUsQ0FBQyxhQUNwQm9CLFNBQVNwQixVQUFVLENBQUMsZUFDcEJvQixTQUFTQyxRQUFRLENBQUM7QUFDM0I7QUFFQSxTQUFTQyxZQUFZRixRQUFnQjtJQUNuQyxPQUFPWixZQUFZRixJQUFJLENBQUNGLENBQUFBLFFBQVNnQixTQUFTcEIsVUFBVSxDQUFDSTtBQUN2RDtBQUVBLFNBQVNtQixpQkFBaUJILFFBQWdCO0lBQ3hDLE9BQU9BLFNBQVNwQixVQUFVLENBQUMsaUJBQ3BCb0IsU0FBU3BCLFVBQVUsQ0FBQyxpQkFDcEJvQixTQUFTcEIsVUFBVSxDQUFDLGVBQ3BCb0IsU0FBU3BCLFVBQVUsQ0FBQyxnQkFDcEJvQixTQUFTcEIsVUFBVSxDQUFDLHFCQUNwQlcsYUFBYUwsSUFBSSxDQUFDRixDQUFBQSxRQUFTZ0IsU0FBU3BCLFVBQVUsQ0FBQ0ksV0FDL0NRLG1CQUFtQk4sSUFBSSxDQUFDRixDQUFBQSxRQUFTZ0IsU0FBU3BCLFVBQVUsQ0FBQ0k7QUFDOUQ7QUFFQSxTQUFTb0IsZ0JBQWdCQyxPQUFvQjtJQUMzQyxNQUFNQyxLQUFLRCxRQUFRQyxFQUFFLElBQUlELFFBQVFFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNCQUFzQjtJQUNuRSxNQUFNUixXQUFXSyxRQUFRSSxPQUFPLENBQUNULFFBQVE7SUFDekMsT0FBTyxDQUFDLEVBQUVNLEdBQUcsQ0FBQyxFQUFFTixTQUFTLENBQUM7QUFDNUI7QUFFQSxTQUFTVSxlQUFlTCxPQUFvQjtJQUMxQyxNQUFNTSxNQUFNUCxnQkFBZ0JDO0lBQzVCLE1BQU1MLFdBQVdLLFFBQVFJLE9BQU8sQ0FBQ1QsUUFBUTtJQUV6QyxrQ0FBa0M7SUFDbEMsSUFBSVksU0FBU25CLGtCQUFrQkcsT0FBTztJQUN0QyxLQUFLLE1BQU0sQ0FBQ2lCLFNBQVNDLGNBQWMsSUFBSUMsT0FBT0MsT0FBTyxDQUFDdkIsbUJBQW9CO1FBQ3hFLElBQUlvQixZQUFZLGFBQWFiLFNBQVNwQixVQUFVLENBQUNpQyxVQUFVO1lBQ3pERCxTQUFTRTtZQUNUO1FBQ0Y7SUFDRjtJQUVBLE1BQU1HLE1BQU1DLEtBQUtELEdBQUc7SUFDcEIsTUFBTUUsY0FBY0YsTUFBTUwsT0FBT2pCLE1BQU07SUFFdkMsaUNBQWlDO0lBQ2pDLElBQUl5QixRQUFRdkIsZUFBZVcsR0FBRyxDQUFDRztJQUMvQixJQUFJLENBQUNTLFNBQVNBLE1BQU1DLFNBQVMsR0FBR0YsYUFBYTtRQUMzQ0MsUUFBUTtZQUFFRSxPQUFPO1lBQUdELFdBQVdKLE1BQU1MLE9BQU9qQixNQUFNO1FBQUM7UUFDbkRFLGVBQWUwQixHQUFHLENBQUNaLEtBQUtTO0lBQzFCO0lBRUEsMEJBQTBCO0lBQzFCLElBQUlBLE1BQU1FLEtBQUssSUFBSVYsT0FBT2xCLFFBQVEsRUFBRTtRQUNsQyxPQUFPO1lBQUU4QixTQUFTO1lBQU9DLFdBQVc7WUFBR0osV0FBV0QsTUFBTUMsU0FBUztRQUFDO0lBQ3BFO0lBRUEsb0JBQW9CO0lBQ3BCRCxNQUFNRSxLQUFLO0lBQ1h6QixlQUFlMEIsR0FBRyxDQUFDWixLQUFLUztJQUV4QixPQUFPO1FBQ0xJLFNBQVM7UUFDVEMsV0FBV2IsT0FBT2xCLFFBQVEsR0FBRzBCLE1BQU1FLEtBQUs7UUFDeENELFdBQVdELE1BQU1DLFNBQVM7SUFDNUI7QUFDRjtBQUVBLFNBQVNLLG1CQUFtQkMsUUFBc0I7SUFDaEQsbUJBQW1CO0lBQ25CQSxTQUFTcEIsT0FBTyxDQUFDZ0IsR0FBRyxDQUFDLDBCQUEwQjtJQUMvQ0ksU0FBU3BCLE9BQU8sQ0FBQ2dCLEdBQUcsQ0FBQyxtQkFBbUI7SUFDeENJLFNBQVNwQixPQUFPLENBQUNnQixHQUFHLENBQUMsb0JBQW9CO0lBQ3pDSSxTQUFTcEIsT0FBTyxDQUFDZ0IsR0FBRyxDQUFDLG1CQUFtQjtJQUN4Q0ksU0FBU3BCLE9BQU8sQ0FBQ2dCLEdBQUcsQ0FBQyxzQkFBc0I7SUFFM0MsNEJBQTRCO0lBQzVCLElBQUlLLEtBQXlCLEVBQWMsRUFFMUM7SUFFRCxPQUFPRDtBQUNUO0FBRU8sU0FBU0UsV0FBV3hCLE9BQW9CO0lBQzdDLE1BQU0sRUFBRUwsUUFBUSxFQUFFLEdBQUdLLFFBQVFJLE9BQU87SUFFcEMsc0JBQXNCO0lBQ3RCLE1BQU1xQixZQUFZcEIsZUFBZUw7SUFDakMsSUFBSSxDQUFDeUIsVUFBVU4sT0FBTyxFQUFFO1FBQ3RCLE1BQU1HLFdBQVcsSUFBSWxELHFEQUFZQSxDQUMvQnNELEtBQUtDLFNBQVMsQ0FBQztZQUNiQyxPQUFPO1lBQ1BDLFlBQVlDLEtBQUtDLElBQUksQ0FBQyxDQUFDTixVQUFVVCxTQUFTLEdBQUdILEtBQUtELEdBQUcsRUFBQyxJQUFLO1FBQzdELElBQ0E7WUFDRW9CLFFBQVE7WUFDUjlCLFNBQVM7Z0JBQ1AsZ0JBQWdCO2dCQUNoQixlQUFlNEIsS0FBS0MsSUFBSSxDQUFDLENBQUNOLFVBQVVULFNBQVMsR0FBR0gsS0FBS0QsR0FBRyxFQUFDLElBQUssTUFBTXFCLFFBQVE7Z0JBQzVFLHFCQUFxQjdDLGtCQUFrQkcsT0FBTyxDQUFDRixRQUFRLENBQUM0QyxRQUFRO2dCQUNoRSx5QkFBeUJSLFVBQVVMLFNBQVMsQ0FBQ2EsUUFBUTtnQkFDckQscUJBQXFCUixVQUFVVCxTQUFTLENBQUNpQixRQUFRO1lBQ25EO1FBQ0Y7UUFFRixPQUFPWixtQkFBbUJDO0lBQzVCO0lBRUEscUNBQXFDO0lBQ3JDLE1BQU1BLFdBQVdsRCxxREFBWUEsQ0FBQzhELElBQUk7SUFDbENaLFNBQVNwQixPQUFPLENBQUNnQixHQUFHLENBQUMscUJBQXFCOUIsa0JBQWtCRyxPQUFPLENBQUNGLFFBQVEsQ0FBQzRDLFFBQVE7SUFDckZYLFNBQVNwQixPQUFPLENBQUNnQixHQUFHLENBQUMseUJBQXlCTyxVQUFVTCxTQUFTLENBQUNhLFFBQVE7SUFDMUVYLFNBQVNwQixPQUFPLENBQUNnQixHQUFHLENBQUMscUJBQXFCTyxVQUFVVCxTQUFTLENBQUNpQixRQUFRO0lBRXRFLG9DQUFvQztJQUNwQyxJQUFJdkMsY0FBY0MsV0FBVztRQUMzQixPQUFPMEIsbUJBQW1CQztJQUM1QjtJQUVBLGtDQUFrQztJQUNsQyxNQUFNaEQsYUFBYTBCLFFBQVFFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDO0lBQ3ZDLE1BQU1nQyxRQUFROUQsdUJBQXVCQyxlQUFlMEIsUUFBUW9DLE9BQU8sQ0FBQ2pDLEdBQUcsQ0FBQyxlQUFla0M7SUFFdkYsb0NBQW9DO0lBQ3BDLElBQUl2QyxpQkFBaUJILFdBQVc7UUFDOUIsSUFBSSxDQUFDd0MsT0FBTztZQUNWLElBQUl4QyxTQUFTcEIsVUFBVSxDQUFDLFVBQVU7Z0JBQ2hDLE9BQU8sSUFBSUgscURBQVlBLENBQ3JCc0QsS0FBS0MsU0FBUyxDQUFDO29CQUFFQyxPQUFPO2dCQUEwQixJQUNsRDtvQkFBRUksUUFBUTtvQkFBSzlCLFNBQVM7d0JBQUUsZ0JBQWdCO29CQUFtQjtnQkFBRTtZQUVuRTtZQUNBLE9BQU85QixxREFBWUEsQ0FBQ2tFLFFBQVEsQ0FBQyxJQUFJQyxJQUFJLFVBQVV2QyxRQUFRd0MsR0FBRztRQUM1RDtRQUVBLDBEQUEwRDtRQUMxRCxNQUFNQyxVQUFVO1lBQUVDLE1BQU07WUFBUUMsUUFBUTtRQUFPO1FBQy9DLDJDQUEyQztRQUMzQyxrQkFBa0I7UUFDbEIsd0NBQXdDO1FBQ3hDLCtCQUErQjtRQUMvQiwrREFBK0Q7UUFDL0QseUVBQXlFO1FBQ3pFLFFBQVE7UUFDUixNQUFNO1FBQ04saUVBQWlFO1FBQ2pFLElBQUk7UUFFSiwwQkFBMEI7UUFDMUIsSUFBSSxDQUFDbEUsZUFBZWdFLFFBQVFDLElBQUksRUFBRS9DLFdBQVc7WUFDM0MsSUFBSUEsU0FBU3BCLFVBQVUsQ0FBQyxVQUFVO2dCQUNoQyxPQUFPLElBQUlILHFEQUFZQSxDQUNyQnNELEtBQUtDLFNBQVMsQ0FBQztvQkFBRUMsT0FBTztnQkFBMkIsSUFDbkQ7b0JBQUVJLFFBQVE7b0JBQUs5QixTQUFTO3dCQUFFLGdCQUFnQjtvQkFBbUI7Z0JBQUU7WUFFbkU7WUFDQSxPQUFPOUIscURBQVlBLENBQUNrRSxRQUFRLENBQUMsSUFBSUMsSUFBSSxpQkFBaUJ2QyxRQUFRd0MsR0FBRztRQUNuRTtRQUVBLDBDQUEwQztRQUMxQyxJQUFJN0MsU0FBU3BCLFVBQVUsQ0FBQyxVQUFVO1lBQ2hDK0MsU0FBU3BCLE9BQU8sQ0FBQ2dCLEdBQUcsQ0FBQyxhQUFhdUIsUUFBUUUsTUFBTTtZQUNoRHJCLFNBQVNwQixPQUFPLENBQUNnQixHQUFHLENBQUMsZUFBZXVCLFFBQVFDLElBQUk7WUFDaERwQixTQUFTcEIsT0FBTyxDQUFDZ0IsR0FBRyxDQUFDLHVCQUF1QnVCLFFBQVFHLGdCQUFnQjtRQUN0RTtJQUNGO0lBRUEscURBQXFEO0lBQ3JELElBQUkvQyxZQUFZRixhQUFhd0MsT0FBTztRQUNsQyxNQUFNTSxVQUFVSSxrQkFBa0JWO1FBQ2xDLElBQUlNLFNBQVM7WUFDWCxPQUFPckUscURBQVlBLENBQUNrRSxRQUFRLENBQUMsSUFBSUMsSUFBSSxjQUFjdkMsUUFBUXdDLEdBQUc7UUFDaEU7SUFDRjtJQUVBLE9BQU9uQixtQkFBbUJDO0FBQzVCO0FBRU8sTUFBTWYsU0FBUztJQUNwQnVDLFNBQVM7UUFDUDs7Ozs7S0FLQyxHQUNEO0tBQ0Q7QUFDSCxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9taWRkbGV3YXJlLnRzP2QxOTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuXG4vLyBTaW1wbGlmaWVkIG1pZGRsZXdhcmUgZm9yIG5vdyAtIHdpbGwgYWRkIGZ1bGwgYXV0aCBsYXRlclxuZnVuY3Rpb24gZXh0cmFjdFRva2VuRnJvbUhlYWRlcihhdXRoSGVhZGVyOiBzdHJpbmcgfCBudWxsKTogc3RyaW5nIHwgbnVsbCB7XG4gIGlmICghYXV0aEhlYWRlciB8fCAhYXV0aEhlYWRlci5zdGFydHNXaXRoKCdCZWFyZXIgJykpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG4gIHJldHVybiBhdXRoSGVhZGVyLnN1YnN0cmluZyg3KVxufVxuXG5mdW5jdGlvbiBjYW5BY2Nlc3NSb3V0ZSh1c2VyUm9sZTogc3RyaW5nLCByb3V0ZTogc3RyaW5nKTogYm9vbGVhbiB7XG4gIC8vIFNpbXBsaWZpZWQgcm91dGUgYWNjZXNzIGNoZWNrXG4gIGNvbnN0IGFkbWluUm91dGVzID0gWycvYWRtaW4nLCAnL3N1cGVyLWFkbWluJ11cbiAgaWYgKGFkbWluUm91dGVzLnNvbWUociA9PiByb3V0ZS5zdGFydHNXaXRoKHIpKSkge1xuICAgIHJldHVybiB1c2VyUm9sZSA9PT0gJ0FETUlOJyB8fCB1c2VyUm9sZSA9PT0gJ1NVUEVSX0FETUlOJ1xuICB9XG4gIHJldHVybiB0cnVlXG59XG5cbi8vIERlZmluZSByb3V0ZSBwYXR0ZXJuc1xuY29uc3QgQVVUSF9ST1VURVMgPSBbJy9sb2dpbicsICcvcmVnaXN0ZXInLCAnL2ZvcmdvdC1wYXNzd29yZCcsICcvcmVzZXQtcGFzc3dvcmQnXVxuY29uc3QgUFVCTElDX1JPVVRFUyA9IFsnLycsICcvcHJpY2luZycsICcvZmVhdHVyZXMnLCAnL2NvbnRhY3QnLCAnL3Rlcm1zJywgJy9wcml2YWN5J11cbmNvbnN0IEFQSV9QVUJMSUNfUk9VVEVTID0gWycvYXBpL2F1dGgvbG9naW4nLCAnL2FwaS9hdXRoL3JlZ2lzdGVyJywgJy9hcGkvYXV0aC9mb3Jnb3QtcGFzc3dvcmQnLCAnL2FwaS9hdXRoL3Jlc2V0LXBhc3N3b3JkJ11cbmNvbnN0IEFETUlOX1JPVVRFUyA9IFsnL2FkbWluJ11cbmNvbnN0IFNVUEVSX0FETUlOX1JPVVRFUyA9IFsnL3N1cGVyLWFkbWluJ11cblxuLy8gUmF0ZSBsaW1pdGluZyBjb25maWd1cmF0aW9uXG5jb25zdCBSQVRFX0xJTUlUX0NPTkZJRyA9IHtcbiAgJy9hcGkvYXV0aC9sb2dpbic6IHsgcmVxdWVzdHM6IDUsIHdpbmRvdzogMTUgKiA2MCAqIDEwMDAgfSwgLy8gNSByZXF1ZXN0cyBwZXIgMTUgbWludXRlc1xuICAnL2FwaS9hdXRoL3JlZ2lzdGVyJzogeyByZXF1ZXN0czogMywgd2luZG93OiA2MCAqIDYwICogMTAwMCB9LCAvLyAzIHJlcXVlc3RzIHBlciBob3VyXG4gICcvYXBpL2F1dGgvZm9yZ290LXBhc3N3b3JkJzogeyByZXF1ZXN0czogMywgd2luZG93OiA2MCAqIDYwICogMTAwMCB9LCAvLyAzIHJlcXVlc3RzIHBlciBob3VyXG4gICcvYXBpLyc6IHsgcmVxdWVzdHM6IDEwMCwgd2luZG93OiA2MCAqIDEwMDAgfSwgLy8gMTAwIHJlcXVlc3RzIHBlciBtaW51dGUgZm9yIEFQSVxuICBkZWZhdWx0OiB7IHJlcXVlc3RzOiAxMDAwLCB3aW5kb3c6IDYwICogMTAwMCB9LCAvLyAxMDAwIHJlcXVlc3RzIHBlciBtaW51dGUgZm9yIG90aGVyIHJvdXRlc1xufVxuXG4vLyBJbi1tZW1vcnkgcmF0ZSBsaW1pdGluZyBzdG9yZSAoaW4gcHJvZHVjdGlvbiwgdXNlIFJlZGlzKVxuY29uc3QgcmF0ZUxpbWl0U3RvcmUgPSBuZXcgTWFwPHN0cmluZywgeyBjb3VudDogbnVtYmVyOyByZXNldFRpbWU6IG51bWJlciB9PigpXG5cbmZ1bmN0aW9uIGlzUHVibGljUm91dGUocGF0aG5hbWU6IHN0cmluZyk6IGJvb2xlYW4ge1xuICByZXR1cm4gUFVCTElDX1JPVVRFUy5pbmNsdWRlcyhwYXRobmFtZSkgfHwgXG4gICAgICAgICBwYXRobmFtZS5zdGFydHNXaXRoKCcvYXBpL3B1YmxpYycpIHx8XG4gICAgICAgICBBUElfUFVCTElDX1JPVVRFUy5pbmNsdWRlcyhwYXRobmFtZSkgfHxcbiAgICAgICAgIHBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9fbmV4dCcpIHx8XG4gICAgICAgICBwYXRobmFtZS5zdGFydHNXaXRoKCcvZmF2aWNvbicpIHx8XG4gICAgICAgICBwYXRobmFtZS5pbmNsdWRlcygnLicpXG59XG5cbmZ1bmN0aW9uIGlzQXV0aFJvdXRlKHBhdGhuYW1lOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgcmV0dXJuIEFVVEhfUk9VVEVTLnNvbWUocm91dGUgPT4gcGF0aG5hbWUuc3RhcnRzV2l0aChyb3V0ZSkpXG59XG5cbmZ1bmN0aW9uIGlzUHJvdGVjdGVkUm91dGUocGF0aG5hbWU6IHN0cmluZyk6IGJvb2xlYW4ge1xuICByZXR1cm4gcGF0aG5hbWUuc3RhcnRzV2l0aCgnL2Rhc2hib2FyZCcpIHx8IFxuICAgICAgICAgcGF0aG5hbWUuc3RhcnRzV2l0aCgnL3dvcmtzcGFjZScpIHx8XG4gICAgICAgICBwYXRobmFtZS5zdGFydHNXaXRoKCcvcHJvZmlsZScpIHx8XG4gICAgICAgICBwYXRobmFtZS5zdGFydHNXaXRoKCcvc2V0dGluZ3MnKSB8fFxuICAgICAgICAgcGF0aG5hbWUuc3RhcnRzV2l0aCgnL2FwaS9wcm90ZWN0ZWQnKSB8fFxuICAgICAgICAgQURNSU5fUk9VVEVTLnNvbWUocm91dGUgPT4gcGF0aG5hbWUuc3RhcnRzV2l0aChyb3V0ZSkpIHx8XG4gICAgICAgICBTVVBFUl9BRE1JTl9ST1VURVMuc29tZShyb3V0ZSA9PiBwYXRobmFtZS5zdGFydHNXaXRoKHJvdXRlKSlcbn1cblxuZnVuY3Rpb24gZ2V0UmF0ZUxpbWl0S2V5KHJlcXVlc3Q6IE5leHRSZXF1ZXN0KTogc3RyaW5nIHtcbiAgY29uc3QgaXAgPSByZXF1ZXN0LmlwIHx8IHJlcXVlc3QuaGVhZGVycy5nZXQoJ3gtZm9yd2FyZGVkLWZvcicpIHx8ICd1bmtub3duJ1xuICBjb25zdCBwYXRobmFtZSA9IHJlcXVlc3QubmV4dFVybC5wYXRobmFtZVxuICByZXR1cm4gYCR7aXB9OiR7cGF0aG5hbWV9YFxufVxuXG5mdW5jdGlvbiBjaGVja1JhdGVMaW1pdChyZXF1ZXN0OiBOZXh0UmVxdWVzdCk6IHsgYWxsb3dlZDogYm9vbGVhbjsgcmVtYWluaW5nOiBudW1iZXI7IHJlc2V0VGltZTogbnVtYmVyIH0ge1xuICBjb25zdCBrZXkgPSBnZXRSYXRlTGltaXRLZXkocmVxdWVzdClcbiAgY29uc3QgcGF0aG5hbWUgPSByZXF1ZXN0Lm5leHRVcmwucGF0aG5hbWVcbiAgXG4gIC8vIEZpbmQgbWF0Y2hpbmcgcmF0ZSBsaW1pdCBjb25maWdcbiAgbGV0IGNvbmZpZyA9IFJBVEVfTElNSVRfQ09ORklHLmRlZmF1bHRcbiAgZm9yIChjb25zdCBbcGF0dGVybiwgcGF0dGVybkNvbmZpZ10gb2YgT2JqZWN0LmVudHJpZXMoUkFURV9MSU1JVF9DT05GSUcpKSB7XG4gICAgaWYgKHBhdHRlcm4gIT09ICdkZWZhdWx0JyAmJiBwYXRobmFtZS5zdGFydHNXaXRoKHBhdHRlcm4pKSB7XG4gICAgICBjb25maWcgPSBwYXR0ZXJuQ29uZmlnXG4gICAgICBicmVha1xuICAgIH1cbiAgfVxuICBcbiAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKVxuICBjb25zdCB3aW5kb3dTdGFydCA9IG5vdyAtIGNvbmZpZy53aW5kb3dcbiAgXG4gIC8vIEdldCBvciBjcmVhdGUgcmF0ZSBsaW1pdCBlbnRyeVxuICBsZXQgZW50cnkgPSByYXRlTGltaXRTdG9yZS5nZXQoa2V5KVxuICBpZiAoIWVudHJ5IHx8IGVudHJ5LnJlc2V0VGltZSA8IHdpbmRvd1N0YXJ0KSB7XG4gICAgZW50cnkgPSB7IGNvdW50OiAwLCByZXNldFRpbWU6IG5vdyArIGNvbmZpZy53aW5kb3cgfVxuICAgIHJhdGVMaW1pdFN0b3JlLnNldChrZXksIGVudHJ5KVxuICB9XG4gIFxuICAvLyBDaGVjayBpZiBsaW1pdCBleGNlZWRlZFxuICBpZiAoZW50cnkuY291bnQgPj0gY29uZmlnLnJlcXVlc3RzKSB7XG4gICAgcmV0dXJuIHsgYWxsb3dlZDogZmFsc2UsIHJlbWFpbmluZzogMCwgcmVzZXRUaW1lOiBlbnRyeS5yZXNldFRpbWUgfVxuICB9XG4gIFxuICAvLyBJbmNyZW1lbnQgY291bnRlclxuICBlbnRyeS5jb3VudCsrXG4gIHJhdGVMaW1pdFN0b3JlLnNldChrZXksIGVudHJ5KVxuICBcbiAgcmV0dXJuIHsgXG4gICAgYWxsb3dlZDogdHJ1ZSwgXG4gICAgcmVtYWluaW5nOiBjb25maWcucmVxdWVzdHMgLSBlbnRyeS5jb3VudCwgXG4gICAgcmVzZXRUaW1lOiBlbnRyeS5yZXNldFRpbWUgXG4gIH1cbn1cblxuZnVuY3Rpb24gYWRkU2VjdXJpdHlIZWFkZXJzKHJlc3BvbnNlOiBOZXh0UmVzcG9uc2UpOiBOZXh0UmVzcG9uc2Uge1xuICAvLyBTZWN1cml0eSBoZWFkZXJzXG4gIHJlc3BvbnNlLmhlYWRlcnMuc2V0KCdYLUNvbnRlbnQtVHlwZS1PcHRpb25zJywgJ25vc25pZmYnKVxuICByZXNwb25zZS5oZWFkZXJzLnNldCgnWC1GcmFtZS1PcHRpb25zJywgJ0RFTlknKVxuICByZXNwb25zZS5oZWFkZXJzLnNldCgnWC1YU1MtUHJvdGVjdGlvbicsICcxOyBtb2RlPWJsb2NrJylcbiAgcmVzcG9uc2UuaGVhZGVycy5zZXQoJ1JlZmVycmVyLVBvbGljeScsICdzdHJpY3Qtb3JpZ2luLXdoZW4tY3Jvc3Mtb3JpZ2luJylcbiAgcmVzcG9uc2UuaGVhZGVycy5zZXQoJ1Blcm1pc3Npb25zLVBvbGljeScsICdjYW1lcmE9KCksIG1pY3JvcGhvbmU9KCksIGdlb2xvY2F0aW9uPSgpJylcbiAgXG4gIC8vIEhTVFMgKG9ubHkgaW4gcHJvZHVjdGlvbilcbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgICByZXNwb25zZS5oZWFkZXJzLnNldCgnU3RyaWN0LVRyYW5zcG9ydC1TZWN1cml0eScsICdtYXgtYWdlPTMxNTM2MDAwOyBpbmNsdWRlU3ViRG9tYWlucycpXG4gIH1cbiAgXG4gIHJldHVybiByZXNwb25zZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gbWlkZGxld2FyZShyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICBjb25zdCB7IHBhdGhuYW1lIH0gPSByZXF1ZXN0Lm5leHRVcmxcbiAgXG4gIC8vIENoZWNrIHJhdGUgbGltaXRpbmdcbiAgY29uc3QgcmF0ZUxpbWl0ID0gY2hlY2tSYXRlTGltaXQocmVxdWVzdClcbiAgaWYgKCFyYXRlTGltaXQuYWxsb3dlZCkge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gbmV3IE5leHRSZXNwb25zZShcbiAgICAgIEpTT04uc3RyaW5naWZ5KHsgXG4gICAgICAgIGVycm9yOiAnVG9vIG1hbnkgcmVxdWVzdHMnLCBcbiAgICAgICAgcmV0cnlBZnRlcjogTWF0aC5jZWlsKChyYXRlTGltaXQucmVzZXRUaW1lIC0gRGF0ZS5ub3coKSkgLyAxMDAwKSBcbiAgICAgIH0pLFxuICAgICAgeyBcbiAgICAgICAgc3RhdHVzOiA0MjksXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICdSZXRyeS1BZnRlcic6IE1hdGguY2VpbCgocmF0ZUxpbWl0LnJlc2V0VGltZSAtIERhdGUubm93KCkpIC8gMTAwMCkudG9TdHJpbmcoKSxcbiAgICAgICAgICAnWC1SYXRlTGltaXQtTGltaXQnOiBSQVRFX0xJTUlUX0NPTkZJRy5kZWZhdWx0LnJlcXVlc3RzLnRvU3RyaW5nKCksXG4gICAgICAgICAgJ1gtUmF0ZUxpbWl0LVJlbWFpbmluZyc6IHJhdGVMaW1pdC5yZW1haW5pbmcudG9TdHJpbmcoKSxcbiAgICAgICAgICAnWC1SYXRlTGltaXQtUmVzZXQnOiByYXRlTGltaXQucmVzZXRUaW1lLnRvU3RyaW5nKCksXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICApXG4gICAgcmV0dXJuIGFkZFNlY3VyaXR5SGVhZGVycyhyZXNwb25zZSlcbiAgfVxuICBcbiAgLy8gQWRkIHJhdGUgbGltaXQgaGVhZGVycyB0byByZXNwb25zZVxuICBjb25zdCByZXNwb25zZSA9IE5leHRSZXNwb25zZS5uZXh0KClcbiAgcmVzcG9uc2UuaGVhZGVycy5zZXQoJ1gtUmF0ZUxpbWl0LUxpbWl0JywgUkFURV9MSU1JVF9DT05GSUcuZGVmYXVsdC5yZXF1ZXN0cy50b1N0cmluZygpKVxuICByZXNwb25zZS5oZWFkZXJzLnNldCgnWC1SYXRlTGltaXQtUmVtYWluaW5nJywgcmF0ZUxpbWl0LnJlbWFpbmluZy50b1N0cmluZygpKVxuICByZXNwb25zZS5oZWFkZXJzLnNldCgnWC1SYXRlTGltaXQtUmVzZXQnLCByYXRlTGltaXQucmVzZXRUaW1lLnRvU3RyaW5nKCkpXG4gIFxuICAvLyBTa2lwIG1pZGRsZXdhcmUgZm9yIHB1YmxpYyByb3V0ZXNcbiAgaWYgKGlzUHVibGljUm91dGUocGF0aG5hbWUpKSB7XG4gICAgcmV0dXJuIGFkZFNlY3VyaXR5SGVhZGVycyhyZXNwb25zZSlcbiAgfVxuICBcbiAgLy8gR2V0IHRva2VuIGZyb20gaGVhZGVyIG9yIGNvb2tpZVxuICBjb25zdCBhdXRoSGVhZGVyID0gcmVxdWVzdC5oZWFkZXJzLmdldCgnYXV0aG9yaXphdGlvbicpXG4gIGNvbnN0IHRva2VuID0gZXh0cmFjdFRva2VuRnJvbUhlYWRlcihhdXRoSGVhZGVyKSB8fCByZXF1ZXN0LmNvb2tpZXMuZ2V0KCdhdXRoLXRva2VuJyk/LnZhbHVlXG4gIFxuICAvLyBWZXJpZnkgdG9rZW4gZm9yIHByb3RlY3RlZCByb3V0ZXNcbiAgaWYgKGlzUHJvdGVjdGVkUm91dGUocGF0aG5hbWUpKSB7XG4gICAgaWYgKCF0b2tlbikge1xuICAgICAgaWYgKHBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9hcGkvJykpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBOZXh0UmVzcG9uc2UoXG4gICAgICAgICAgSlNPTi5zdHJpbmdpZnkoeyBlcnJvcjogJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJyB9KSxcbiAgICAgICAgICB7IHN0YXR1czogNDAxLCBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSB9XG4gICAgICAgIClcbiAgICAgIH1cbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QobmV3IFVSTCgnL2xvZ2luJywgcmVxdWVzdC51cmwpKVxuICAgIH1cbiAgICBcbiAgICAvLyBGb3Igbm93LCBza2lwIHRva2VuIHZlcmlmaWNhdGlvbiAtIHdpbGwgaW1wbGVtZW50IGxhdGVyXG4gICAgY29uc3QgcGF5bG9hZCA9IHsgcm9sZTogJ1VTRVInLCB1c2VySWQ6ICd0ZW1wJyB9XG4gICAgLy8gY29uc3QgcGF5bG9hZCA9IHZlcmlmeUFjY2Vzc1Rva2VuKHRva2VuKVxuICAgIC8vIGlmICghcGF5bG9hZCkge1xuICAgIC8vICAgaWYgKHBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9hcGkvJykpIHtcbiAgICAvLyAgICAgcmV0dXJuIG5ldyBOZXh0UmVzcG9uc2UoXG4gICAgLy8gICAgICAgSlNPTi5zdHJpbmdpZnkoeyBlcnJvcjogJ0ludmFsaWQgb3IgZXhwaXJlZCB0b2tlbicgfSksXG4gICAgLy8gICAgICAgeyBzdGF0dXM6IDQwMSwgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0gfVxuICAgIC8vICAgICApXG4gICAgLy8gICB9XG4gICAgLy8gICByZXR1cm4gTmV4dFJlc3BvbnNlLnJlZGlyZWN0KG5ldyBVUkwoJy9sb2dpbicsIHJlcXVlc3QudXJsKSlcbiAgICAvLyB9XG4gICAgXG4gICAgLy8gQ2hlY2sgcm9sZS1iYXNlZCBhY2Nlc3NcbiAgICBpZiAoIWNhbkFjY2Vzc1JvdXRlKHBheWxvYWQucm9sZSwgcGF0aG5hbWUpKSB7XG4gICAgICBpZiAocGF0aG5hbWUuc3RhcnRzV2l0aCgnL2FwaS8nKSkge1xuICAgICAgICByZXR1cm4gbmV3IE5leHRSZXNwb25zZShcbiAgICAgICAgICBKU09OLnN0cmluZ2lmeSh7IGVycm9yOiAnSW5zdWZmaWNpZW50IHBlcm1pc3Npb25zJyB9KSxcbiAgICAgICAgICB7IHN0YXR1czogNDAzLCBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSB9XG4gICAgICAgIClcbiAgICAgIH1cbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QobmV3IFVSTCgnL3VuYXV0aG9yaXplZCcsIHJlcXVlc3QudXJsKSlcbiAgICB9XG4gICAgXG4gICAgLy8gQWRkIHVzZXIgaW5mbyB0byBoZWFkZXJzIGZvciBBUEkgcm91dGVzXG4gICAgaWYgKHBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9hcGkvJykpIHtcbiAgICAgIHJlc3BvbnNlLmhlYWRlcnMuc2V0KCdYLVVzZXItSWQnLCBwYXlsb2FkLnVzZXJJZClcbiAgICAgIHJlc3BvbnNlLmhlYWRlcnMuc2V0KCdYLVVzZXItUm9sZScsIHBheWxvYWQucm9sZSlcbiAgICAgIHJlc3BvbnNlLmhlYWRlcnMuc2V0KCdYLVVzZXItU3Vic2NyaXB0aW9uJywgcGF5bG9hZC5zdWJzY3JpcHRpb25UaWVyKVxuICAgIH1cbiAgfVxuICBcbiAgLy8gUmVkaXJlY3QgYXV0aGVudGljYXRlZCB1c2VycyBhd2F5IGZyb20gYXV0aCByb3V0ZXNcbiAgaWYgKGlzQXV0aFJvdXRlKHBhdGhuYW1lKSAmJiB0b2tlbikge1xuICAgIGNvbnN0IHBheWxvYWQgPSB2ZXJpZnlBY2Nlc3NUb2tlbih0b2tlbilcbiAgICBpZiAocGF5bG9hZCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdChuZXcgVVJMKCcvZGFzaGJvYXJkJywgcmVxdWVzdC51cmwpKVxuICAgIH1cbiAgfVxuICBcbiAgcmV0dXJuIGFkZFNlY3VyaXR5SGVhZGVycyhyZXNwb25zZSlcbn1cblxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IHtcbiAgbWF0Y2hlcjogW1xuICAgIC8qXG4gICAgICogTWF0Y2ggYWxsIHJlcXVlc3QgcGF0aHMgZXhjZXB0IGZvciB0aGUgb25lcyBzdGFydGluZyB3aXRoOlxuICAgICAqIC0gX25leHQvc3RhdGljIChzdGF0aWMgZmlsZXMpXG4gICAgICogLSBfbmV4dC9pbWFnZSAoaW1hZ2Ugb3B0aW1pemF0aW9uIGZpbGVzKVxuICAgICAqIC0gZmF2aWNvbi5pY28gKGZhdmljb24gZmlsZSlcbiAgICAgKi9cbiAgICAnLygoPyFfbmV4dC9zdGF0aWN8X25leHQvaW1hZ2V8ZmF2aWNvbi5pY28pLiopJyxcbiAgXSxcbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJleHRyYWN0VG9rZW5Gcm9tSGVhZGVyIiwiYXV0aEhlYWRlciIsInN0YXJ0c1dpdGgiLCJzdWJzdHJpbmciLCJjYW5BY2Nlc3NSb3V0ZSIsInVzZXJSb2xlIiwicm91dGUiLCJhZG1pblJvdXRlcyIsInNvbWUiLCJyIiwiQVVUSF9ST1VURVMiLCJQVUJMSUNfUk9VVEVTIiwiQVBJX1BVQkxJQ19ST1VURVMiLCJBRE1JTl9ST1VURVMiLCJTVVBFUl9BRE1JTl9ST1VURVMiLCJSQVRFX0xJTUlUX0NPTkZJRyIsInJlcXVlc3RzIiwid2luZG93IiwiZGVmYXVsdCIsInJhdGVMaW1pdFN0b3JlIiwiTWFwIiwiaXNQdWJsaWNSb3V0ZSIsInBhdGhuYW1lIiwiaW5jbHVkZXMiLCJpc0F1dGhSb3V0ZSIsImlzUHJvdGVjdGVkUm91dGUiLCJnZXRSYXRlTGltaXRLZXkiLCJyZXF1ZXN0IiwiaXAiLCJoZWFkZXJzIiwiZ2V0IiwibmV4dFVybCIsImNoZWNrUmF0ZUxpbWl0Iiwia2V5IiwiY29uZmlnIiwicGF0dGVybiIsInBhdHRlcm5Db25maWciLCJPYmplY3QiLCJlbnRyaWVzIiwibm93IiwiRGF0ZSIsIndpbmRvd1N0YXJ0IiwiZW50cnkiLCJyZXNldFRpbWUiLCJjb3VudCIsInNldCIsImFsbG93ZWQiLCJyZW1haW5pbmciLCJhZGRTZWN1cml0eUhlYWRlcnMiLCJyZXNwb25zZSIsInByb2Nlc3MiLCJtaWRkbGV3YXJlIiwicmF0ZUxpbWl0IiwiSlNPTiIsInN0cmluZ2lmeSIsImVycm9yIiwicmV0cnlBZnRlciIsIk1hdGgiLCJjZWlsIiwic3RhdHVzIiwidG9TdHJpbmciLCJuZXh0IiwidG9rZW4iLCJjb29raWVzIiwidmFsdWUiLCJyZWRpcmVjdCIsIlVSTCIsInVybCIsInBheWxvYWQiLCJyb2xlIiwidXNlcklkIiwic3Vic2NyaXB0aW9uVGllciIsInZlcmlmeUFjY2Vzc1Rva2VuIiwibWF0Y2hlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});