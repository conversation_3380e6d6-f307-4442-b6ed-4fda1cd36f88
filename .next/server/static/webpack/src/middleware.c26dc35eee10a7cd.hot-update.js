"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n// Simplified middleware for now - will add full auth later\nfunction extractTokenFromHeader(authHeader) {\n    if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n        return null;\n    }\n    return authHeader.substring(7);\n}\nfunction canAccessRoute(userRole, route) {\n    // Simplified route access check\n    const adminRoutes = [\n        \"/admin\",\n        \"/super-admin\"\n    ];\n    if (adminRoutes.some((r)=>route.startsWith(r))) {\n        return userRole === \"ADMIN\" || userRole === \"SUPER_ADMIN\";\n    }\n    return true;\n}\n// Define route patterns\nconst AUTH_ROUTES = [\n    \"/login\",\n    \"/register\",\n    \"/forgot-password\",\n    \"/reset-password\"\n];\nconst PUBLIC_ROUTES = [\n    \"/\",\n    \"/pricing\",\n    \"/features\",\n    \"/contact\",\n    \"/terms\",\n    \"/privacy\"\n];\nconst API_PUBLIC_ROUTES = [\n    \"/api/auth/login\",\n    \"/api/auth/register\",\n    \"/api/auth/forgot-password\",\n    \"/api/auth/reset-password\"\n];\nconst ADMIN_ROUTES = [\n    \"/admin\"\n];\nconst SUPER_ADMIN_ROUTES = [\n    \"/super-admin\"\n];\n// Rate limiting configuration\nconst RATE_LIMIT_CONFIG = {\n    \"/api/auth/login\": {\n        requests: 5,\n        window: 15 * 60 * 1000\n    },\n    \"/api/auth/register\": {\n        requests: 3,\n        window: 60 * 60 * 1000\n    },\n    \"/api/auth/forgot-password\": {\n        requests: 3,\n        window: 60 * 60 * 1000\n    },\n    \"/api/\": {\n        requests: 100,\n        window: 60 * 1000\n    },\n    default: {\n        requests: 1000,\n        window: 60 * 1000\n    }\n};\n// In-memory rate limiting store (in production, use Redis)\nconst rateLimitStore = new Map();\nfunction isPublicRoute(pathname) {\n    return PUBLIC_ROUTES.includes(pathname) || pathname.startsWith(\"/api/public\") || API_PUBLIC_ROUTES.includes(pathname) || pathname.startsWith(\"/_next\") || pathname.startsWith(\"/favicon\") || pathname.includes(\".\");\n}\nfunction isAuthRoute(pathname) {\n    return AUTH_ROUTES.some((route)=>pathname.startsWith(route));\n}\nfunction isProtectedRoute(pathname) {\n    return pathname.startsWith(\"/dashboard\") || pathname.startsWith(\"/workspace\") || pathname.startsWith(\"/profile\") || pathname.startsWith(\"/settings\") || pathname.startsWith(\"/api/protected\") || ADMIN_ROUTES.some((route)=>pathname.startsWith(route)) || SUPER_ADMIN_ROUTES.some((route)=>pathname.startsWith(route));\n}\nfunction getRateLimitKey(request) {\n    const ip = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n    const pathname = request.nextUrl.pathname;\n    return `${ip}:${pathname}`;\n}\nfunction checkRateLimit(request) {\n    const key = getRateLimitKey(request);\n    const pathname = request.nextUrl.pathname;\n    // Find matching rate limit config\n    let config = RATE_LIMIT_CONFIG.default;\n    for (const [pattern, patternConfig] of Object.entries(RATE_LIMIT_CONFIG)){\n        if (pattern !== \"default\" && pathname.startsWith(pattern)) {\n            config = patternConfig;\n            break;\n        }\n    }\n    const now = Date.now();\n    const windowStart = now - config.window;\n    // Get or create rate limit entry\n    let entry = rateLimitStore.get(key);\n    if (!entry || entry.resetTime < windowStart) {\n        entry = {\n            count: 0,\n            resetTime: now + config.window\n        };\n        rateLimitStore.set(key, entry);\n    }\n    // Check if limit exceeded\n    if (entry.count >= config.requests) {\n        return {\n            allowed: false,\n            remaining: 0,\n            resetTime: entry.resetTime\n        };\n    }\n    // Increment counter\n    entry.count++;\n    rateLimitStore.set(key, entry);\n    return {\n        allowed: true,\n        remaining: config.requests - entry.count,\n        resetTime: entry.resetTime\n    };\n}\nfunction addSecurityHeaders(response) {\n    // Security headers\n    response.headers.set(\"X-Content-Type-Options\", \"nosniff\");\n    response.headers.set(\"X-Frame-Options\", \"DENY\");\n    response.headers.set(\"X-XSS-Protection\", \"1; mode=block\");\n    response.headers.set(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n    response.headers.set(\"Permissions-Policy\", \"camera=(), microphone=(), geolocation=()\");\n    // HSTS (only in production)\n    if (false) {}\n    return response;\n}\nfunction middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Check rate limiting\n    const rateLimit = checkRateLimit(request);\n    if (!rateLimit.allowed) {\n        const response = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: \"Too many requests\",\n            retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)\n        }), {\n            status: 429,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Retry-After\": Math.ceil((rateLimit.resetTime - Date.now()) / 1000).toString(),\n                \"X-RateLimit-Limit\": RATE_LIMIT_CONFIG.default.requests.toString(),\n                \"X-RateLimit-Remaining\": rateLimit.remaining.toString(),\n                \"X-RateLimit-Reset\": rateLimit.resetTime.toString()\n            }\n        });\n        return addSecurityHeaders(response);\n    }\n    // Add rate limit headers to response\n    const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    response.headers.set(\"X-RateLimit-Limit\", RATE_LIMIT_CONFIG.default.requests.toString());\n    response.headers.set(\"X-RateLimit-Remaining\", rateLimit.remaining.toString());\n    response.headers.set(\"X-RateLimit-Reset\", rateLimit.resetTime.toString());\n    // Skip middleware for public routes\n    if (isPublicRoute(pathname)) {\n        return addSecurityHeaders(response);\n    }\n    // Get token from header or cookie\n    const authHeader = request.headers.get(\"authorization\");\n    const token = extractTokenFromHeader(authHeader) || request.cookies.get(\"auth-token\")?.value;\n    // Verify token for protected routes\n    if (isProtectedRoute(pathname)) {\n        if (!token) {\n            if (pathname.startsWith(\"/api/\")) {\n                return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n                    error: \"Authentication required\"\n                }), {\n                    status: 401,\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/login\", request.url));\n        }\n        const payload = verifyAccessToken(token);\n        if (!payload) {\n            if (pathname.startsWith(\"/api/\")) {\n                return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n                    error: \"Invalid or expired token\"\n                }), {\n                    status: 401,\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/login\", request.url));\n        }\n        // Check role-based access\n        if (!canAccessRoute(payload.role, pathname)) {\n            if (pathname.startsWith(\"/api/\")) {\n                return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n                    error: \"Insufficient permissions\"\n                }), {\n                    status: 403,\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/unauthorized\", request.url));\n        }\n        // Add user info to headers for API routes\n        if (pathname.startsWith(\"/api/\")) {\n            response.headers.set(\"X-User-Id\", payload.userId);\n            response.headers.set(\"X-User-Role\", payload.role);\n            response.headers.set(\"X-User-Subscription\", payload.subscriptionTier);\n        }\n    }\n    // Redirect authenticated users away from auth routes\n    if (isAuthRoute(pathname) && token) {\n        const payload = verifyAccessToken(token);\n        if (payload) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        }\n    }\n    return addSecurityHeaders(response);\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */ \"/((?!_next/static|_next/image|favicon.ico).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});