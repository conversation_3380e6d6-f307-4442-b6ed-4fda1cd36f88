"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/lib/auth/permissions.ts":
/*!*************************************!*\
  !*** ./src/lib/auth/permissions.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROTECTED_ROUTES: () => (/* binding */ PROTECTED_ROUTES),\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   SUBSCRIPTION_LIMITS: () => (/* binding */ SUBSCRIPTION_LIMITS),\n/* harmony export */   canAccessResource: () => (/* binding */ canAccessResource),\n/* harmony export */   canAccessRoute: () => (/* binding */ canAccessRoute),\n/* harmony export */   getRemainingQuota: () => (/* binding */ getRemainingQuota),\n/* harmony export */   getSubscriptionLimit: () => (/* binding */ getSubscriptionLimit),\n/* harmony export */   getUserPermissions: () => (/* binding */ getUserPermissions),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   isWithinSubscriptionLimit: () => (/* binding */ isWithinSubscriptionLimit)\n/* harmony export */ });\n// Define role-based permissions\n// Define base permissions first\nconst USER_PERMISSIONS = [\n    {\n        resource: \"project\",\n        action: \"create\"\n    },\n    {\n        resource: \"project\",\n        action: \"read\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"project\",\n        action: \"update\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"project\",\n        action: \"delete\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"conversation\",\n        action: \"create\"\n    },\n    {\n        resource: \"conversation\",\n        action: \"read\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"conversation\",\n        action: \"update\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"conversation\",\n        action: \"delete\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"profile\",\n        action: \"read\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"profile\",\n        action: \"update\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"subscription\",\n        action: \"read\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"subscription\",\n        action: \"update\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"usage\",\n        action: \"read\",\n        conditions: {\n            owner: true\n        }\n    }\n];\nconst ENTERPRISE_PERMISSIONS = [\n    ...USER_PERMISSIONS,\n    {\n        resource: \"analytics\",\n        action: \"read\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"team\",\n        action: \"create\"\n    },\n    {\n        resource: \"team\",\n        action: \"read\",\n        conditions: {\n            member: true\n        }\n    },\n    {\n        resource: \"team\",\n        action: \"update\",\n        conditions: {\n            admin: true\n        }\n    },\n    {\n        resource: \"api\",\n        action: \"read\"\n    },\n    {\n        resource: \"api\",\n        action: \"create\"\n    },\n    {\n        resource: \"webhook\",\n        action: \"create\"\n    },\n    {\n        resource: \"webhook\",\n        action: \"read\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"webhook\",\n        action: \"update\",\n        conditions: {\n            owner: true\n        }\n    },\n    {\n        resource: \"webhook\",\n        action: \"delete\",\n        conditions: {\n            owner: true\n        }\n    }\n];\nconst ADMIN_PERMISSIONS = [\n    {\n        resource: \"*\",\n        action: \"read\"\n    },\n    {\n        resource: \"user\",\n        action: \"read\"\n    },\n    {\n        resource: \"user\",\n        action: \"update\"\n    },\n    {\n        resource: \"user\",\n        action: \"delete\"\n    },\n    {\n        resource: \"project\",\n        action: \"read\"\n    },\n    {\n        resource: \"project\",\n        action: \"update\"\n    },\n    {\n        resource: \"project\",\n        action: \"delete\"\n    },\n    {\n        resource: \"subscription\",\n        action: \"read\"\n    },\n    {\n        resource: \"subscription\",\n        action: \"update\"\n    },\n    {\n        resource: \"analytics\",\n        action: \"read\"\n    },\n    {\n        resource: \"system\",\n        action: \"read\"\n    },\n    {\n        resource: \"audit\",\n        action: \"read\"\n    }\n];\nconst SUPER_ADMIN_PERMISSIONS = [\n    {\n        resource: \"*\",\n        action: \"create\"\n    },\n    {\n        resource: \"*\",\n        action: \"read\"\n    },\n    {\n        resource: \"*\",\n        action: \"update\"\n    },\n    {\n        resource: \"*\",\n        action: \"delete\"\n    },\n    {\n        resource: \"*\",\n        action: \"admin\"\n    }\n];\nconst ROLE_PERMISSIONS = {\n    USER: USER_PERMISSIONS,\n    ENTERPRISE: ENTERPRISE_PERMISSIONS,\n    ADMIN: ADMIN_PERMISSIONS,\n    SUPER_ADMIN: SUPER_ADMIN_PERMISSIONS\n};\n// Define subscription tier limitations\nconst SUBSCRIPTION_LIMITS = {\n    FREE: {\n        projectsPerMonth: 5,\n        aiInteractionsPerDay: 50,\n        storageGB: 1,\n        apiCallsPerHour: 100,\n        teamMembers: 1,\n        customTemplates: 0\n    },\n    PRO: {\n        projectsPerMonth: 50,\n        aiInteractionsPerDay: 500,\n        storageGB: 10,\n        apiCallsPerHour: 1000,\n        teamMembers: 5,\n        customTemplates: 10\n    },\n    ENTERPRISE: {\n        projectsPerMonth: -1,\n        aiInteractionsPerDay: -1,\n        storageGB: 100,\n        apiCallsPerHour: 10000,\n        teamMembers: 50,\n        customTemplates: -1\n    },\n    CUSTOM: {\n        projectsPerMonth: -1,\n        aiInteractionsPerDay: -1,\n        storageGB: -1,\n        apiCallsPerHour: -1,\n        teamMembers: -1,\n        customTemplates: -1\n    }\n};\nfunction getUserPermissions(role, subscriptionTier) {\n    const permissions = ROLE_PERMISSIONS[role] || [];\n    return {\n        role,\n        subscriptionTier,\n        permissions\n    };\n}\nfunction hasPermission(userPermissions, resource, action, context) {\n    // Super admin has all permissions\n    if (userPermissions.role === \"SUPER_ADMIN\") {\n        return true;\n    }\n    // Check if user has specific permission\n    const permission = userPermissions.permissions.find((p)=>(p.resource === resource || p.resource === \"*\") && p.action === action);\n    if (!permission) {\n        return false;\n    }\n    // Check conditions if they exist\n    if (permission.conditions && context) {\n        for (const [key, value] of Object.entries(permission.conditions)){\n            if (context[key] !== value) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nfunction canAccessResource(userPermissions, resource, action, resourceOwnerId, userId) {\n    const context = {};\n    if (resourceOwnerId && userId) {\n        context.owner = resourceOwnerId === userId;\n    }\n    return hasPermission(userPermissions, resource, action, context);\n}\nfunction getSubscriptionLimit(subscriptionTier, limitType) {\n    return SUBSCRIPTION_LIMITS[subscriptionTier]?.[limitType] || 0;\n}\nfunction isWithinSubscriptionLimit(subscriptionTier, limitType, currentUsage) {\n    const limit = getSubscriptionLimit(subscriptionTier, limitType);\n    // -1 means unlimited\n    if (limit === -1) {\n        return true;\n    }\n    return currentUsage < limit;\n}\nfunction getRemainingQuota(subscriptionTier, limitType, currentUsage) {\n    const limit = getSubscriptionLimit(subscriptionTier, limitType);\n    // -1 means unlimited\n    if (limit === -1) {\n        return -1;\n    }\n    return Math.max(0, limit - currentUsage);\n}\n// Route protection helpers\nconst PROTECTED_ROUTES = {\n    \"/dashboard\": [\n        \"USER\",\n        \"ENTERPRISE\",\n        \"ADMIN\",\n        \"SUPER_ADMIN\"\n    ],\n    \"/admin\": [\n        \"ADMIN\",\n        \"SUPER_ADMIN\"\n    ],\n    \"/super-admin\": [\n        \"SUPER_ADMIN\"\n    ],\n    \"/api/admin\": [\n        \"ADMIN\",\n        \"SUPER_ADMIN\"\n    ],\n    \"/api/super-admin\": [\n        \"SUPER_ADMIN\"\n    ]\n};\nfunction canAccessRoute(userRole, route) {\n    const allowedRoles = PROTECTED_ROUTES[route];\n    if (!allowedRoles) {\n        return true // Public route\n        ;\n    }\n    return allowedRoles.includes(userRole);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/auth/permissions.ts\n");

/***/ })

});