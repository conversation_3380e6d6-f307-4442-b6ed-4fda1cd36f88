"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5kcm9pZHdlYi1lbnRlcnByaXNlLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9wcmltaXRpdmUvZGlzdC9pbmRleC5tanM/ZDU4MyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9jb3JlL3ByaW1pdGl2ZS9zcmMvcHJpbWl0aXZlLnRzeFxuZnVuY3Rpb24gY29tcG9zZUV2ZW50SGFuZGxlcnMob3JpZ2luYWxFdmVudEhhbmRsZXIsIG91ckV2ZW50SGFuZGxlciwgeyBjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPSB0cnVlIH0gPSB7fSkge1xuICByZXR1cm4gZnVuY3Rpb24gaGFuZGxlRXZlbnQoZXZlbnQpIHtcbiAgICBvcmlnaW5hbEV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICBpZiAoY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID09PSBmYWxzZSB8fCAhZXZlbnQuZGVmYXVsdFByZXZlbnRlZCkge1xuICAgICAgcmV0dXJuIG91ckV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICB9XG4gIH07XG59XG5leHBvcnQge1xuICBjb21wb3NlRXZlbnRIYW5kbGVyc1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-checkbox/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox),\n/* harmony export */   CheckboxIndicator: () => (/* binding */ CheckboxIndicator),\n/* harmony export */   Indicator: () => (/* binding */ CheckboxIndicator),\n/* harmony export */   Root: () => (/* binding */ Checkbox),\n/* harmony export */   createCheckboxScope: () => (/* binding */ createCheckboxScope),\n/* harmony export */   unstable_BubbleInput: () => (/* binding */ CheckboxBubbleInput),\n/* harmony export */   unstable_CheckboxBubbleInput: () => (/* binding */ CheckboxBubbleInput),\n/* harmony export */   unstable_CheckboxProvider: () => (/* binding */ CheckboxProvider),\n/* harmony export */   unstable_CheckboxTrigger: () => (/* binding */ CheckboxTrigger),\n/* harmony export */   unstable_Provider: () => (/* binding */ CheckboxProvider),\n/* harmony export */   unstable_Trigger: () => (/* binding */ CheckboxTrigger)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Checkbox,CheckboxIndicator,Indicator,Root,createCheckboxScope,unstable_BubbleInput,unstable_CheckboxBubbleInput,unstable_CheckboxProvider,unstable_CheckboxTrigger,unstable_Provider,unstable_Trigger auto */ // src/checkbox.tsx\n\n\n\n\n\n\n\n\n\n\nvar CHECKBOX_NAME = \"Checkbox\";\nvar [createCheckboxContext, createCheckboxScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(CHECKBOX_NAME);\nvar [CheckboxProviderImpl, useCheckboxContext] = createCheckboxContext(CHECKBOX_NAME);\nfunction CheckboxProvider(props) {\n    const { __scopeCheckbox, checked: checkedProp, children, defaultChecked, disabled, form, name, onCheckedChange, required, value = \"on\", // @ts-expect-error\n    internal_do_not_use_render } = props;\n    const [checked, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: checkedProp,\n        defaultProp: defaultChecked ?? false,\n        onChange: onCheckedChange,\n        caller: CHECKBOX_NAME\n    });\n    const [control, setControl] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [bubbleInput, setBubbleInput] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = control ? !!form || !!control.closest(\"form\") : // We set this to true by default so that events bubble to forms without JS (SSR)\n    true;\n    const context = {\n        checked,\n        disabled,\n        setChecked,\n        control,\n        setControl,\n        name,\n        form,\n        value,\n        hasConsumerStoppedPropagationRef,\n        required,\n        defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked,\n        isFormControl,\n        bubbleInput,\n        setBubbleInput\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxProviderImpl, {\n        scope: __scopeCheckbox,\n        ...context,\n        children: isFunction(internal_do_not_use_render) ? internal_do_not_use_render(context) : children\n    });\n}\nvar TRIGGER_NAME = \"CheckboxTrigger\";\nvar CheckboxTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeCheckbox, onKeyDown, onClick, ...checkboxProps }, forwardedRef)=>{\n    const { control, value, disabled, checked, required, setControl, setChecked, hasConsumerStoppedPropagationRef, isFormControl, bubbleInput } = useCheckboxContext(TRIGGER_NAME, __scopeCheckbox);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, setControl);\n    const initialCheckedStateRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(checked);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const form = control?.form;\n        if (form) {\n            const reset = ()=>setChecked(initialCheckedStateRef.current);\n            form.addEventListener(\"reset\", reset);\n            return ()=>form.removeEventListener(\"reset\", reset);\n        }\n    }, [\n        control,\n        setChecked\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n        type: \"button\",\n        role: \"checkbox\",\n        \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n        \"aria-required\": required,\n        \"data-state\": getState(checked),\n        \"data-disabled\": disabled ? \"\" : void 0,\n        disabled,\n        value,\n        ...checkboxProps,\n        ref: composedRefs,\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(onKeyDown, (event)=>{\n            if (event.key === \"Enter\") event.preventDefault();\n        }),\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(onClick, (event)=>{\n            setChecked((prevChecked)=>isIndeterminate(prevChecked) ? true : !prevChecked);\n            if (bubbleInput && isFormControl) {\n                hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n        })\n    });\n});\nCheckboxTrigger.displayName = TRIGGER_NAME;\nvar Checkbox = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCheckbox, name, checked, defaultChecked, required, disabled, value, onCheckedChange, form, ...checkboxProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxProvider, {\n        __scopeCheckbox,\n        checked,\n        defaultChecked,\n        disabled,\n        required,\n        onCheckedChange,\n        name,\n        form,\n        value,\n        internal_do_not_use_render: ({ isFormControl })=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxTrigger, {\n                        ...checkboxProps,\n                        ref: forwardedRef,\n                        __scopeCheckbox\n                    }),\n                    isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxBubbleInput, {\n                        __scopeCheckbox\n                    })\n                ]\n            })\n    });\n});\nCheckbox.displayName = CHECKBOX_NAME;\nvar INDICATOR_NAME = \"CheckboxIndicator\";\nvar CheckboxIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__.Presence, {\n        present: forceMount || isIndeterminate(context.checked) || context.checked === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span, {\n            \"data-state\": getState(context.checked),\n            \"data-disabled\": context.disabled ? \"\" : void 0,\n            ...indicatorProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"none\",\n                ...props.style\n            }\n        })\n    });\n});\nCheckboxIndicator.displayName = INDICATOR_NAME;\nvar BUBBLE_INPUT_NAME = \"CheckboxBubbleInput\";\nvar CheckboxBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeCheckbox, ...props }, forwardedRef)=>{\n    const { control, hasConsumerStoppedPropagationRef, checked, defaultChecked, required, disabled, name, value, form, bubbleInput, setBubbleInput } = useCheckboxContext(BUBBLE_INPUT_NAME, __scopeCheckbox);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, setBubbleInput);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const input = bubbleInput;\n        if (!input) return;\n        const inputProto = window.HTMLInputElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n        const setChecked = descriptor.set;\n        const bubbles = !hasConsumerStoppedPropagationRef.current;\n        if (prevChecked !== checked && setChecked) {\n            const event = new Event(\"click\", {\n                bubbles\n            });\n            input.indeterminate = isIndeterminate(checked);\n            setChecked.call(input, isIndeterminate(checked) ? false : checked);\n            input.dispatchEvent(event);\n        }\n    }, [\n        bubbleInput,\n        prevChecked,\n        checked,\n        hasConsumerStoppedPropagationRef\n    ]);\n    const defaultCheckedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isIndeterminate(checked) ? false : checked);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.input, {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: defaultChecked ?? defaultCheckedRef.current,\n        required,\n        disabled,\n        name,\n        value,\n        form,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0,\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            transform: \"translateX(-100%)\"\n        }\n    });\n});\nCheckboxBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\nfunction isIndeterminate(checked) {\n    return checked === \"indeterminate\";\n}\nfunction getState(checked) {\n    return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection),\n/* harmony export */   unstable_createCollection: () => (/* binding */ createCollection2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection,unstable_createCollection auto */ // src/collection-legacy.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            context.itemMap.set(ref, {\n                ref,\n                ...itemData\n            });\n            return ()=>void context.itemMap.delete(ref);\n        });\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            const collectionNode = context.collectionRef.current;\n            if (!collectionNode) return [];\n            const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n            const items = Array.from(context.itemMap.values());\n            const orderedItems = items.sort((a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n            return orderedItems;\n        }, [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n// src/collection.tsx\n\n\n\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n    #keys;\n    constructor(entries){\n        super(entries);\n        this.#keys = [\n            ...super.keys()\n        ];\n        __instanciated.set(this, true);\n    }\n    set(key, value) {\n        if (__instanciated.get(this)) {\n            if (this.has(key)) {\n                this.#keys[this.#keys.indexOf(key)] = key;\n            } else {\n                this.#keys.push(key);\n            }\n        }\n        super.set(key, value);\n        return this;\n    }\n    insert(index, key, value) {\n        const has = this.has(key);\n        const length = this.#keys.length;\n        const relativeIndex = toSafeInteger(index);\n        let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n        const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n        if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n            this.set(key, value);\n            return this;\n        }\n        const size = this.size + (has ? 0 : 1);\n        if (relativeIndex < 0) {\n            actualIndex++;\n        }\n        const keys = [\n            ...this.#keys\n        ];\n        let nextValue;\n        let shouldSkip = false;\n        for(let i = actualIndex; i < size; i++){\n            if (actualIndex === i) {\n                let nextKey = keys[i];\n                if (keys[i] === key) {\n                    nextKey = keys[i + 1];\n                }\n                if (has) {\n                    this.delete(key);\n                }\n                nextValue = this.get(nextKey);\n                this.set(key, value);\n            } else {\n                if (!shouldSkip && keys[i - 1] === key) {\n                    shouldSkip = true;\n                }\n                const currentKey = keys[shouldSkip ? i : i - 1];\n                const currentValue = nextValue;\n                nextValue = this.get(currentKey);\n                this.delete(currentKey);\n                this.set(currentKey, currentValue);\n            }\n        }\n        return this;\n    }\n    with(index, key, value) {\n        const copy = new _OrderedDict(this);\n        copy.insert(index, key, value);\n        return copy;\n    }\n    before(key) {\n        const index = this.#keys.indexOf(key) - 1;\n        if (index < 0) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position before the given key.\n   */ setBefore(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index, newKey, value);\n    }\n    after(key) {\n        let index = this.#keys.indexOf(key);\n        index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n        if (index === -1) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position after the given key.\n   */ setAfter(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index + 1, newKey, value);\n    }\n    first() {\n        return this.entryAt(0);\n    }\n    last() {\n        return this.entryAt(-1);\n    }\n    clear() {\n        this.#keys = [];\n        return super.clear();\n    }\n    delete(key) {\n        const deleted = super.delete(key);\n        if (deleted) {\n            this.#keys.splice(this.#keys.indexOf(key), 1);\n        }\n        return deleted;\n    }\n    deleteAt(index) {\n        const key = this.keyAt(index);\n        if (key !== void 0) {\n            return this.delete(key);\n        }\n        return false;\n    }\n    at(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return this.get(key);\n        }\n    }\n    entryAt(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return [\n                key,\n                this.get(key)\n            ];\n        }\n    }\n    indexOf(key) {\n        return this.#keys.indexOf(key);\n    }\n    keyAt(index) {\n        return at(this.#keys, index);\n    }\n    from(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.at(dest);\n    }\n    keyFrom(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.keyAt(dest);\n    }\n    find(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return entry;\n            }\n            index++;\n        }\n        return void 0;\n    }\n    findIndex(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return index;\n            }\n            index++;\n        }\n        return -1;\n    }\n    filter(predicate, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                entries.push(entry);\n            }\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    map(callbackfn, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            entries.push([\n                entry[0],\n                Reflect.apply(callbackfn, thisArg, [\n                    entry,\n                    index,\n                    this\n                ])\n            ]);\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    reduce(...args) {\n        const [callbackfn, initialValue] = args;\n        let index = 0;\n        let accumulator = initialValue ?? this.at(0);\n        for (const entry of this){\n            if (index === 0 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n            index++;\n        }\n        return accumulator;\n    }\n    reduceRight(...args) {\n        const [callbackfn, initialValue] = args;\n        let accumulator = initialValue ?? this.at(-1);\n        for(let index = this.size - 1; index >= 0; index--){\n            const entry = this.at(index);\n            if (index === this.size - 1 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n        }\n        return accumulator;\n    }\n    toSorted(compareFn) {\n        const entries = [\n            ...this.entries()\n        ].sort(compareFn);\n        return new _OrderedDict(entries);\n    }\n    toReversed() {\n        const reversed = new _OrderedDict();\n        for(let index = this.size - 1; index >= 0; index--){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            reversed.set(key, element);\n        }\n        return reversed;\n    }\n    toSpliced(...args) {\n        const entries = [\n            ...this.entries()\n        ];\n        entries.splice(...args);\n        return new _OrderedDict(entries);\n    }\n    slice(start, end) {\n        const result = new _OrderedDict();\n        let stop = this.size - 1;\n        if (start === void 0) {\n            return result;\n        }\n        if (start < 0) {\n            start = start + this.size;\n        }\n        if (end !== void 0 && end > 0) {\n            stop = end - 1;\n        }\n        for(let index = start; index <= stop; index++){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            result.set(key, element);\n        }\n        return result;\n    }\n    every(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (!Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return false;\n            }\n            index++;\n        }\n        return true;\n    }\n    some(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return true;\n            }\n            index++;\n        }\n        return false;\n    }\n};\nfunction at(array, index) {\n    if (\"at\" in Array.prototype) {\n        return Array.prototype.at.call(array, index);\n    }\n    const actualIndex = toSafeIndex(array, index);\n    return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n    const length = array.length;\n    const relativeIndex = toSafeInteger(index);\n    const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n    return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n// src/collection.tsx\n\nfunction createCollection2(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionElement: null,\n        collectionRef: {\n            current: null\n        },\n        collectionRefObject: {\n            current: null\n        },\n        itemMap: new OrderedDict(),\n        setItemMap: ()=>void 0\n    });\n    const CollectionProvider = ({ state, ...props })=>{\n        return state ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionInit, {\n            ...props\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const CollectionInit = (props)=>{\n        const state = useInitCollection();\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        });\n    };\n    CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n    const CollectionProviderImpl = (props)=>{\n        const { scope, children, state } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [collectionElement, setCollectionElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(ref, setCollectionElement);\n        const [itemMap, setItemMap] = state;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            if (!collectionElement) return;\n            const observer = getChildListObserver(()=>{});\n            observer.observe(collectionElement, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                observer.disconnect();\n            };\n        }, [\n            collectionElement\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionContextProvider, {\n            scope,\n            itemMap,\n            setItemMap,\n            collectionRef: composeRefs,\n            collectionRefObject: ref,\n            collectionElement,\n            children\n        });\n    };\n    CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [element, setElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref, setElement);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        const { setItemMap } = context;\n        const itemDataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(itemData);\n        if (!shallowEqual(itemDataRef.current, itemData)) {\n            itemDataRef.current = itemData;\n        }\n        const memoizedItemData = itemDataRef.current;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const itemData2 = memoizedItemData;\n            setItemMap((map)=>{\n                if (!element) {\n                    return map;\n                }\n                if (!map.has(element)) {\n                    map.set(element, {\n                        ...itemData2,\n                        element\n                    });\n                    return map.toSorted(sortByDocumentPosition);\n                }\n                return map.set(element, {\n                    ...itemData2,\n                    element\n                }).toSorted(sortByDocumentPosition);\n            });\n            return ()=>{\n                setItemMap((map)=>{\n                    if (!element || !map.has(element)) {\n                        return map;\n                    }\n                    map.delete(element);\n                    return new OrderedDict(map);\n                });\n            };\n        }, [\n            element,\n            memoizedItemData,\n            setItemMap\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useInitCollection() {\n        return react__WEBPACK_IMPORTED_MODULE_0__.useState(new OrderedDict());\n    }\n    function useCollection(scope) {\n        const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n        return itemMap;\n    }\n    const functions = {\n        createCollectionScope,\n        useCollection,\n        useInitCollection\n    };\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        functions\n    ];\n}\nfunction shallowEqual(a, b) {\n    if (a === b) return true;\n    if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n    if (a == null || b == null) return false;\n    const keysA = Object.keys(a);\n    const keysB = Object.keys(b);\n    if (keysA.length !== keysB.length) return false;\n    for (const key of keysA){\n        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n        if (a[key] !== b[key]) return false;\n    }\n    return true;\n}\nfunction isElementPreceding(a, b) {\n    return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n    return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n    const observer = new MutationObserver((mutationsList)=>{\n        for (const mutation of mutationsList){\n            if (mutation.type === \"childList\") {\n                callback();\n                return;\n            }\n        }\n    });\n    return observer;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSx3QkFBd0IscUJBQXFCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyw4Q0FBaUI7QUFDMUI7QUFJRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5kcm9pZHdlYi1lbnRlcnByaXNlLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1jb21wb3NlLXJlZnMvZGlzdC9pbmRleC5tanM/NTE3ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9jb21wb3NlLXJlZnMvc3JjL2NvbXBvc2UtcmVmcy50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gc2V0UmVmKHJlZiwgdmFsdWUpIHtcbiAgaWYgKHR5cGVvZiByZWYgPT09IFwiZnVuY3Rpb25cIikge1xuICAgIHJldHVybiByZWYodmFsdWUpO1xuICB9IGVsc2UgaWYgKHJlZiAhPT0gbnVsbCAmJiByZWYgIT09IHZvaWQgMCkge1xuICAgIHJlZi5jdXJyZW50ID0gdmFsdWU7XG4gIH1cbn1cbmZ1bmN0aW9uIGNvbXBvc2VSZWZzKC4uLnJlZnMpIHtcbiAgcmV0dXJuIChub2RlKSA9PiB7XG4gICAgbGV0IGhhc0NsZWFudXAgPSBmYWxzZTtcbiAgICBjb25zdCBjbGVhbnVwcyA9IHJlZnMubWFwKChyZWYpID0+IHtcbiAgICAgIGNvbnN0IGNsZWFudXAgPSBzZXRSZWYocmVmLCBub2RlKTtcbiAgICAgIGlmICghaGFzQ2xlYW51cCAmJiB0eXBlb2YgY2xlYW51cCA9PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgaGFzQ2xlYW51cCA9IHRydWU7XG4gICAgICB9XG4gICAgICByZXR1cm4gY2xlYW51cDtcbiAgICB9KTtcbiAgICBpZiAoaGFzQ2xlYW51cCkge1xuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjbGVhbnVwcy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgIGNvbnN0IGNsZWFudXAgPSBjbGVhbnVwc1tpXTtcbiAgICAgICAgICBpZiAodHlwZW9mIGNsZWFudXAgPT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICBjbGVhbnVwKCk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHNldFJlZihyZWZzW2ldLCBudWxsKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfVxuICB9O1xufVxuZnVuY3Rpb24gdXNlQ29tcG9zZWRSZWZzKC4uLnJlZnMpIHtcbiAgcmV0dXJuIFJlYWN0LnVzZUNhbGxiYWNrKGNvbXBvc2VSZWZzKC4uLnJlZnMpLCByZWZzKTtcbn1cbmV4cG9ydCB7XG4gIGNvbXBvc2VSZWZzLFxuICB1c2VDb21wb3NlZFJlZnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Label,Root auto */ // src/label.tsx\n\n\n\nvar NAME = \"Label\";\nvar Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label, {\n        ...props,\n        ref: forwardedRef,\n        onMouseDown: (event)=>{\n            const target = event.target;\n            if (target.closest(\"button, input, select, textarea\")) return;\n            props.onMouseDown?.(event);\n            if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n        }\n    });\n});\nLabel.displayName = NAME;\nvar Root = Label;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>setMounted(true), []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            const ownerWindow = node.ownerDocument.defaultView ?? window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            stylesRef.current = node2 ? getComputedStyle(node2) : null;\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n          return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n        props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n      }\n      return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-toast/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Action: () => (/* binding */ Action),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createToastScope: () => (/* binding */ createToastScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Action,Close,Description,Provider,Root,Title,Toast,ToastAction,ToastClose,ToastDescription,ToastProvider,ToastTitle,ToastViewport,Viewport,createToastScope auto */ // src/toast.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(\"Toast\");\nvar [createToastContext, createToastScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(\"Toast\", [\n    createCollectionScope\n]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props)=>{\n    const { __scopeToast, label = \"Notification\", duration = 5e3, swipeDirection = \"right\", swipeThreshold = 50, children } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [toastCount, setToastCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const isFocusedToastEscapeKeyDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isClosePausedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    if (!label.trim()) {\n        console.error(`Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n        scope: __scopeToast,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastProviderProvider, {\n            scope: __scopeToast,\n            label,\n            duration,\n            swipeDirection,\n            swipeThreshold,\n            toastCount,\n            viewport,\n            onViewportChange: setViewport,\n            onToastAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount + 1), []),\n            onToastRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount - 1), []),\n            isFocusedToastEscapeKeyDownRef,\n            isClosePausedRef,\n            children\n        })\n    });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\n    \"F8\"\n];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, hotkey = VIEWPORT_DEFAULT_HOTKEY, label = \"Notifications ({hotkey})\", ...viewportProps } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const headFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const tailFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            const isHotkeyPressed = hotkey.length !== 0 && hotkey.every((key)=>event[key] || event.code === key);\n            if (isHotkeyPressed) ref.current?.focus();\n        };\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const wrapper = wrapperRef.current;\n        const viewport = ref.current;\n        if (hasToasts && wrapper && viewport) {\n            const handlePause = ()=>{\n                if (!context.isClosePausedRef.current) {\n                    const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n                    viewport.dispatchEvent(pauseEvent);\n                    context.isClosePausedRef.current = true;\n                }\n            };\n            const handleResume = ()=>{\n                if (context.isClosePausedRef.current) {\n                    const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n                    viewport.dispatchEvent(resumeEvent);\n                    context.isClosePausedRef.current = false;\n                }\n            };\n            const handleFocusOutResume = (event)=>{\n                const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n                if (isFocusMovingOutside) handleResume();\n            };\n            const handlePointerLeaveResume = ()=>{\n                const isFocusInside = wrapper.contains(document.activeElement);\n                if (!isFocusInside) handleResume();\n            };\n            wrapper.addEventListener(\"focusin\", handlePause);\n            wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n            wrapper.addEventListener(\"pointermove\", handlePause);\n            wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n            window.addEventListener(\"blur\", handlePause);\n            window.addEventListener(\"focus\", handleResume);\n            return ()=>{\n                wrapper.removeEventListener(\"focusin\", handlePause);\n                wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n                wrapper.removeEventListener(\"pointermove\", handlePause);\n                wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n                window.removeEventListener(\"blur\", handlePause);\n                window.removeEventListener(\"focus\", handleResume);\n            };\n        }\n    }, [\n        hasToasts,\n        context.isClosePausedRef\n    ]);\n    const getSortedTabbableCandidates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(({ tabbingDirection })=>{\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem)=>{\n            const toastNode = toastItem.ref.current;\n            const toastTabbableCandidates = [\n                toastNode,\n                ...getTabbableCandidates(toastNode)\n            ];\n            return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n        });\n        return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n    }, [\n        getItems\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = ref.current;\n        if (viewport) {\n            const handleKeyDown = (event)=>{\n                const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                if (isTabKey) {\n                    const focusedElement = document.activeElement;\n                    const isTabbingBackwards = event.shiftKey;\n                    const targetIsViewport = event.target === viewport;\n                    if (targetIsViewport && isTabbingBackwards) {\n                        headFocusProxyRef.current?.focus();\n                        return;\n                    }\n                    const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n                    const sortedCandidates = getSortedTabbableCandidates({\n                        tabbingDirection\n                    });\n                    const index = sortedCandidates.findIndex((candidate)=>candidate === focusedElement);\n                    if (focusFirst(sortedCandidates.slice(index + 1))) {\n                        event.preventDefault();\n                    } else {\n                        isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n                    }\n                }\n            };\n            viewport.addEventListener(\"keydown\", handleKeyDown);\n            return ()=>viewport.removeEventListener(\"keydown\", handleKeyDown);\n        }\n    }, [\n        getItems,\n        getSortedTabbableCandidates\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Branch, {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: {\n            pointerEvents: hasToasts ? void 0 : \"none\"\n        },\n        children: [\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: headFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"forwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeToast,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.ol, {\n                    tabIndex: -1,\n                    ...viewportProps,\n                    ref: composedRefs\n                })\n            }),\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: tailFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"backwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            })\n        ]\n    });\n});\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: {\n            position: \"fixed\"\n        },\n        onFocus: (event)=>{\n            const prevFocusedElement = event.relatedTarget;\n            const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n            if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n    });\n});\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? true,\n        onChange: onOpenChange,\n        caller: TOAST_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastImpl, {\n            open,\n            ...toastProps,\n            ref: forwardedRef,\n            onClose: ()=>setOpen(false),\n            onPause: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onPause),\n            onResume: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onResume),\n            onSwipeStart: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeStart, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n            }),\n            onSwipeMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeMove, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n            }),\n            onSwipeCancel: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeCancel, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n            }),\n            onSwipeEnd: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeEnd, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n                setOpen(false);\n            })\n        })\n    });\n});\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n    onClose () {}\n});\nvar ToastImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, type = \"foreground\", duration: durationProp, open, onClose, onEscapeKeyDown, onPause, onResume, onSwipeStart, onSwipeMove, onSwipeCancel, onSwipeEnd, ...toastProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const swipeDeltaRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRemainingTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(duration);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(()=>{\n        const isFocusInToast = node?.contains(document.activeElement);\n        if (isFocusInToast) context.viewport?.focus();\n        onClose();\n    });\n    const startTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((duration2)=>{\n        if (!duration2 || duration2 === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = /* @__PURE__ */ new Date().getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration2);\n    }, [\n        handleClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = context.viewport;\n        if (viewport) {\n            const handleResume = ()=>{\n                startTimer(closeTimerRemainingTimeRef.current);\n                onResume?.();\n            };\n            const handlePause = ()=>{\n                const elapsedTime = /* @__PURE__ */ new Date().getTime() - closeTimerStartTimeRef.current;\n                closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n                window.clearTimeout(closeTimerRef.current);\n                onPause?.();\n            };\n            viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n            viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n            return ()=>{\n                viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n                viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n            };\n        }\n    }, [\n        context.viewport,\n        duration,\n        onPause,\n        onResume,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [\n        open,\n        duration,\n        context.isClosePausedRef,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        onToastAdd();\n        return ()=>onToastRemove();\n    }, [\n        onToastAdd,\n        onToastRemove\n    ]);\n    const announceTextContent = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return node ? getAnnounceTextContent(node) : null;\n    }, [\n        node\n    ]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            announceTextContent && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounce, {\n                __scopeToast,\n                role: \"status\",\n                \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n                \"aria-atomic\": true,\n                children: announceTextContent\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastInteractiveProvider, {\n                scope: __scopeToast,\n                onClose: handleClose,\n                children: /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                    scope: __scopeToast,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Root, {\n                        asChild: true,\n                        onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEscapeKeyDown, ()=>{\n                            if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                            context.isFocusedToastEscapeKeyDownRef.current = false;\n                        }),\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.li, {\n                            role: \"status\",\n                            \"aria-live\": \"off\",\n                            \"aria-atomic\": true,\n                            tabIndex: 0,\n                            \"data-state\": open ? \"open\" : \"closed\",\n                            \"data-swipe-direction\": context.swipeDirection,\n                            ...toastProps,\n                            ref: composedRefs,\n                            style: {\n                                userSelect: \"none\",\n                                touchAction: \"none\",\n                                ...props.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                                if (event.key !== \"Escape\") return;\n                                onEscapeKeyDown?.(event.nativeEvent);\n                                if (!event.nativeEvent.defaultPrevented) {\n                                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                                    handleClose();\n                                }\n                            }),\n                            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                                if (event.button !== 0) return;\n                                pointerStartRef.current = {\n                                    x: event.clientX,\n                                    y: event.clientY\n                                };\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                                if (!pointerStartRef.current) return;\n                                const x = event.clientX - pointerStartRef.current.x;\n                                const y = event.clientY - pointerStartRef.current.y;\n                                const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                                const isHorizontalSwipe = [\n                                    \"left\",\n                                    \"right\"\n                                ].includes(context.swipeDirection);\n                                const clamp = [\n                                    \"left\",\n                                    \"up\"\n                                ].includes(context.swipeDirection) ? Math.min : Math.max;\n                                const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                                const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                                const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                                const delta = {\n                                    x: clampedX,\n                                    y: clampedY\n                                };\n                                const eventDetail = {\n                                    originalEvent: event,\n                                    delta\n                                };\n                                if (hasSwipeMoveStarted) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                                        discrete: false\n                                    });\n                                } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                                        discrete: false\n                                    });\n                                    event.target.setPointerCapture(event.pointerId);\n                                } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                                    pointerStartRef.current = null;\n                                }\n                            }),\n                            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                                const delta = swipeDeltaRef.current;\n                                const target = event.target;\n                                if (target.hasPointerCapture(event.pointerId)) {\n                                    target.releasePointerCapture(event.pointerId);\n                                }\n                                swipeDeltaRef.current = null;\n                                pointerStartRef.current = null;\n                                if (delta) {\n                                    const toast = event.currentTarget;\n                                    const eventDetail = {\n                                        originalEvent: event,\n                                        delta\n                                    };\n                                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                                            discrete: true\n                                        });\n                                    } else {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_CANCEL, onSwipeCancel, eventDetail, {\n                                            discrete: true\n                                        });\n                                    }\n                                    toast.addEventListener(\"click\", (event2)=>event2.preventDefault(), {\n                                        once: true\n                                    });\n                                }\n                            })\n                        })\n                    })\n                }), context.viewport)\n            })\n        ]\n    });\n});\nvar ToastAnnounce = (props)=>{\n    const { __scopeToast, children, ...announceProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [renderAnnounceText, setRenderAnnounceText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isAnnounced, setIsAnnounced] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useNextFrame(()=>setRenderAnnounceText(true));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const timer = window.setTimeout(()=>setIsAnnounced(true), 1e3);\n        return ()=>window.clearTimeout(timer);\n    }, []);\n    return isAnnounced ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n            ...announceProps,\n            children: renderAnnounceText && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    context.label,\n                    \" \",\n                    children\n                ]\n            })\n        })\n    });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n        console.error(`Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`);\n        return null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        altText,\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastClose, {\n            ...actionProps,\n            ref: forwardedRef\n        })\n    });\n});\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            type: \"button\",\n            ...closeProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, interactiveContext.onClose)\n        })\n    });\n});\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, altText, ...announceExcludeProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        \"data-radix-toast-announce-exclude\": \"\",\n        \"data-radix-toast-announce-alt\": altText || void 0,\n        ...announceExcludeProps,\n        ref: forwardedRef\n    });\n});\nfunction getAnnounceTextContent(container) {\n    const textContent = [];\n    const childNodes = Array.from(container.childNodes);\n    childNodes.forEach((node)=>{\n        if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n        if (isHTMLElement(node)) {\n            const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n            const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n            if (!isHidden) {\n                if (isExcluded) {\n                    const altText = node.dataset.radixToastAnnounceAlt;\n                    if (altText) textContent.push(altText);\n                } else {\n                    textContent.push(...getAnnounceTextContent(node));\n                }\n            }\n        }\n    });\n    return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const currentTarget = detail.originalEvent.currentTarget;\n    const event = new CustomEvent(name, {\n        bubbles: true,\n        cancelable: true,\n        detail\n    });\n    if (handler) currentTarget.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.dispatchDiscreteCustomEvent)(currentTarget, event);\n    } else {\n        currentTarget.dispatchEvent(event);\n    }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0)=>{\n    const deltaX = Math.abs(delta.x);\n    const deltaY = Math.abs(delta.y);\n    const isDeltaX = deltaX > deltaY;\n    if (direction === \"left\" || direction === \"right\") {\n        return isDeltaX && deltaX > threshold;\n    } else {\n        return !isDeltaX && deltaY > threshold;\n    }\n};\nfunction useNextFrame(callback = ()=>{}) {\n    const fn = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(callback);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__.useLayoutEffect)(()=>{\n        let raf1 = 0;\n        let raf2 = 0;\n        raf1 = window.requestAnimationFrame(()=>raf2 = window.requestAnimationFrame(fn));\n        return ()=>{\n            window.cancelAnimationFrame(raf1);\n            window.cancelAnimationFrame(raf2);\n        };\n    }, [\n        fn\n    ]);\n}\nfunction isHTMLElement(node) {\n    return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5kcm9pZHdlYi1lbnRlcnByaXNlLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmL2Rpc3QvaW5kZXgubWpzPzgwZGUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWNhbGxiYWNrLXJlZi9zcmMvdXNlLWNhbGxiYWNrLXJlZi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlQ2FsbGJhY2tSZWYoY2FsbGJhY2spIHtcbiAgY29uc3QgY2FsbGJhY2tSZWYgPSBSZWFjdC51c2VSZWYoY2FsbGJhY2spO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNhbGxiYWNrUmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgfSk7XG4gIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+ICguLi5hcmdzKSA9PiBjYWxsYmFja1JlZi5jdXJyZW50Py4oLi4uYXJncyksIFtdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUNhbGxiYWNrUmVmXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n  if (typeof useReactEffectEvent === \"function\") {\n    return useReactEffectEvent(callback);\n  }\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  if (typeof useReactInsertionEffect === \"function\") {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n      ref.current = callback;\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => ref.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lZmZlY3QtZXZlbnQvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ29FO0FBQ3JDO0FBQy9CLDBCQUEwQix5TEFBSztBQUMvQiw4QkFBOEIseUxBQUs7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLHlDQUFZO0FBQzFCO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxJQUFJO0FBQ0osSUFBSSxrRkFBZTtBQUNuQjtBQUNBLEtBQUs7QUFDTDtBQUNBLFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5kcm9pZHdlYi1lbnRlcnByaXNlLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtZWZmZWN0LWV2ZW50L2Rpc3QvaW5kZXgubWpzPzEzYzQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3VzZS1lZmZlY3QtZXZlbnQudHN4XG5pbXBvcnQgeyB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0XCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VSZWFjdEVmZmVjdEV2ZW50ID0gUmVhY3RbXCIgdXNlRWZmZWN0RXZlbnQgXCIudHJpbSgpLnRvU3RyaW5nKCldO1xudmFyIHVzZVJlYWN0SW5zZXJ0aW9uRWZmZWN0ID0gUmVhY3RbXCIgdXNlSW5zZXJ0aW9uRWZmZWN0IFwiLnRyaW0oKS50b1N0cmluZygpXTtcbmZ1bmN0aW9uIHVzZUVmZmVjdEV2ZW50KGNhbGxiYWNrKSB7XG4gIGlmICh0eXBlb2YgdXNlUmVhY3RFZmZlY3RFdmVudCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgcmV0dXJuIHVzZVJlYWN0RWZmZWN0RXZlbnQoY2FsbGJhY2spO1xuICB9XG4gIGNvbnN0IHJlZiA9IFJlYWN0LnVzZVJlZigoKSA9PiB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiQ2Fubm90IGNhbGwgYW4gZXZlbnQgaGFuZGxlciB3aGlsZSByZW5kZXJpbmcuXCIpO1xuICB9KTtcbiAgaWYgKHR5cGVvZiB1c2VSZWFjdEluc2VydGlvbkVmZmVjdCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgdXNlUmVhY3RJbnNlcnRpb25FZmZlY3QoKCkgPT4ge1xuICAgICAgcmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgICB9KTtcbiAgfSBlbHNlIHtcbiAgICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgICAgcmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgICB9KTtcbiAgfVxuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiAoLi4uYXJncykgPT4gcmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlRWZmZWN0RXZlbnRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUNtQztBQUNsRTtBQUNBLDBCQUEwQixnRkFBYztBQUN4QyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsZUFBZTtBQUM5RSwrRUFBK0UsZUFBZTtBQUM5RixHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5kcm9pZHdlYi1lbnRlcnByaXNlLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtZXNjYXBlLWtleWRvd24vZGlzdC9pbmRleC5tanM/OGZhMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtZXNjYXBlLWtleWRvd24vc3JjL3VzZS1lc2NhcGUta2V5ZG93bi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2tSZWYgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWZcIjtcbmZ1bmN0aW9uIHVzZUVzY2FwZUtleWRvd24ob25Fc2NhcGVLZXlEb3duUHJvcCwgb3duZXJEb2N1bWVudCA9IGdsb2JhbFRoaXM/LmRvY3VtZW50KSB7XG4gIGNvbnN0IG9uRXNjYXBlS2V5RG93biA9IHVzZUNhbGxiYWNrUmVmKG9uRXNjYXBlS2V5RG93blByb3ApO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUtleURvd24gPSAoZXZlbnQpID0+IHtcbiAgICAgIGlmIChldmVudC5rZXkgPT09IFwiRXNjYXBlXCIpIHtcbiAgICAgICAgb25Fc2NhcGVLZXlEb3duKGV2ZW50KTtcbiAgICAgIH1cbiAgICB9O1xuICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93biwgeyBjYXB0dXJlOiB0cnVlIH0pO1xuICAgIHJldHVybiAoKSA9PiBvd25lckRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24sIHsgY2FwdHVyZTogdHJ1ZSB9KTtcbiAgfSwgW29uRXNjYXBlS2V5RG93biwgb3duZXJEb2N1bWVudF0pO1xufVxuZXhwb3J0IHtcbiAgdXNlRXNjYXBlS2V5ZG93blxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsOENBQThDLGtEQUFxQjtBQUNuRTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmRyb2lkd2ViLWVudGVycHJpc2UvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzPzJiYTEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWxheW91dC1lZmZlY3Qvc3JjL3VzZS1sYXlvdXQtZWZmZWN0LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlTGF5b3V0RWZmZWN0MiA9IGdsb2JhbFRoaXM/LmRvY3VtZW50ID8gUmVhY3QudXNlTGF5b3V0RWZmZWN0IDogKCkgPT4ge1xufTtcbmV4cG9ydCB7XG4gIHVzZUxheW91dEVmZmVjdDIgYXMgdXNlTGF5b3V0RWZmZWN0XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-previous/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-previous/src/use-previous.tsx\n\nfunction usePrevious(value) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef({ value, previous: value });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1wcmV2aW91cy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQytCO0FBQy9CO0FBQ0EsY0FBYyx5Q0FBWSxHQUFHLHdCQUF3QjtBQUNyRCxTQUFTLDBDQUFhO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5kcm9pZHdlYi1lbnRlcnByaXNlLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtcHJldmlvdXMvZGlzdC9pbmRleC5tanM/YmUxYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtcHJldmlvdXMvc3JjL3VzZS1wcmV2aW91cy50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlUHJldmlvdXModmFsdWUpIHtcbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmKHsgdmFsdWUsIHByZXZpb3VzOiB2YWx1ZSB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4ge1xuICAgIGlmIChyZWYuY3VycmVudC52YWx1ZSAhPT0gdmFsdWUpIHtcbiAgICAgIHJlZi5jdXJyZW50LnByZXZpb3VzID0gcmVmLmN1cnJlbnQudmFsdWU7XG4gICAgICByZWYuY3VycmVudC52YWx1ZSA9IHZhbHVlO1xuICAgIH1cbiAgICByZXR1cm4gcmVmLmN1cnJlbnQucHJldmlvdXM7XG4gIH0sIFt2YWx1ZV0pO1xufVxuZXhwb3J0IHtcbiAgdXNlUHJldmlvdXNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-size/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSize: () => (/* binding */ useSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/use-size/src/use-size.tsx\n\n\nfunction useSize(element) {\n  const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(void 0);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VISUALLY_HIDDEN_STYLES: () => (/* binding */ VISUALLY_HIDDEN_STYLES),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/visually-hidden.tsx\n\n\n\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXZpc3VhbGx5LWhpZGRlbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTtBQUMrQjtBQUN1QjtBQUNkO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLHFCQUFxQiw2Q0FBZ0I7QUFDckM7QUFDQSwyQkFBMkIsc0RBQUc7QUFDOUIsTUFBTSxnRUFBUztBQUNmO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFLRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5kcm9pZHdlYi1lbnRlcnByaXNlLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC12aXN1YWxseS1oaWRkZW4vZGlzdC9pbmRleC5tanM/ZTNjZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdmlzdWFsbHktaGlkZGVuLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZVwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgVklTVUFMTFlfSElEREVOX1NUWUxFUyA9IE9iamVjdC5mcmVlemUoe1xuICAvLyBTZWU6IGh0dHBzOi8vZ2l0aHViLmNvbS90d2JzL2Jvb3RzdHJhcC9ibG9iL21haW4vc2Nzcy9taXhpbnMvX3Zpc3VhbGx5LWhpZGRlbi5zY3NzXG4gIHBvc2l0aW9uOiBcImFic29sdXRlXCIsXG4gIGJvcmRlcjogMCxcbiAgd2lkdGg6IDEsXG4gIGhlaWdodDogMSxcbiAgcGFkZGluZzogMCxcbiAgbWFyZ2luOiAtMSxcbiAgb3ZlcmZsb3c6IFwiaGlkZGVuXCIsXG4gIGNsaXA6IFwicmVjdCgwLCAwLCAwLCAwKVwiLFxuICB3aGl0ZVNwYWNlOiBcIm5vd3JhcFwiLFxuICB3b3JkV3JhcDogXCJub3JtYWxcIlxufSk7XG52YXIgTkFNRSA9IFwiVmlzdWFsbHlIaWRkZW5cIjtcbnZhciBWaXN1YWxseUhpZGRlbiA9IFJlYWN0LmZvcndhcmRSZWYoXG4gIChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgICBQcmltaXRpdmUuc3BhbixcbiAgICAgIHtcbiAgICAgICAgLi4ucHJvcHMsXG4gICAgICAgIHJlZjogZm9yd2FyZGVkUmVmLFxuICAgICAgICBzdHlsZTogeyAuLi5WSVNVQUxMWV9ISURERU5fU1RZTEVTLCAuLi5wcm9wcy5zdHlsZSB9XG4gICAgICB9XG4gICAgKTtcbiAgfVxuKTtcblZpc3VhbGx5SGlkZGVuLmRpc3BsYXlOYW1lID0gTkFNRTtcbnZhciBSb290ID0gVmlzdWFsbHlIaWRkZW47XG5leHBvcnQge1xuICBSb290LFxuICBWSVNVQUxMWV9ISURERU5fU1RZTEVTLFxuICBWaXN1YWxseUhpZGRlblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;