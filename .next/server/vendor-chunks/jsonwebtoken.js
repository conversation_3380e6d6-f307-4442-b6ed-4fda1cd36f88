/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsonwebtoken";
exports.ids = ["vendor-chunks/jsonwebtoken"];
exports.modules = {

/***/ "(rsc)/./node_modules/jsonwebtoken/decode.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/decode.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\n\nmodule.exports = function (jwt, options) {\n  options = options || {};\n  var decoded = jws.decode(jwt, options);\n  if (!decoded) { return null; }\n  var payload = decoded.payload;\n\n  //try parse the payload\n  if(typeof payload === 'string') {\n    try {\n      var obj = JSON.parse(payload);\n      if(obj !== null && typeof obj === 'object') {\n        payload = obj;\n      }\n    } catch (e) { }\n  }\n\n  //return header if `complete` option is enabled.  header includes claims\n  //such as `kid` and `alg` used to select the key within a JWKS needed to\n  //verify the signature\n  if (options.complete === true) {\n    return {\n      header: decoded.header,\n      payload: payload,\n      signature: decoded.signature\n    };\n  }\n  return payload;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2RlY29kZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxVQUFVLG1CQUFPLENBQUMsOENBQUs7O0FBRXZCO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmRyb2lkd2ViLWVudGVycHJpc2UvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2RlY29kZS5qcz8yYjU0Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBqd3MgPSByZXF1aXJlKCdqd3MnKTtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoand0LCBvcHRpb25zKSB7XG4gIG9wdGlvbnMgPSBvcHRpb25zIHx8IHt9O1xuICB2YXIgZGVjb2RlZCA9IGp3cy5kZWNvZGUoand0LCBvcHRpb25zKTtcbiAgaWYgKCFkZWNvZGVkKSB7IHJldHVybiBudWxsOyB9XG4gIHZhciBwYXlsb2FkID0gZGVjb2RlZC5wYXlsb2FkO1xuXG4gIC8vdHJ5IHBhcnNlIHRoZSBwYXlsb2FkXG4gIGlmKHR5cGVvZiBwYXlsb2FkID09PSAnc3RyaW5nJykge1xuICAgIHRyeSB7XG4gICAgICB2YXIgb2JqID0gSlNPTi5wYXJzZShwYXlsb2FkKTtcbiAgICAgIGlmKG9iaiAhPT0gbnVsbCAmJiB0eXBlb2Ygb2JqID09PSAnb2JqZWN0Jykge1xuICAgICAgICBwYXlsb2FkID0gb2JqO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGUpIHsgfVxuICB9XG5cbiAgLy9yZXR1cm4gaGVhZGVyIGlmIGBjb21wbGV0ZWAgb3B0aW9uIGlzIGVuYWJsZWQuICBoZWFkZXIgaW5jbHVkZXMgY2xhaW1zXG4gIC8vc3VjaCBhcyBga2lkYCBhbmQgYGFsZ2AgdXNlZCB0byBzZWxlY3QgdGhlIGtleSB3aXRoaW4gYSBKV0tTIG5lZWRlZCB0b1xuICAvL3ZlcmlmeSB0aGUgc2lnbmF0dXJlXG4gIGlmIChvcHRpb25zLmNvbXBsZXRlID09PSB0cnVlKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGhlYWRlcjogZGVjb2RlZC5oZWFkZXIsXG4gICAgICBwYXlsb2FkOiBwYXlsb2FkLFxuICAgICAgc2lnbmF0dXJlOiBkZWNvZGVkLnNpZ25hdHVyZVxuICAgIH07XG4gIH1cbiAgcmV0dXJuIHBheWxvYWQ7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/decode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/index.js":
/*!********************************************!*\
  !*** ./node_modules/jsonwebtoken/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = {\n  decode: __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\"),\n  verify: __webpack_require__(/*! ./verify */ \"(rsc)/./node_modules/jsonwebtoken/verify.js\"),\n  sign: __webpack_require__(/*! ./sign */ \"(rsc)/./node_modules/jsonwebtoken/sign.js\"),\n  JsonWebTokenError: __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\"),\n  NotBeforeError: __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\"),\n  TokenExpiredError: __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\"),\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0EsVUFBVSxtQkFBTyxDQUFDLDZEQUFVO0FBQzVCLFVBQVUsbUJBQU8sQ0FBQyw2REFBVTtBQUM1QixRQUFRLG1CQUFPLENBQUMseURBQVE7QUFDeEIscUJBQXFCLG1CQUFPLENBQUMsMkZBQXlCO0FBQ3RELGtCQUFrQixtQkFBTyxDQUFDLHFGQUFzQjtBQUNoRCxxQkFBcUIsbUJBQU8sQ0FBQywyRkFBeUI7QUFDdEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmRyb2lkd2ViLWVudGVycHJpc2UvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2luZGV4LmpzP2NkYTEiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSB7XG4gIGRlY29kZTogcmVxdWlyZSgnLi9kZWNvZGUnKSxcbiAgdmVyaWZ5OiByZXF1aXJlKCcuL3ZlcmlmeScpLFxuICBzaWduOiByZXF1aXJlKCcuL3NpZ24nKSxcbiAgSnNvbldlYlRva2VuRXJyb3I6IHJlcXVpcmUoJy4vbGliL0pzb25XZWJUb2tlbkVycm9yJyksXG4gIE5vdEJlZm9yZUVycm9yOiByZXF1aXJlKCcuL2xpYi9Ob3RCZWZvcmVFcnJvcicpLFxuICBUb2tlbkV4cGlyZWRFcnJvcjogcmVxdWlyZSgnLi9saWIvVG9rZW5FeHBpcmVkRXJyb3InKSxcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/JsonWebTokenError.js ***!
  \************************************************************/
/***/ ((module) => {

eval("var JsonWebTokenError = function (message, error) {\n  Error.call(this, message);\n  if(Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  }\n  this.name = 'JsonWebTokenError';\n  this.message = message;\n  if (error) this.inner = error;\n};\n\nJsonWebTokenError.prototype = Object.create(Error.prototype);\nJsonWebTokenError.prototype.constructor = JsonWebTokenError;\n\nmodule.exports = JsonWebTokenError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Kc29uV2ViVG9rZW5FcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FuZHJvaWR3ZWItZW50ZXJwcmlzZS8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL0pzb25XZWJUb2tlbkVycm9yLmpzPzNkZjciXSwic291cmNlc0NvbnRlbnQiOlsidmFyIEpzb25XZWJUb2tlbkVycm9yID0gZnVuY3Rpb24gKG1lc3NhZ2UsIGVycm9yKSB7XG4gIEVycm9yLmNhbGwodGhpcywgbWVzc2FnZSk7XG4gIGlmKEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKSB7XG4gICAgRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2UodGhpcywgdGhpcy5jb25zdHJ1Y3Rvcik7XG4gIH1cbiAgdGhpcy5uYW1lID0gJ0pzb25XZWJUb2tlbkVycm9yJztcbiAgdGhpcy5tZXNzYWdlID0gbWVzc2FnZTtcbiAgaWYgKGVycm9yKSB0aGlzLmlubmVyID0gZXJyb3I7XG59O1xuXG5Kc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUgPSBPYmplY3QuY3JlYXRlKEVycm9yLnByb3RvdHlwZSk7XG5Kc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUuY29uc3RydWN0b3IgPSBKc29uV2ViVG9rZW5FcnJvcjtcblxubW9kdWxlLmV4cG9ydHMgPSBKc29uV2ViVG9rZW5FcnJvcjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js":
/*!*********************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/NotBeforeError.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\n\nvar NotBeforeError = function (message, date) {\n  JsonWebTokenError.call(this, message);\n  this.name = 'NotBeforeError';\n  this.date = date;\n};\n\nNotBeforeError.prototype = Object.create(JsonWebTokenError.prototype);\n\nNotBeforeError.prototype.constructor = NotBeforeError;\n\nmodule.exports = NotBeforeError;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSx3QkFBd0IsbUJBQU8sQ0FBQyx1RkFBcUI7O0FBRXJEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmRyb2lkd2ViLWVudGVycHJpc2UvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcz9lZmViIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBKc29uV2ViVG9rZW5FcnJvciA9IHJlcXVpcmUoJy4vSnNvbldlYlRva2VuRXJyb3InKTtcblxudmFyIE5vdEJlZm9yZUVycm9yID0gZnVuY3Rpb24gKG1lc3NhZ2UsIGRhdGUpIHtcbiAgSnNvbldlYlRva2VuRXJyb3IuY2FsbCh0aGlzLCBtZXNzYWdlKTtcbiAgdGhpcy5uYW1lID0gJ05vdEJlZm9yZUVycm9yJztcbiAgdGhpcy5kYXRlID0gZGF0ZTtcbn07XG5cbk5vdEJlZm9yZUVycm9yLnByb3RvdHlwZSA9IE9iamVjdC5jcmVhdGUoSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlKTtcblxuTm90QmVmb3JlRXJyb3IucHJvdG90eXBlLmNvbnN0cnVjdG9yID0gTm90QmVmb3JlRXJyb3I7XG5cbm1vZHVsZS5leHBvcnRzID0gTm90QmVmb3JlRXJyb3I7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/TokenExpiredError.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\n\nvar TokenExpiredError = function (message, expiredAt) {\n  JsonWebTokenError.call(this, message);\n  this.name = 'TokenExpiredError';\n  this.expiredAt = expiredAt;\n};\n\nTokenExpiredError.prototype = Object.create(JsonWebTokenError.prototype);\n\nTokenExpiredError.prototype.constructor = TokenExpiredError;\n\nmodule.exports = TokenExpiredError;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSx3QkFBd0IsbUJBQU8sQ0FBQyx1RkFBcUI7O0FBRXJEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmRyb2lkd2ViLWVudGVycHJpc2UvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvci5qcz9kYjBhIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBKc29uV2ViVG9rZW5FcnJvciA9IHJlcXVpcmUoJy4vSnNvbldlYlRva2VuRXJyb3InKTtcblxudmFyIFRva2VuRXhwaXJlZEVycm9yID0gZnVuY3Rpb24gKG1lc3NhZ2UsIGV4cGlyZWRBdCkge1xuICBKc29uV2ViVG9rZW5FcnJvci5jYWxsKHRoaXMsIG1lc3NhZ2UpO1xuICB0aGlzLm5hbWUgPSAnVG9rZW5FeHBpcmVkRXJyb3InO1xuICB0aGlzLmV4cGlyZWRBdCA9IGV4cGlyZWRBdDtcbn07XG5cblRva2VuRXhwaXJlZEVycm9yLnByb3RvdHlwZSA9IE9iamVjdC5jcmVhdGUoSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlKTtcblxuVG9rZW5FeHBpcmVkRXJyb3IucHJvdG90eXBlLmNvbnN0cnVjdG9yID0gVG9rZW5FeHBpcmVkRXJyb3I7XG5cbm1vZHVsZS5leHBvcnRzID0gVG9rZW5FeHBpcmVkRXJyb3I7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js":
/*!************************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\n\nmodule.exports = semver.satisfies(process.version, '>=15.7.0');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9hc3ltbWV0cmljS2V5RGV0YWlsc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxlQUFlLG1CQUFPLENBQUMsb0RBQVE7O0FBRS9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5kcm9pZHdlYi1lbnRlcnByaXNlLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvYXN5bW1ldHJpY0tleURldGFpbHNTdXBwb3J0ZWQuanM/MGY1MyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzZW12ZXIgPSByZXF1aXJlKCdzZW12ZXInKTtcblxubW9kdWxlLmV4cG9ydHMgPSBzZW12ZXIuc2F0aXNmaWVzKHByb2Nlc3MudmVyc2lvbiwgJz49MTUuNy4wJyk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js":
/*!******************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/psSupported.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\n\nmodule.exports = semver.satisfies(process.version, '^6.12.0 || >=8.0.0');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9wc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxhQUFhLG1CQUFPLENBQUMsb0RBQVE7O0FBRTdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5kcm9pZHdlYi1lbnRlcnByaXNlLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvcHNTdXBwb3J0ZWQuanM/ZWViZSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgc2VtdmVyID0gcmVxdWlyZSgnc2VtdmVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gc2VtdmVyLnNhdGlzZmllcyhwcm9jZXNzLnZlcnNpb24sICdeNi4xMi4wIHx8ID49OC4wLjAnKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js":
/*!********************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\n\nmodule.exports = semver.satisfies(process.version, '>=16.9.0');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9yc2FQc3NLZXlEZXRhaWxzU3VwcG9ydGVkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsbUJBQU8sQ0FBQyxvREFBUTs7QUFFL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmRyb2lkd2ViLWVudGVycHJpc2UvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9yc2FQc3NLZXlEZXRhaWxzU3VwcG9ydGVkLmpzPzhhODgiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc2VtdmVyID0gcmVxdWlyZSgnc2VtdmVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gc2VtdmVyLnNhdGlzZmllcyhwcm9jZXNzLnZlcnNpb24sICc+PTE2LjkuMCcpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/timespan.js":
/*!***************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/timespan.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var ms = __webpack_require__(/*! ms */ \"(rsc)/./node_modules/ms/index.js\");\n\nmodule.exports = function (time, iat) {\n  var timestamp = iat || Math.floor(Date.now() / 1000);\n\n  if (typeof time === 'string') {\n    var milliseconds = ms(time);\n    if (typeof milliseconds === 'undefined') {\n      return;\n    }\n    return Math.floor(timestamp + milliseconds / 1000);\n  } else if (typeof time === 'number') {\n    return timestamp + time;\n  } else {\n    return;\n  }\n\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi90aW1lc3Bhbi5qcyIsIm1hcHBpbmdzIjoiQUFBQSxTQUFTLG1CQUFPLENBQUMsNENBQUk7O0FBRXJCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmRyb2lkd2ViLWVudGVycHJpc2UvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi90aW1lc3Bhbi5qcz9mMzg3Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBtcyA9IHJlcXVpcmUoJ21zJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKHRpbWUsIGlhdCkge1xuICB2YXIgdGltZXN0YW1wID0gaWF0IHx8IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApO1xuXG4gIGlmICh0eXBlb2YgdGltZSA9PT0gJ3N0cmluZycpIHtcbiAgICB2YXIgbWlsbGlzZWNvbmRzID0gbXModGltZSk7XG4gICAgaWYgKHR5cGVvZiBtaWxsaXNlY29uZHMgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHJldHVybiBNYXRoLmZsb29yKHRpbWVzdGFtcCArIG1pbGxpc2Vjb25kcyAvIDEwMDApO1xuICB9IGVsc2UgaWYgKHR5cGVvZiB0aW1lID09PSAnbnVtYmVyJykge1xuICAgIHJldHVybiB0aW1lc3RhbXAgKyB0aW1lO1xuICB9IGVsc2Uge1xuICAgIHJldHVybjtcbiAgfVxuXG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js":
/*!****************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const ASYMMETRIC_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./asymmetricKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\");\nconst RSA_PSS_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./rsaPssKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\");\n\nconst allowedAlgorithmsForKeys = {\n  'ec': ['ES256', 'ES384', 'ES512'],\n  'rsa': ['RS256', 'PS256', 'RS384', 'PS384', 'RS512', 'PS512'],\n  'rsa-pss': ['PS256', 'PS384', 'PS512']\n};\n\nconst allowedCurves = {\n  ES256: 'prime256v1',\n  ES384: 'secp384r1',\n  ES512: 'secp521r1',\n};\n\nmodule.exports = function(algorithm, key) {\n  if (!algorithm || !key) return;\n\n  const keyType = key.asymmetricKeyType;\n  if (!keyType) return;\n\n  const allowedAlgorithms = allowedAlgorithmsForKeys[keyType];\n\n  if (!allowedAlgorithms) {\n    throw new Error(`Unknown key type \"${keyType}\".`);\n  }\n\n  if (!allowedAlgorithms.includes(algorithm)) {\n    throw new Error(`\"alg\" parameter for \"${keyType}\" key type must be one of: ${allowedAlgorithms.join(', ')}.`)\n  }\n\n  /*\n   * Ignore the next block from test coverage because it gets executed\n   * conditionally depending on the Node version. Not ignoring it would\n   * prevent us from reaching the target % of coverage for versions of\n   * Node under 15.7.0.\n   */\n  /* istanbul ignore next */\n  if (ASYMMETRIC_KEY_DETAILS_SUPPORTED) {\n    switch (keyType) {\n    case 'ec':\n      const keyCurve = key.asymmetricKeyDetails.namedCurve;\n      const allowedCurve = allowedCurves[algorithm];\n\n      if (keyCurve !== allowedCurve) {\n        throw new Error(`\"alg\" parameter \"${algorithm}\" requires curve \"${allowedCurve}\".`);\n      }\n      break;\n\n    case 'rsa-pss':\n      if (RSA_PSS_KEY_DETAILS_SUPPORTED) {\n        const length = parseInt(algorithm.slice(-3), 10);\n        const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = key.asymmetricKeyDetails;\n\n        if (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm) {\n          throw new Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${algorithm}.`);\n        }\n\n        if (saltLength !== undefined && saltLength > length >> 3) {\n          throw new Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${algorithm}.`)\n        }\n      }\n      break;\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/sign.js":
/*!*******************************************!*\
  !*** ./node_modules/jsonwebtoken/sign.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst includes = __webpack_require__(/*! lodash.includes */ \"(rsc)/./node_modules/lodash.includes/index.js\");\nconst isBoolean = __webpack_require__(/*! lodash.isboolean */ \"(rsc)/./node_modules/lodash.isboolean/index.js\");\nconst isInteger = __webpack_require__(/*! lodash.isinteger */ \"(rsc)/./node_modules/lodash.isinteger/index.js\");\nconst isNumber = __webpack_require__(/*! lodash.isnumber */ \"(rsc)/./node_modules/lodash.isnumber/index.js\");\nconst isPlainObject = __webpack_require__(/*! lodash.isplainobject */ \"(rsc)/./node_modules/lodash.isplainobject/index.js\");\nconst isString = __webpack_require__(/*! lodash.isstring */ \"(rsc)/./node_modules/lodash.isstring/index.js\");\nconst once = __webpack_require__(/*! lodash.once */ \"(rsc)/./node_modules/lodash.once/index.js\");\nconst { KeyObject, createSecretKey, createPrivateKey } = __webpack_require__(/*! crypto */ \"crypto\")\n\nconst SUPPORTED_ALGS = ['RS256', 'RS384', 'RS512', 'ES256', 'ES384', 'ES512', 'HS256', 'HS384', 'HS512', 'none'];\nif (PS_SUPPORTED) {\n  SUPPORTED_ALGS.splice(3, 0, 'PS256', 'PS384', 'PS512');\n}\n\nconst sign_options_schema = {\n  expiresIn: { isValid: function(value) { return isInteger(value) || (isString(value) && value); }, message: '\"expiresIn\" should be a number of seconds or string representing a timespan' },\n  notBefore: { isValid: function(value) { return isInteger(value) || (isString(value) && value); }, message: '\"notBefore\" should be a number of seconds or string representing a timespan' },\n  audience: { isValid: function(value) { return isString(value) || Array.isArray(value); }, message: '\"audience\" must be a string or array' },\n  algorithm: { isValid: includes.bind(null, SUPPORTED_ALGS), message: '\"algorithm\" must be a valid string enum value' },\n  header: { isValid: isPlainObject, message: '\"header\" must be an object' },\n  encoding: { isValid: isString, message: '\"encoding\" must be a string' },\n  issuer: { isValid: isString, message: '\"issuer\" must be a string' },\n  subject: { isValid: isString, message: '\"subject\" must be a string' },\n  jwtid: { isValid: isString, message: '\"jwtid\" must be a string' },\n  noTimestamp: { isValid: isBoolean, message: '\"noTimestamp\" must be a boolean' },\n  keyid: { isValid: isString, message: '\"keyid\" must be a string' },\n  mutatePayload: { isValid: isBoolean, message: '\"mutatePayload\" must be a boolean' },\n  allowInsecureKeySizes: { isValid: isBoolean, message: '\"allowInsecureKeySizes\" must be a boolean'},\n  allowInvalidAsymmetricKeyTypes: { isValid: isBoolean, message: '\"allowInvalidAsymmetricKeyTypes\" must be a boolean'}\n};\n\nconst registered_claims_schema = {\n  iat: { isValid: isNumber, message: '\"iat\" should be a number of seconds' },\n  exp: { isValid: isNumber, message: '\"exp\" should be a number of seconds' },\n  nbf: { isValid: isNumber, message: '\"nbf\" should be a number of seconds' }\n};\n\nfunction validate(schema, allowUnknown, object, parameterName) {\n  if (!isPlainObject(object)) {\n    throw new Error('Expected \"' + parameterName + '\" to be a plain object.');\n  }\n  Object.keys(object)\n    .forEach(function(key) {\n      const validator = schema[key];\n      if (!validator) {\n        if (!allowUnknown) {\n          throw new Error('\"' + key + '\" is not allowed in \"' + parameterName + '\"');\n        }\n        return;\n      }\n      if (!validator.isValid(object[key])) {\n        throw new Error(validator.message);\n      }\n    });\n}\n\nfunction validateOptions(options) {\n  return validate(sign_options_schema, false, options, 'options');\n}\n\nfunction validatePayload(payload) {\n  return validate(registered_claims_schema, true, payload, 'payload');\n}\n\nconst options_to_payload = {\n  'audience': 'aud',\n  'issuer': 'iss',\n  'subject': 'sub',\n  'jwtid': 'jti'\n};\n\nconst options_for_objects = [\n  'expiresIn',\n  'notBefore',\n  'noTimestamp',\n  'audience',\n  'issuer',\n  'subject',\n  'jwtid',\n];\n\nmodule.exports = function (payload, secretOrPrivateKey, options, callback) {\n  if (typeof options === 'function') {\n    callback = options;\n    options = {};\n  } else {\n    options = options || {};\n  }\n\n  const isObjectPayload = typeof payload === 'object' &&\n                        !Buffer.isBuffer(payload);\n\n  const header = Object.assign({\n    alg: options.algorithm || 'HS256',\n    typ: isObjectPayload ? 'JWT' : undefined,\n    kid: options.keyid\n  }, options.header);\n\n  function failure(err) {\n    if (callback) {\n      return callback(err);\n    }\n    throw err;\n  }\n\n  if (!secretOrPrivateKey && options.algorithm !== 'none') {\n    return failure(new Error('secretOrPrivateKey must have a value'));\n  }\n\n  if (secretOrPrivateKey != null && !(secretOrPrivateKey instanceof KeyObject)) {\n    try {\n      secretOrPrivateKey = createPrivateKey(secretOrPrivateKey)\n    } catch (_) {\n      try {\n        secretOrPrivateKey = createSecretKey(typeof secretOrPrivateKey === 'string' ? Buffer.from(secretOrPrivateKey) : secretOrPrivateKey)\n      } catch (_) {\n        return failure(new Error('secretOrPrivateKey is not valid key material'));\n      }\n    }\n  }\n\n  if (header.alg.startsWith('HS') && secretOrPrivateKey.type !== 'secret') {\n    return failure(new Error((`secretOrPrivateKey must be a symmetric key when using ${header.alg}`)))\n  } else if (/^(?:RS|PS|ES)/.test(header.alg)) {\n    if (secretOrPrivateKey.type !== 'private') {\n      return failure(new Error((`secretOrPrivateKey must be an asymmetric key when using ${header.alg}`)))\n    }\n    if (!options.allowInsecureKeySizes &&\n      !header.alg.startsWith('ES') &&\n      secretOrPrivateKey.asymmetricKeyDetails !== undefined && //KeyObject.asymmetricKeyDetails is supported in Node 15+\n      secretOrPrivateKey.asymmetricKeyDetails.modulusLength < 2048) {\n      return failure(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n    }\n  }\n\n  if (typeof payload === 'undefined') {\n    return failure(new Error('payload is required'));\n  } else if (isObjectPayload) {\n    try {\n      validatePayload(payload);\n    }\n    catch (error) {\n      return failure(error);\n    }\n    if (!options.mutatePayload) {\n      payload = Object.assign({},payload);\n    }\n  } else {\n    const invalid_options = options_for_objects.filter(function (opt) {\n      return typeof options[opt] !== 'undefined';\n    });\n\n    if (invalid_options.length > 0) {\n      return failure(new Error('invalid ' + invalid_options.join(',') + ' option for ' + (typeof payload ) + ' payload'));\n    }\n  }\n\n  if (typeof payload.exp !== 'undefined' && typeof options.expiresIn !== 'undefined') {\n    return failure(new Error('Bad \"options.expiresIn\" option the payload already has an \"exp\" property.'));\n  }\n\n  if (typeof payload.nbf !== 'undefined' && typeof options.notBefore !== 'undefined') {\n    return failure(new Error('Bad \"options.notBefore\" option the payload already has an \"nbf\" property.'));\n  }\n\n  try {\n    validateOptions(options);\n  }\n  catch (error) {\n    return failure(error);\n  }\n\n  if (!options.allowInvalidAsymmetricKeyTypes) {\n    try {\n      validateAsymmetricKey(header.alg, secretOrPrivateKey);\n    } catch (error) {\n      return failure(error);\n    }\n  }\n\n  const timestamp = payload.iat || Math.floor(Date.now() / 1000);\n\n  if (options.noTimestamp) {\n    delete payload.iat;\n  } else if (isObjectPayload) {\n    payload.iat = timestamp;\n  }\n\n  if (typeof options.notBefore !== 'undefined') {\n    try {\n      payload.nbf = timespan(options.notBefore, timestamp);\n    }\n    catch (err) {\n      return failure(err);\n    }\n    if (typeof payload.nbf === 'undefined') {\n      return failure(new Error('\"notBefore\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n    }\n  }\n\n  if (typeof options.expiresIn !== 'undefined' && typeof payload === 'object') {\n    try {\n      payload.exp = timespan(options.expiresIn, timestamp);\n    }\n    catch (err) {\n      return failure(err);\n    }\n    if (typeof payload.exp === 'undefined') {\n      return failure(new Error('\"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n    }\n  }\n\n  Object.keys(options_to_payload).forEach(function (key) {\n    const claim = options_to_payload[key];\n    if (typeof options[key] !== 'undefined') {\n      if (typeof payload[claim] !== 'undefined') {\n        return failure(new Error('Bad \"options.' + key + '\" option. The payload already has an \"' + claim + '\" property.'));\n      }\n      payload[claim] = options[key];\n    }\n  });\n\n  const encoding = options.encoding || 'utf8';\n\n  if (typeof callback === 'function') {\n    callback = callback && once(callback);\n\n    jws.createSign({\n      header: header,\n      privateKey: secretOrPrivateKey,\n      payload: payload,\n      encoding: encoding\n    }).once('error', callback)\n      .once('done', function (signature) {\n        // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n        if(!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n          return callback(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`))\n        }\n        callback(null, signature);\n      });\n  } else {\n    let signature = jws.sign({header: header, payload: payload, secret: secretOrPrivateKey, encoding: encoding});\n    // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n    if(!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n      throw new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`)\n    }\n    return signature\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL3NpZ24uanMiLCJtYXBwaW5ncyI6IkFBQUEsaUJBQWlCLG1CQUFPLENBQUMseUVBQWdCO0FBQ3pDLHFCQUFxQixtQkFBTyxDQUFDLCtFQUFtQjtBQUNoRCw4QkFBOEIsbUJBQU8sQ0FBQyxtR0FBNkI7QUFDbkUsWUFBWSxtQkFBTyxDQUFDLDhDQUFLO0FBQ3pCLGlCQUFpQixtQkFBTyxDQUFDLHNFQUFpQjtBQUMxQyxrQkFBa0IsbUJBQU8sQ0FBQyx3RUFBa0I7QUFDNUMsa0JBQWtCLG1CQUFPLENBQUMsd0VBQWtCO0FBQzVDLGlCQUFpQixtQkFBTyxDQUFDLHNFQUFpQjtBQUMxQyxzQkFBc0IsbUJBQU8sQ0FBQyxnRkFBc0I7QUFDcEQsaUJBQWlCLG1CQUFPLENBQUMsc0VBQWlCO0FBQzFDLGFBQWEsbUJBQU8sQ0FBQyw4REFBYTtBQUNsQyxRQUFRLCtDQUErQyxFQUFFLG1CQUFPLENBQUMsc0JBQVE7O0FBRXpFO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSwyQkFBMkIsd0RBQXdELDBGQUEwRjtBQUM1TCxlQUFlLDJCQUEyQix3REFBd0QsMEZBQTBGO0FBQzVMLGNBQWMsMkJBQTJCLGlEQUFpRCxtREFBbUQ7QUFDN0ksZUFBZSx3R0FBd0c7QUFDdkgsWUFBWSwrREFBK0Q7QUFDM0UsY0FBYywyREFBMkQ7QUFDekUsWUFBWSx5REFBeUQ7QUFDckUsYUFBYSwwREFBMEQ7QUFDdkUsV0FBVyx3REFBd0Q7QUFDbkUsaUJBQWlCLGdFQUFnRTtBQUNqRixXQUFXLHdEQUF3RDtBQUNuRSxtQkFBbUIsa0VBQWtFO0FBQ3JGLDJCQUEyQix5RUFBeUU7QUFDcEcsb0NBQW9DO0FBQ3BDOztBQUVBO0FBQ0EsU0FBUyxtRUFBbUU7QUFDNUUsU0FBUyxtRUFBbUU7QUFDNUUsU0FBUztBQUNUOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSx1RkFBdUYsV0FBVztBQUNsRyxJQUFJO0FBQ0o7QUFDQSwyRkFBMkYsV0FBVztBQUN0RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkZBQTZGLFdBQVc7QUFDeEc7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDO0FBQ2hDO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0Esa0dBQWtHLFdBQVc7QUFDN0c7QUFDQTtBQUNBLE9BQU87QUFDUCxJQUFJO0FBQ0osOEJBQThCLGlGQUFpRjtBQUMvRztBQUNBO0FBQ0Esb0ZBQW9GLFdBQVc7QUFDL0Y7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmRyb2lkd2ViLWVudGVycHJpc2UvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL3NpZ24uanM/NDJkOCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB0aW1lc3BhbiA9IHJlcXVpcmUoJy4vbGliL3RpbWVzcGFuJyk7XG5jb25zdCBQU19TVVBQT1JURUQgPSByZXF1aXJlKCcuL2xpYi9wc1N1cHBvcnRlZCcpO1xuY29uc3QgdmFsaWRhdGVBc3ltbWV0cmljS2V5ID0gcmVxdWlyZSgnLi9saWIvdmFsaWRhdGVBc3ltbWV0cmljS2V5Jyk7XG5jb25zdCBqd3MgPSByZXF1aXJlKCdqd3MnKTtcbmNvbnN0IGluY2x1ZGVzID0gcmVxdWlyZSgnbG9kYXNoLmluY2x1ZGVzJyk7XG5jb25zdCBpc0Jvb2xlYW4gPSByZXF1aXJlKCdsb2Rhc2guaXNib29sZWFuJyk7XG5jb25zdCBpc0ludGVnZXIgPSByZXF1aXJlKCdsb2Rhc2guaXNpbnRlZ2VyJyk7XG5jb25zdCBpc051bWJlciA9IHJlcXVpcmUoJ2xvZGFzaC5pc251bWJlcicpO1xuY29uc3QgaXNQbGFpbk9iamVjdCA9IHJlcXVpcmUoJ2xvZGFzaC5pc3BsYWlub2JqZWN0Jyk7XG5jb25zdCBpc1N0cmluZyA9IHJlcXVpcmUoJ2xvZGFzaC5pc3N0cmluZycpO1xuY29uc3Qgb25jZSA9IHJlcXVpcmUoJ2xvZGFzaC5vbmNlJyk7XG5jb25zdCB7IEtleU9iamVjdCwgY3JlYXRlU2VjcmV0S2V5LCBjcmVhdGVQcml2YXRlS2V5IH0gPSByZXF1aXJlKCdjcnlwdG8nKVxuXG5jb25zdCBTVVBQT1JURURfQUxHUyA9IFsnUlMyNTYnLCAnUlMzODQnLCAnUlM1MTInLCAnRVMyNTYnLCAnRVMzODQnLCAnRVM1MTInLCAnSFMyNTYnLCAnSFMzODQnLCAnSFM1MTInLCAnbm9uZSddO1xuaWYgKFBTX1NVUFBPUlRFRCkge1xuICBTVVBQT1JURURfQUxHUy5zcGxpY2UoMywgMCwgJ1BTMjU2JywgJ1BTMzg0JywgJ1BTNTEyJyk7XG59XG5cbmNvbnN0IHNpZ25fb3B0aW9uc19zY2hlbWEgPSB7XG4gIGV4cGlyZXNJbjogeyBpc1ZhbGlkOiBmdW5jdGlvbih2YWx1ZSkgeyByZXR1cm4gaXNJbnRlZ2VyKHZhbHVlKSB8fCAoaXNTdHJpbmcodmFsdWUpICYmIHZhbHVlKTsgfSwgbWVzc2FnZTogJ1wiZXhwaXJlc0luXCIgc2hvdWxkIGJlIGEgbnVtYmVyIG9mIHNlY29uZHMgb3Igc3RyaW5nIHJlcHJlc2VudGluZyBhIHRpbWVzcGFuJyB9LFxuICBub3RCZWZvcmU6IHsgaXNWYWxpZDogZnVuY3Rpb24odmFsdWUpIHsgcmV0dXJuIGlzSW50ZWdlcih2YWx1ZSkgfHwgKGlzU3RyaW5nKHZhbHVlKSAmJiB2YWx1ZSk7IH0sIG1lc3NhZ2U6ICdcIm5vdEJlZm9yZVwiIHNob3VsZCBiZSBhIG51bWJlciBvZiBzZWNvbmRzIG9yIHN0cmluZyByZXByZXNlbnRpbmcgYSB0aW1lc3BhbicgfSxcbiAgYXVkaWVuY2U6IHsgaXNWYWxpZDogZnVuY3Rpb24odmFsdWUpIHsgcmV0dXJuIGlzU3RyaW5nKHZhbHVlKSB8fCBBcnJheS5pc0FycmF5KHZhbHVlKTsgfSwgbWVzc2FnZTogJ1wiYXVkaWVuY2VcIiBtdXN0IGJlIGEgc3RyaW5nIG9yIGFycmF5JyB9LFxuICBhbGdvcml0aG06IHsgaXNWYWxpZDogaW5jbHVkZXMuYmluZChudWxsLCBTVVBQT1JURURfQUxHUyksIG1lc3NhZ2U6ICdcImFsZ29yaXRobVwiIG11c3QgYmUgYSB2YWxpZCBzdHJpbmcgZW51bSB2YWx1ZScgfSxcbiAgaGVhZGVyOiB7IGlzVmFsaWQ6IGlzUGxhaW5PYmplY3QsIG1lc3NhZ2U6ICdcImhlYWRlclwiIG11c3QgYmUgYW4gb2JqZWN0JyB9LFxuICBlbmNvZGluZzogeyBpc1ZhbGlkOiBpc1N0cmluZywgbWVzc2FnZTogJ1wiZW5jb2RpbmdcIiBtdXN0IGJlIGEgc3RyaW5nJyB9LFxuICBpc3N1ZXI6IHsgaXNWYWxpZDogaXNTdHJpbmcsIG1lc3NhZ2U6ICdcImlzc3VlclwiIG11c3QgYmUgYSBzdHJpbmcnIH0sXG4gIHN1YmplY3Q6IHsgaXNWYWxpZDogaXNTdHJpbmcsIG1lc3NhZ2U6ICdcInN1YmplY3RcIiBtdXN0IGJlIGEgc3RyaW5nJyB9LFxuICBqd3RpZDogeyBpc1ZhbGlkOiBpc1N0cmluZywgbWVzc2FnZTogJ1wiand0aWRcIiBtdXN0IGJlIGEgc3RyaW5nJyB9LFxuICBub1RpbWVzdGFtcDogeyBpc1ZhbGlkOiBpc0Jvb2xlYW4sIG1lc3NhZ2U6ICdcIm5vVGltZXN0YW1wXCIgbXVzdCBiZSBhIGJvb2xlYW4nIH0sXG4gIGtleWlkOiB7IGlzVmFsaWQ6IGlzU3RyaW5nLCBtZXNzYWdlOiAnXCJrZXlpZFwiIG11c3QgYmUgYSBzdHJpbmcnIH0sXG4gIG11dGF0ZVBheWxvYWQ6IHsgaXNWYWxpZDogaXNCb29sZWFuLCBtZXNzYWdlOiAnXCJtdXRhdGVQYXlsb2FkXCIgbXVzdCBiZSBhIGJvb2xlYW4nIH0sXG4gIGFsbG93SW5zZWN1cmVLZXlTaXplczogeyBpc1ZhbGlkOiBpc0Jvb2xlYW4sIG1lc3NhZ2U6ICdcImFsbG93SW5zZWN1cmVLZXlTaXplc1wiIG11c3QgYmUgYSBib29sZWFuJ30sXG4gIGFsbG93SW52YWxpZEFzeW1tZXRyaWNLZXlUeXBlczogeyBpc1ZhbGlkOiBpc0Jvb2xlYW4sIG1lc3NhZ2U6ICdcImFsbG93SW52YWxpZEFzeW1tZXRyaWNLZXlUeXBlc1wiIG11c3QgYmUgYSBib29sZWFuJ31cbn07XG5cbmNvbnN0IHJlZ2lzdGVyZWRfY2xhaW1zX3NjaGVtYSA9IHtcbiAgaWF0OiB7IGlzVmFsaWQ6IGlzTnVtYmVyLCBtZXNzYWdlOiAnXCJpYXRcIiBzaG91bGQgYmUgYSBudW1iZXIgb2Ygc2Vjb25kcycgfSxcbiAgZXhwOiB7IGlzVmFsaWQ6IGlzTnVtYmVyLCBtZXNzYWdlOiAnXCJleHBcIiBzaG91bGQgYmUgYSBudW1iZXIgb2Ygc2Vjb25kcycgfSxcbiAgbmJmOiB7IGlzVmFsaWQ6IGlzTnVtYmVyLCBtZXNzYWdlOiAnXCJuYmZcIiBzaG91bGQgYmUgYSBudW1iZXIgb2Ygc2Vjb25kcycgfVxufTtcblxuZnVuY3Rpb24gdmFsaWRhdGUoc2NoZW1hLCBhbGxvd1Vua25vd24sIG9iamVjdCwgcGFyYW1ldGVyTmFtZSkge1xuICBpZiAoIWlzUGxhaW5PYmplY3Qob2JqZWN0KSkge1xuICAgIHRocm93IG5ldyBFcnJvcignRXhwZWN0ZWQgXCInICsgcGFyYW1ldGVyTmFtZSArICdcIiB0byBiZSBhIHBsYWluIG9iamVjdC4nKTtcbiAgfVxuICBPYmplY3Qua2V5cyhvYmplY3QpXG4gICAgLmZvckVhY2goZnVuY3Rpb24oa2V5KSB7XG4gICAgICBjb25zdCB2YWxpZGF0b3IgPSBzY2hlbWFba2V5XTtcbiAgICAgIGlmICghdmFsaWRhdG9yKSB7XG4gICAgICAgIGlmICghYWxsb3dVbmtub3duKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdcIicgKyBrZXkgKyAnXCIgaXMgbm90IGFsbG93ZWQgaW4gXCInICsgcGFyYW1ldGVyTmFtZSArICdcIicpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGlmICghdmFsaWRhdG9yLmlzVmFsaWQob2JqZWN0W2tleV0pKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcih2YWxpZGF0b3IubWVzc2FnZSk7XG4gICAgICB9XG4gICAgfSk7XG59XG5cbmZ1bmN0aW9uIHZhbGlkYXRlT3B0aW9ucyhvcHRpb25zKSB7XG4gIHJldHVybiB2YWxpZGF0ZShzaWduX29wdGlvbnNfc2NoZW1hLCBmYWxzZSwgb3B0aW9ucywgJ29wdGlvbnMnKTtcbn1cblxuZnVuY3Rpb24gdmFsaWRhdGVQYXlsb2FkKHBheWxvYWQpIHtcbiAgcmV0dXJuIHZhbGlkYXRlKHJlZ2lzdGVyZWRfY2xhaW1zX3NjaGVtYSwgdHJ1ZSwgcGF5bG9hZCwgJ3BheWxvYWQnKTtcbn1cblxuY29uc3Qgb3B0aW9uc190b19wYXlsb2FkID0ge1xuICAnYXVkaWVuY2UnOiAnYXVkJyxcbiAgJ2lzc3Vlcic6ICdpc3MnLFxuICAnc3ViamVjdCc6ICdzdWInLFxuICAnand0aWQnOiAnanRpJ1xufTtcblxuY29uc3Qgb3B0aW9uc19mb3Jfb2JqZWN0cyA9IFtcbiAgJ2V4cGlyZXNJbicsXG4gICdub3RCZWZvcmUnLFxuICAnbm9UaW1lc3RhbXAnLFxuICAnYXVkaWVuY2UnLFxuICAnaXNzdWVyJyxcbiAgJ3N1YmplY3QnLFxuICAnand0aWQnLFxuXTtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAocGF5bG9hZCwgc2VjcmV0T3JQcml2YXRlS2V5LCBvcHRpb25zLCBjYWxsYmFjaykge1xuICBpZiAodHlwZW9mIG9wdGlvbnMgPT09ICdmdW5jdGlvbicpIHtcbiAgICBjYWxsYmFjayA9IG9wdGlvbnM7XG4gICAgb3B0aW9ucyA9IHt9O1xuICB9IGVsc2Uge1xuICAgIG9wdGlvbnMgPSBvcHRpb25zIHx8IHt9O1xuICB9XG5cbiAgY29uc3QgaXNPYmplY3RQYXlsb2FkID0gdHlwZW9mIHBheWxvYWQgPT09ICdvYmplY3QnICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAhQnVmZmVyLmlzQnVmZmVyKHBheWxvYWQpO1xuXG4gIGNvbnN0IGhlYWRlciA9IE9iamVjdC5hc3NpZ24oe1xuICAgIGFsZzogb3B0aW9ucy5hbGdvcml0aG0gfHwgJ0hTMjU2JyxcbiAgICB0eXA6IGlzT2JqZWN0UGF5bG9hZCA/ICdKV1QnIDogdW5kZWZpbmVkLFxuICAgIGtpZDogb3B0aW9ucy5rZXlpZFxuICB9LCBvcHRpb25zLmhlYWRlcik7XG5cbiAgZnVuY3Rpb24gZmFpbHVyZShlcnIpIHtcbiAgICBpZiAoY2FsbGJhY2spIHtcbiAgICAgIHJldHVybiBjYWxsYmFjayhlcnIpO1xuICAgIH1cbiAgICB0aHJvdyBlcnI7XG4gIH1cblxuICBpZiAoIXNlY3JldE9yUHJpdmF0ZUtleSAmJiBvcHRpb25zLmFsZ29yaXRobSAhPT0gJ25vbmUnKSB7XG4gICAgcmV0dXJuIGZhaWx1cmUobmV3IEVycm9yKCdzZWNyZXRPclByaXZhdGVLZXkgbXVzdCBoYXZlIGEgdmFsdWUnKSk7XG4gIH1cblxuICBpZiAoc2VjcmV0T3JQcml2YXRlS2V5ICE9IG51bGwgJiYgIShzZWNyZXRPclByaXZhdGVLZXkgaW5zdGFuY2VvZiBLZXlPYmplY3QpKSB7XG4gICAgdHJ5IHtcbiAgICAgIHNlY3JldE9yUHJpdmF0ZUtleSA9IGNyZWF0ZVByaXZhdGVLZXkoc2VjcmV0T3JQcml2YXRlS2V5KVxuICAgIH0gY2F0Y2ggKF8pIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHNlY3JldE9yUHJpdmF0ZUtleSA9IGNyZWF0ZVNlY3JldEtleSh0eXBlb2Ygc2VjcmV0T3JQcml2YXRlS2V5ID09PSAnc3RyaW5nJyA/IEJ1ZmZlci5mcm9tKHNlY3JldE9yUHJpdmF0ZUtleSkgOiBzZWNyZXRPclByaXZhdGVLZXkpXG4gICAgICB9IGNhdGNoIChfKSB7XG4gICAgICAgIHJldHVybiBmYWlsdXJlKG5ldyBFcnJvcignc2VjcmV0T3JQcml2YXRlS2V5IGlzIG5vdCB2YWxpZCBrZXkgbWF0ZXJpYWwnKSk7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgaWYgKGhlYWRlci5hbGcuc3RhcnRzV2l0aCgnSFMnKSAmJiBzZWNyZXRPclByaXZhdGVLZXkudHlwZSAhPT0gJ3NlY3JldCcpIHtcbiAgICByZXR1cm4gZmFpbHVyZShuZXcgRXJyb3IoKGBzZWNyZXRPclByaXZhdGVLZXkgbXVzdCBiZSBhIHN5bW1ldHJpYyBrZXkgd2hlbiB1c2luZyAke2hlYWRlci5hbGd9YCkpKVxuICB9IGVsc2UgaWYgKC9eKD86UlN8UFN8RVMpLy50ZXN0KGhlYWRlci5hbGcpKSB7XG4gICAgaWYgKHNlY3JldE9yUHJpdmF0ZUtleS50eXBlICE9PSAncHJpdmF0ZScpIHtcbiAgICAgIHJldHVybiBmYWlsdXJlKG5ldyBFcnJvcigoYHNlY3JldE9yUHJpdmF0ZUtleSBtdXN0IGJlIGFuIGFzeW1tZXRyaWMga2V5IHdoZW4gdXNpbmcgJHtoZWFkZXIuYWxnfWApKSlcbiAgICB9XG4gICAgaWYgKCFvcHRpb25zLmFsbG93SW5zZWN1cmVLZXlTaXplcyAmJlxuICAgICAgIWhlYWRlci5hbGcuc3RhcnRzV2l0aCgnRVMnKSAmJlxuICAgICAgc2VjcmV0T3JQcml2YXRlS2V5LmFzeW1tZXRyaWNLZXlEZXRhaWxzICE9PSB1bmRlZmluZWQgJiYgLy9LZXlPYmplY3QuYXN5bW1ldHJpY0tleURldGFpbHMgaXMgc3VwcG9ydGVkIGluIE5vZGUgMTUrXG4gICAgICBzZWNyZXRPclByaXZhdGVLZXkuYXN5bW1ldHJpY0tleURldGFpbHMubW9kdWx1c0xlbmd0aCA8IDIwNDgpIHtcbiAgICAgIHJldHVybiBmYWlsdXJlKG5ldyBFcnJvcihgc2VjcmV0T3JQcml2YXRlS2V5IGhhcyBhIG1pbmltdW0ga2V5IHNpemUgb2YgMjA0OCBiaXRzIGZvciAke2hlYWRlci5hbGd9YCkpO1xuICAgIH1cbiAgfVxuXG4gIGlmICh0eXBlb2YgcGF5bG9hZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICByZXR1cm4gZmFpbHVyZShuZXcgRXJyb3IoJ3BheWxvYWQgaXMgcmVxdWlyZWQnKSk7XG4gIH0gZWxzZSBpZiAoaXNPYmplY3RQYXlsb2FkKSB7XG4gICAgdHJ5IHtcbiAgICAgIHZhbGlkYXRlUGF5bG9hZChwYXlsb2FkKTtcbiAgICB9XG4gICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4gZmFpbHVyZShlcnJvcik7XG4gICAgfVxuICAgIGlmICghb3B0aW9ucy5tdXRhdGVQYXlsb2FkKSB7XG4gICAgICBwYXlsb2FkID0gT2JqZWN0LmFzc2lnbih7fSxwYXlsb2FkKTtcbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgY29uc3QgaW52YWxpZF9vcHRpb25zID0gb3B0aW9uc19mb3Jfb2JqZWN0cy5maWx0ZXIoZnVuY3Rpb24gKG9wdCkge1xuICAgICAgcmV0dXJuIHR5cGVvZiBvcHRpb25zW29wdF0gIT09ICd1bmRlZmluZWQnO1xuICAgIH0pO1xuXG4gICAgaWYgKGludmFsaWRfb3B0aW9ucy5sZW5ndGggPiAwKSB7XG4gICAgICByZXR1cm4gZmFpbHVyZShuZXcgRXJyb3IoJ2ludmFsaWQgJyArIGludmFsaWRfb3B0aW9ucy5qb2luKCcsJykgKyAnIG9wdGlvbiBmb3IgJyArICh0eXBlb2YgcGF5bG9hZCApICsgJyBwYXlsb2FkJykpO1xuICAgIH1cbiAgfVxuXG4gIGlmICh0eXBlb2YgcGF5bG9hZC5leHAgIT09ICd1bmRlZmluZWQnICYmIHR5cGVvZiBvcHRpb25zLmV4cGlyZXNJbiAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICByZXR1cm4gZmFpbHVyZShuZXcgRXJyb3IoJ0JhZCBcIm9wdGlvbnMuZXhwaXJlc0luXCIgb3B0aW9uIHRoZSBwYXlsb2FkIGFscmVhZHkgaGFzIGFuIFwiZXhwXCIgcHJvcGVydHkuJykpO1xuICB9XG5cbiAgaWYgKHR5cGVvZiBwYXlsb2FkLm5iZiAhPT0gJ3VuZGVmaW5lZCcgJiYgdHlwZW9mIG9wdGlvbnMubm90QmVmb3JlICE9PSAndW5kZWZpbmVkJykge1xuICAgIHJldHVybiBmYWlsdXJlKG5ldyBFcnJvcignQmFkIFwib3B0aW9ucy5ub3RCZWZvcmVcIiBvcHRpb24gdGhlIHBheWxvYWQgYWxyZWFkeSBoYXMgYW4gXCJuYmZcIiBwcm9wZXJ0eS4nKSk7XG4gIH1cblxuICB0cnkge1xuICAgIHZhbGlkYXRlT3B0aW9ucyhvcHRpb25zKTtcbiAgfVxuICBjYXRjaCAoZXJyb3IpIHtcbiAgICByZXR1cm4gZmFpbHVyZShlcnJvcik7XG4gIH1cblxuICBpZiAoIW9wdGlvbnMuYWxsb3dJbnZhbGlkQXN5bW1ldHJpY0tleVR5cGVzKSB7XG4gICAgdHJ5IHtcbiAgICAgIHZhbGlkYXRlQXN5bW1ldHJpY0tleShoZWFkZXIuYWxnLCBzZWNyZXRPclByaXZhdGVLZXkpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4gZmFpbHVyZShlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgY29uc3QgdGltZXN0YW1wID0gcGF5bG9hZC5pYXQgfHwgTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMCk7XG5cbiAgaWYgKG9wdGlvbnMubm9UaW1lc3RhbXApIHtcbiAgICBkZWxldGUgcGF5bG9hZC5pYXQ7XG4gIH0gZWxzZSBpZiAoaXNPYmplY3RQYXlsb2FkKSB7XG4gICAgcGF5bG9hZC5pYXQgPSB0aW1lc3RhbXA7XG4gIH1cblxuICBpZiAodHlwZW9mIG9wdGlvbnMubm90QmVmb3JlICE9PSAndW5kZWZpbmVkJykge1xuICAgIHRyeSB7XG4gICAgICBwYXlsb2FkLm5iZiA9IHRpbWVzcGFuKG9wdGlvbnMubm90QmVmb3JlLCB0aW1lc3RhbXApO1xuICAgIH1cbiAgICBjYXRjaCAoZXJyKSB7XG4gICAgICByZXR1cm4gZmFpbHVyZShlcnIpO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIHBheWxvYWQubmJmID09PSAndW5kZWZpbmVkJykge1xuICAgICAgcmV0dXJuIGZhaWx1cmUobmV3IEVycm9yKCdcIm5vdEJlZm9yZVwiIHNob3VsZCBiZSBhIG51bWJlciBvZiBzZWNvbmRzIG9yIHN0cmluZyByZXByZXNlbnRpbmcgYSB0aW1lc3BhbiBlZzogXCIxZFwiLCBcIjIwaFwiLCA2MCcpKTtcbiAgICB9XG4gIH1cblxuICBpZiAodHlwZW9mIG9wdGlvbnMuZXhwaXJlc0luICE9PSAndW5kZWZpbmVkJyAmJiB0eXBlb2YgcGF5bG9hZCA9PT0gJ29iamVjdCcpIHtcbiAgICB0cnkge1xuICAgICAgcGF5bG9hZC5leHAgPSB0aW1lc3BhbihvcHRpb25zLmV4cGlyZXNJbiwgdGltZXN0YW1wKTtcbiAgICB9XG4gICAgY2F0Y2ggKGVycikge1xuICAgICAgcmV0dXJuIGZhaWx1cmUoZXJyKTtcbiAgICB9XG4gICAgaWYgKHR5cGVvZiBwYXlsb2FkLmV4cCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHJldHVybiBmYWlsdXJlKG5ldyBFcnJvcignXCJleHBpcmVzSW5cIiBzaG91bGQgYmUgYSBudW1iZXIgb2Ygc2Vjb25kcyBvciBzdHJpbmcgcmVwcmVzZW50aW5nIGEgdGltZXNwYW4gZWc6IFwiMWRcIiwgXCIyMGhcIiwgNjAnKSk7XG4gICAgfVxuICB9XG5cbiAgT2JqZWN0LmtleXMob3B0aW9uc190b19wYXlsb2FkKS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHtcbiAgICBjb25zdCBjbGFpbSA9IG9wdGlvbnNfdG9fcGF5bG9hZFtrZXldO1xuICAgIGlmICh0eXBlb2Ygb3B0aW9uc1trZXldICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgaWYgKHR5cGVvZiBwYXlsb2FkW2NsYWltXSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgcmV0dXJuIGZhaWx1cmUobmV3IEVycm9yKCdCYWQgXCJvcHRpb25zLicgKyBrZXkgKyAnXCIgb3B0aW9uLiBUaGUgcGF5bG9hZCBhbHJlYWR5IGhhcyBhbiBcIicgKyBjbGFpbSArICdcIiBwcm9wZXJ0eS4nKSk7XG4gICAgICB9XG4gICAgICBwYXlsb2FkW2NsYWltXSA9IG9wdGlvbnNba2V5XTtcbiAgICB9XG4gIH0pO1xuXG4gIGNvbnN0IGVuY29kaW5nID0gb3B0aW9ucy5lbmNvZGluZyB8fCAndXRmOCc7XG5cbiAgaWYgKHR5cGVvZiBjYWxsYmFjayA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIGNhbGxiYWNrID0gY2FsbGJhY2sgJiYgb25jZShjYWxsYmFjayk7XG5cbiAgICBqd3MuY3JlYXRlU2lnbih7XG4gICAgICBoZWFkZXI6IGhlYWRlcixcbiAgICAgIHByaXZhdGVLZXk6IHNlY3JldE9yUHJpdmF0ZUtleSxcbiAgICAgIHBheWxvYWQ6IHBheWxvYWQsXG4gICAgICBlbmNvZGluZzogZW5jb2RpbmdcbiAgICB9KS5vbmNlKCdlcnJvcicsIGNhbGxiYWNrKVxuICAgICAgLm9uY2UoJ2RvbmUnLCBmdW5jdGlvbiAoc2lnbmF0dXJlKSB7XG4gICAgICAgIC8vIFRPRE86IFJlbW92ZSBpbiBmYXZvciBvZiB0aGUgbW9kdWx1cyBsZW5ndGggY2hlY2sgYmVmb3JlIHNpZ25pbmcgb25jZSBub2RlIDE1KyBpcyB0aGUgbWluaW11bSBzdXBwb3J0ZWQgdmVyc2lvblxuICAgICAgICBpZighb3B0aW9ucy5hbGxvd0luc2VjdXJlS2V5U2l6ZXMgJiYgL14oPzpSU3xQUykvLnRlc3QoaGVhZGVyLmFsZykgJiYgc2lnbmF0dXJlLmxlbmd0aCA8IDI1Nikge1xuICAgICAgICAgIHJldHVybiBjYWxsYmFjayhuZXcgRXJyb3IoYHNlY3JldE9yUHJpdmF0ZUtleSBoYXMgYSBtaW5pbXVtIGtleSBzaXplIG9mIDIwNDggYml0cyBmb3IgJHtoZWFkZXIuYWxnfWApKVxuICAgICAgICB9XG4gICAgICAgIGNhbGxiYWNrKG51bGwsIHNpZ25hdHVyZSk7XG4gICAgICB9KTtcbiAgfSBlbHNlIHtcbiAgICBsZXQgc2lnbmF0dXJlID0gandzLnNpZ24oe2hlYWRlcjogaGVhZGVyLCBwYXlsb2FkOiBwYXlsb2FkLCBzZWNyZXQ6IHNlY3JldE9yUHJpdmF0ZUtleSwgZW5jb2Rpbmc6IGVuY29kaW5nfSk7XG4gICAgLy8gVE9ETzogUmVtb3ZlIGluIGZhdm9yIG9mIHRoZSBtb2R1bHVzIGxlbmd0aCBjaGVjayBiZWZvcmUgc2lnbmluZyBvbmNlIG5vZGUgMTUrIGlzIHRoZSBtaW5pbXVtIHN1cHBvcnRlZCB2ZXJzaW9uXG4gICAgaWYoIW9wdGlvbnMuYWxsb3dJbnNlY3VyZUtleVNpemVzICYmIC9eKD86UlN8UFMpLy50ZXN0KGhlYWRlci5hbGcpICYmIHNpZ25hdHVyZS5sZW5ndGggPCAyNTYpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgc2VjcmV0T3JQcml2YXRlS2V5IGhhcyBhIG1pbmltdW0ga2V5IHNpemUgb2YgMjA0OCBiaXRzIGZvciAke2hlYWRlci5hbGd9YClcbiAgICB9XG4gICAgcmV0dXJuIHNpZ25hdHVyZVxuICB9XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/sign.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/verify.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/verify.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const JsonWebTokenError = __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nconst NotBeforeError = __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\");\nconst TokenExpiredError = __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\");\nconst decode = __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\");\nconst timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst {KeyObject, createSecretKey, createPublicKey} = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst PUB_KEY_ALGS = ['RS256', 'RS384', 'RS512'];\nconst EC_KEY_ALGS = ['ES256', 'ES384', 'ES512'];\nconst RSA_KEY_ALGS = ['RS256', 'RS384', 'RS512'];\nconst HS_ALGS = ['HS256', 'HS384', 'HS512'];\n\nif (PS_SUPPORTED) {\n  PUB_KEY_ALGS.splice(PUB_KEY_ALGS.length, 0, 'PS256', 'PS384', 'PS512');\n  RSA_KEY_ALGS.splice(RSA_KEY_ALGS.length, 0, 'PS256', 'PS384', 'PS512');\n}\n\nmodule.exports = function (jwtString, secretOrPublicKey, options, callback) {\n  if ((typeof options === 'function') && !callback) {\n    callback = options;\n    options = {};\n  }\n\n  if (!options) {\n    options = {};\n  }\n\n  //clone this object since we are going to mutate it.\n  options = Object.assign({}, options);\n\n  let done;\n\n  if (callback) {\n    done = callback;\n  } else {\n    done = function(err, data) {\n      if (err) throw err;\n      return data;\n    };\n  }\n\n  if (options.clockTimestamp && typeof options.clockTimestamp !== 'number') {\n    return done(new JsonWebTokenError('clockTimestamp must be a number'));\n  }\n\n  if (options.nonce !== undefined && (typeof options.nonce !== 'string' || options.nonce.trim() === '')) {\n    return done(new JsonWebTokenError('nonce must be a non-empty string'));\n  }\n\n  if (options.allowInvalidAsymmetricKeyTypes !== undefined && typeof options.allowInvalidAsymmetricKeyTypes !== 'boolean') {\n    return done(new JsonWebTokenError('allowInvalidAsymmetricKeyTypes must be a boolean'));\n  }\n\n  const clockTimestamp = options.clockTimestamp || Math.floor(Date.now() / 1000);\n\n  if (!jwtString){\n    return done(new JsonWebTokenError('jwt must be provided'));\n  }\n\n  if (typeof jwtString !== 'string') {\n    return done(new JsonWebTokenError('jwt must be a string'));\n  }\n\n  const parts = jwtString.split('.');\n\n  if (parts.length !== 3){\n    return done(new JsonWebTokenError('jwt malformed'));\n  }\n\n  let decodedToken;\n\n  try {\n    decodedToken = decode(jwtString, { complete: true });\n  } catch(err) {\n    return done(err);\n  }\n\n  if (!decodedToken) {\n    return done(new JsonWebTokenError('invalid token'));\n  }\n\n  const header = decodedToken.header;\n  let getSecret;\n\n  if(typeof secretOrPublicKey === 'function') {\n    if(!callback) {\n      return done(new JsonWebTokenError('verify must be called asynchronous if secret or public key is provided as a callback'));\n    }\n\n    getSecret = secretOrPublicKey;\n  }\n  else {\n    getSecret = function(header, secretCallback) {\n      return secretCallback(null, secretOrPublicKey);\n    };\n  }\n\n  return getSecret(header, function(err, secretOrPublicKey) {\n    if(err) {\n      return done(new JsonWebTokenError('error in secret or public key callback: ' + err.message));\n    }\n\n    const hasSignature = parts[2].trim() !== '';\n\n    if (!hasSignature && secretOrPublicKey){\n      return done(new JsonWebTokenError('jwt signature is required'));\n    }\n\n    if (hasSignature && !secretOrPublicKey) {\n      return done(new JsonWebTokenError('secret or public key must be provided'));\n    }\n\n    if (!hasSignature && !options.algorithms) {\n      return done(new JsonWebTokenError('please specify \"none\" in \"algorithms\" to verify unsigned tokens'));\n    }\n\n    if (secretOrPublicKey != null && !(secretOrPublicKey instanceof KeyObject)) {\n      try {\n        secretOrPublicKey = createPublicKey(secretOrPublicKey);\n      } catch (_) {\n        try {\n          secretOrPublicKey = createSecretKey(typeof secretOrPublicKey === 'string' ? Buffer.from(secretOrPublicKey) : secretOrPublicKey);\n        } catch (_) {\n          return done(new JsonWebTokenError('secretOrPublicKey is not valid key material'))\n        }\n      }\n    }\n\n    if (!options.algorithms) {\n      if (secretOrPublicKey.type === 'secret') {\n        options.algorithms = HS_ALGS;\n      } else if (['rsa', 'rsa-pss'].includes(secretOrPublicKey.asymmetricKeyType)) {\n        options.algorithms = RSA_KEY_ALGS\n      } else if (secretOrPublicKey.asymmetricKeyType === 'ec') {\n        options.algorithms = EC_KEY_ALGS\n      } else {\n        options.algorithms = PUB_KEY_ALGS\n      }\n    }\n\n    if (options.algorithms.indexOf(decodedToken.header.alg) === -1) {\n      return done(new JsonWebTokenError('invalid algorithm'));\n    }\n\n    if (header.alg.startsWith('HS') && secretOrPublicKey.type !== 'secret') {\n      return done(new JsonWebTokenError((`secretOrPublicKey must be a symmetric key when using ${header.alg}`)))\n    } else if (/^(?:RS|PS|ES)/.test(header.alg) && secretOrPublicKey.type !== 'public') {\n      return done(new JsonWebTokenError((`secretOrPublicKey must be an asymmetric key when using ${header.alg}`)))\n    }\n\n    if (!options.allowInvalidAsymmetricKeyTypes) {\n      try {\n        validateAsymmetricKey(header.alg, secretOrPublicKey);\n      } catch (e) {\n        return done(e);\n      }\n    }\n\n    let valid;\n\n    try {\n      valid = jws.verify(jwtString, decodedToken.header.alg, secretOrPublicKey);\n    } catch (e) {\n      return done(e);\n    }\n\n    if (!valid) {\n      return done(new JsonWebTokenError('invalid signature'));\n    }\n\n    const payload = decodedToken.payload;\n\n    if (typeof payload.nbf !== 'undefined' && !options.ignoreNotBefore) {\n      if (typeof payload.nbf !== 'number') {\n        return done(new JsonWebTokenError('invalid nbf value'));\n      }\n      if (payload.nbf > clockTimestamp + (options.clockTolerance || 0)) {\n        return done(new NotBeforeError('jwt not active', new Date(payload.nbf * 1000)));\n      }\n    }\n\n    if (typeof payload.exp !== 'undefined' && !options.ignoreExpiration) {\n      if (typeof payload.exp !== 'number') {\n        return done(new JsonWebTokenError('invalid exp value'));\n      }\n      if (clockTimestamp >= payload.exp + (options.clockTolerance || 0)) {\n        return done(new TokenExpiredError('jwt expired', new Date(payload.exp * 1000)));\n      }\n    }\n\n    if (options.audience) {\n      const audiences = Array.isArray(options.audience) ? options.audience : [options.audience];\n      const target = Array.isArray(payload.aud) ? payload.aud : [payload.aud];\n\n      const match = target.some(function (targetAudience) {\n        return audiences.some(function (audience) {\n          return audience instanceof RegExp ? audience.test(targetAudience) : audience === targetAudience;\n        });\n      });\n\n      if (!match) {\n        return done(new JsonWebTokenError('jwt audience invalid. expected: ' + audiences.join(' or ')));\n      }\n    }\n\n    if (options.issuer) {\n      const invalid_issuer =\n              (typeof options.issuer === 'string' && payload.iss !== options.issuer) ||\n              (Array.isArray(options.issuer) && options.issuer.indexOf(payload.iss) === -1);\n\n      if (invalid_issuer) {\n        return done(new JsonWebTokenError('jwt issuer invalid. expected: ' + options.issuer));\n      }\n    }\n\n    if (options.subject) {\n      if (payload.sub !== options.subject) {\n        return done(new JsonWebTokenError('jwt subject invalid. expected: ' + options.subject));\n      }\n    }\n\n    if (options.jwtid) {\n      if (payload.jti !== options.jwtid) {\n        return done(new JsonWebTokenError('jwt jwtid invalid. expected: ' + options.jwtid));\n      }\n    }\n\n    if (options.nonce) {\n      if (payload.nonce !== options.nonce) {\n        return done(new JsonWebTokenError('jwt nonce invalid. expected: ' + options.nonce));\n      }\n    }\n\n    if (options.maxAge) {\n      if (typeof payload.iat !== 'number') {\n        return done(new JsonWebTokenError('iat required when maxAge is specified'));\n      }\n\n      const maxAgeTimestamp = timespan(options.maxAge, payload.iat);\n      if (typeof maxAgeTimestamp === 'undefined') {\n        return done(new JsonWebTokenError('\"maxAge\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n      }\n      if (clockTimestamp >= maxAgeTimestamp + (options.clockTolerance || 0)) {\n        return done(new TokenExpiredError('maxAge exceeded', new Date(maxAgeTimestamp * 1000)));\n      }\n    }\n\n    if (options.complete === true) {\n      const signature = decodedToken.signature;\n\n      return done(null, {\n        header: header,\n        payload: payload,\n        signature: signature\n      });\n    }\n\n    return done(null, payload);\n  });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/verify.js\n");

/***/ })

};
;