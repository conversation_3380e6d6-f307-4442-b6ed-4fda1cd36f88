"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel-map";
exports.ids = ["vendor-chunks/side-channel-map"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel-map/index.js":
/*!************************************************!*\
  !*** ./node_modules/side-channel-map/index.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar GetIntrinsic = __webpack_require__(/*! get-intrinsic */ \"(rsc)/./node_modules/get-intrinsic/index.js\");\nvar callBound = __webpack_require__(/*! call-bound */ \"(rsc)/./node_modules/call-bound/index.js\");\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\n\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\nvar $Map = GetIntrinsic('%Map%', true);\n\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => V} */\nvar $mapGet = callBound('Map.prototype.get', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K, value: V) => void} */\nvar $mapSet = callBound('Map.prototype.set', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */\nvar $mapHas = callBound('Map.prototype.has', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */\nvar $mapDelete = callBound('Map.prototype.delete', true);\n/** @type {<K, V>(thisArg: Map<K, V>) => number} */\nvar $mapSize = callBound('Map.prototype.size', true);\n\n/** @type {import('.')} */\nmodule.exports = !!$Map && /** @type {Exclude<import('.'), false>} */ function getSideChannelMap() {\n\t/** @typedef {ReturnType<typeof getSideChannelMap>} Channel */\n\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t/** @type {Map<K, V> | undefined} */ var $m;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\tif ($m) {\n\t\t\t\tvar result = $mapDelete($m, key);\n\t\t\t\tif ($mapSize($m) === 0) {\n\t\t\t\t\t$m = void undefined;\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tget: function (key) { // eslint-disable-line consistent-return\n\t\t\tif ($m) {\n\t\t\t\treturn $mapGet($m, key);\n\t\t\t}\n\t\t},\n\t\thas: function (key) {\n\t\t\tif ($m) {\n\t\t\t\treturn $mapHas($m, key);\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$m) {\n\t\t\t\t// @ts-expect-error TS can't handle narrowing a variable inside a closure\n\t\t\t\t$m = new $Map();\n\t\t\t}\n\t\t\t$mapSet($m, key, value);\n\t\t}\n\t};\n\n\t// @ts-expect-error TODO: figure out why TS is erroring here\n\treturn channel;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel-map/index.js\n");

/***/ })

};
;