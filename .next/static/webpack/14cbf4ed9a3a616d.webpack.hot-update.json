{"c": ["main", "app/layout", "webpack"], "r": ["app/(auth)/register/page", "app/pricing/page"], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp%2F(auth)%2Fregister%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/(auth)/register/page.tsx", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headphones.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fclaudiu%2FDesktop%2Fproiecte%2Fandroidweb%2Fsrc%2Fapp%2Fpricing%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/pricing/page.tsx"]}