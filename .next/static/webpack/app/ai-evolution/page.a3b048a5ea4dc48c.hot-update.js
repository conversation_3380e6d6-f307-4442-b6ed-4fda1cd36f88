"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-evolution/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zap.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Zap; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Zap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Zap\", [\n    [\n        \"polygon\",\n        {\n            points: \"13 2 3 14 12 14 11 22 21 10 12 10 13 2\",\n            key: \"45s27k\"\n        }\n    ]\n]);\n //# sourceMappingURL=zap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvemFwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sTUFBQUEsTUFBTUMsZ0VBQWdCQSxDQUFDLE9BQU87SUFDbEM7UUFBQztRQUFXO1lBQUVDLFFBQVE7WUFBMENDLEtBQUs7UUFBQTtLQUFVO0NBQ2hGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvaWNvbnMvemFwLnRzPzkxMjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBaYXBcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHOXNlV2R2YmlCd2IybHVkSE05SWpFeklESWdNeUF4TkNBeE1pQXhOQ0F4TVNBeU1pQXlNU0F4TUNBeE1pQXhNQ0F4TXlBeUlpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL3phcFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IFphcCA9IGNyZWF0ZUx1Y2lkZUljb24oJ1phcCcsIFtcbiAgWydwb2x5Z29uJywgeyBwb2ludHM6ICcxMyAyIDMgMTQgMTIgMTQgMTEgMjIgMjEgMTAgMTIgMTAgMTMgMicsIGtleTogJzQ1czI3aycgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgWmFwO1xuIl0sIm5hbWVzIjpbIlphcCIsImNyZWF0ZUx1Y2lkZUljb24iLCJwb2ludHMiLCJrZXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/ai-evolution/page.tsx":
/*!***************************************!*\
  !*** ./src/app/ai-evolution/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AIEvolutionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AIEvolutionPage() {\n    _s();\n    const [learningEvents, setLearningEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalAppsCreated: 0,\n        patternsLearned: 0,\n        successRate: 0,\n        averageGenerationTime: 0,\n        knowledgeBase: 0,\n        currentLearning: []\n    });\n    const [isLearning, setIsLearning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createdApps, setCreatedApps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [learningProgress, setLearningProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulare învățare continuă în timp real\n        const interval = setInterval(()=>{\n            simulateAILearning();\n        }, 3000);\n        loadAIMetrics();\n        loadCreatedApps();\n        return ()=>clearInterval(interval);\n    }, []);\n    const loadCreatedApps = async ()=>{\n        try {\n            const token = localStorage.getItem(\"accessToken\");\n            if (!token) return;\n            const response = await fetch(\"/api/projects\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setCreatedApps(data.projects || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading apps:\", error);\n        }\n    };\n    const simulateAILearning = ()=>{\n        const learningTypes = [\n            {\n                type: \"PATTERN_LEARNED\",\n                descriptions: [\n                    \"\\xcenvățat pattern nou pentru autentificare biometrică\",\n                    \"Optimizat structura pentru aplicații e-commerce\",\n                    \"Descoperit pattern eficient pentru UI responsive\",\n                    \"\\xcenvățat integrare avansată pentru API-uri REST\"\n                ]\n            },\n            {\n                type: \"OPTIMIZATION\",\n                descriptions: [\n                    \"Optimizat timpul de generare cu 15%\",\n                    \"\\xcembunătățit calitatea codului generat\",\n                    \"Redus numărul de erori cu 23%\",\n                    \"Optimizat arhitectura pentru performanță\"\n                ]\n            },\n            {\n                type: \"APP_CREATED\",\n                descriptions: [\n                    \"Aplicație Android creată cu succes\",\n                    \"Aplicație iOS generată și testată\",\n                    \"App cross-platform finalizată\",\n                    \"Aplicație enterprise completată\"\n                ]\n            }\n        ];\n        const randomType = learningTypes[Math.floor(Math.random() * learningTypes.length)];\n        const randomDesc = randomType.descriptions[Math.floor(Math.random() * randomType.descriptions.length)];\n        const newEvent = {\n            id: \"event_\".concat(Date.now()),\n            timestamp: new Date(),\n            type: randomType.type,\n            description: randomDesc,\n            impact: Math.floor(Math.random() * 100) + 1,\n            data: {\n                model: \"qwen2.5:0.5b\",\n                confidence: Math.random() * 0.3 + 0.7,\n                processingTime: Math.random() * 2000 + 500\n            }\n        };\n        setLearningEvents((prev)=>[\n                newEvent,\n                ...prev.slice(0, 19)\n            ]);\n        // Update metrics\n        setMetrics((prev)=>({\n                ...prev,\n                totalAppsCreated: prev.totalAppsCreated + (randomType.type === \"APP_CREATED\" ? 1 : 0),\n                patternsLearned: prev.patternsLearned + (randomType.type === \"PATTERN_LEARNED\" ? 1 : 0),\n                successRate: Math.min(99.9, prev.successRate + Math.random() * 0.1),\n                knowledgeBase: prev.knowledgeBase + Math.random() * 10,\n                currentLearning: [\n                    \"Analizează pattern-uri noi din aplicațiile create\",\n                    \"Optimizează algoritmi de generare cod\",\n                    \"\\xcenvață din feedback-ul utilizatorilor\",\n                    \"\\xcembunătățește arhitectura aplicațiilor\"\n                ]\n            }));\n        setIsLearning(true);\n        setTimeout(()=>setIsLearning(false), 1000);\n    };\n    const loadAIMetrics = async ()=>{\n        // Simulare încărcare metrici inițiale\n        setMetrics({\n            totalAppsCreated: 1247,\n            patternsLearned: 3891,\n            successRate: 94.7,\n            averageGenerationTime: 4.2,\n            knowledgeBase: 15847,\n            currentLearning: [\n                \"Analizează pattern-uri noi din aplicațiile create\",\n                \"Optimizează algoritmi de generare cod\",\n                \"\\xcenvață din feedback-ul utilizatorilor\",\n                \"\\xcembunătățește arhitectura aplicațiilor\"\n            ]\n        });\n    };\n    const getEventIcon = (type)=>{\n        switch(type){\n            case \"APP_CREATED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 34\n                }, this);\n            case \"PATTERN_LEARNED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 38\n                }, this);\n            case \"OPTIMIZATION\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 35\n                }, this);\n            case \"ERROR_CORRECTED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 38\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getEventColor = (type)=>{\n        switch(type){\n            case \"APP_CREATED\":\n                return \"bg-green-100 text-green-800\";\n            case \"PATTERN_LEARNED\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"OPTIMIZATION\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"ERROR_CORRECTED\":\n                return \"bg-orange-100 text-orange-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"AI Evolution Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this),\n                                isLearning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    className: \"bg-green-100 text-green-800 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-3 w-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Learning...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Monitorizează \\xeen timp real cum AI-ul \\xeenvață și evoluează din fiecare aplicație creată\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Apps Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: metrics.totalAppsCreated.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Patterns Learned\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: metrics.patternsLearned.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-8 w-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Success Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        metrics.successRate.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Knowledge Base\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: metrics.knowledgeBase.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-8 w-8 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Current Learning Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: metrics.currentLearning.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                Math.floor(Math.random() * 40 + 60),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                                                    value: Math.floor(Math.random() * 40 + 60),\n                                                    className: \"h-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Real-time Learning Events\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                        children: learningEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg\",\n                                                children: [\n                                                    getEventIcon(event.type),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                        className: getEventColor(event.type),\n                                                                        children: event.type.replace(\"_\", \" \")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: event.timestamp.toLocaleTimeString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4 mt-2 text-xs text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Impact: \",\n                                                                            event.impact,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Confidence: \",\n                                                                            (event.data.confidence * 100).toFixed(1),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Time: \",\n                                                                            event.data.processingTime.toFixed(0),\n                                                                            \"ms\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, event.id, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Smartphone, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Apps Created & Learning Data\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-4\",\n                                                    children: \"Recently Created Apps\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 max-h-64 overflow-y-auto\",\n                                                    children: createdApps.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: \"No apps created yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 23\n                                                    }, this) : createdApps.slice(0, 5).map((app)=>{\n                                                        var _app_features;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: app.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 336,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                app.platform,\n                                                                                \" • \",\n                                                                                ((_app_features = app.features) === null || _app_features === void 0 ? void 0 : _app_features.length) || 0,\n                                                                                \" features\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: [\n                                                                                \"Created \",\n                                                                                new Date(app.createdAt).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                            className: \"bg-green-100 text-green-800 text-xs\",\n                                                                            children: app.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 343,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                            children: [\n                                                                                \"Learning: +\",\n                                                                                Math.floor(Math.random() * 50 + 10),\n                                                                                \" patterns\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 346,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, app.id, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-4\",\n                                                    children: \"What AI Learned\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 max-h-64 overflow-y-auto\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-blue-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: \"Pattern Recognition\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 363,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: [\n                                                                        \"Learned new UI patterns from \",\n                                                                        createdApps.length,\n                                                                        \" apps. Improved component generation by 23%.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-purple-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-purple-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: \"Code Optimization\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 373,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"Optimized code structure based on user feedback. Reduced generation time by 15%.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-green-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 382,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: \"Feature Integration\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"Improved feature combination logic. Better integration between authentication and navigation.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-orange-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-orange-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: \"Quality Enhancement\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"Enhanced code quality metrics. Improved error handling and performance optimization.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\n_s(AIEvolutionPage, \"U7OpAAVE4VH5XuPpKFgj6PrSMqk=\");\n_c = AIEvolutionPage;\nvar _c;\n$RefreshReg$(_c, \"AIEvolutionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-evolution/page.tsx\n"));

/***/ })

});