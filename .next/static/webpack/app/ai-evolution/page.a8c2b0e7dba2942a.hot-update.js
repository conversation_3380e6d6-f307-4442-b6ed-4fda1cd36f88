"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-evolution/page",{

/***/ "(app-pages-browser)/./src/app/ai-evolution/page.tsx":
/*!***************************************!*\
  !*** ./src/app/ai-evolution/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AIEvolutionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AIEvolutionPage() {\n    _s();\n    const [learningEvents, setLearningEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalAppsCreated: 0,\n        patternsLearned: 0,\n        successRate: 0,\n        averageGenerationTime: 0,\n        knowledgeBase: 0,\n        currentLearning: []\n    });\n    const [isLearning, setIsLearning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createdApps, setCreatedApps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [learningProgress, setLearningProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulare învățare continuă în timp real\n        const interval = setInterval(()=>{\n            simulateAILearning();\n        }, 3000);\n        loadAIMetrics();\n        loadCreatedApps();\n        return ()=>clearInterval(interval);\n    }, []);\n    const loadCreatedApps = async ()=>{\n        try {\n            const token = localStorage.getItem(\"accessToken\");\n            if (!token) return;\n            const response = await fetch(\"/api/projects\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setCreatedApps(data.projects || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading apps:\", error);\n        }\n    };\n    const simulateAILearning = ()=>{\n        const learningTypes = [\n            {\n                type: \"PATTERN_LEARNED\",\n                descriptions: [\n                    \"\\xcenvățat pattern nou pentru autentificare biometrică\",\n                    \"Optimizat structura pentru aplicații e-commerce\",\n                    \"Descoperit pattern eficient pentru UI responsive\",\n                    \"\\xcenvățat integrare avansată pentru API-uri REST\"\n                ]\n            },\n            {\n                type: \"OPTIMIZATION\",\n                descriptions: [\n                    \"Optimizat timpul de generare cu 15%\",\n                    \"\\xcembunătățit calitatea codului generat\",\n                    \"Redus numărul de erori cu 23%\",\n                    \"Optimizat arhitectura pentru performanță\"\n                ]\n            },\n            {\n                type: \"APP_CREATED\",\n                descriptions: [\n                    \"Aplicație Android creată cu succes\",\n                    \"Aplicație iOS generată și testată\",\n                    \"App cross-platform finalizată\",\n                    \"Aplicație enterprise completată\"\n                ]\n            }\n        ];\n        const randomType = learningTypes[Math.floor(Math.random() * learningTypes.length)];\n        const randomDesc = randomType.descriptions[Math.floor(Math.random() * randomType.descriptions.length)];\n        const newEvent = {\n            id: \"event_\".concat(Date.now()),\n            timestamp: new Date(),\n            type: randomType.type,\n            description: randomDesc,\n            impact: Math.floor(Math.random() * 100) + 1,\n            data: {\n                model: \"qwen2.5:0.5b\",\n                confidence: Math.random() * 0.3 + 0.7,\n                processingTime: Math.random() * 2000 + 500\n            }\n        };\n        setLearningEvents((prev)=>[\n                newEvent,\n                ...prev.slice(0, 19)\n            ]);\n        // Update metrics\n        setMetrics((prev)=>({\n                ...prev,\n                totalAppsCreated: prev.totalAppsCreated + (randomType.type === \"APP_CREATED\" ? 1 : 0),\n                patternsLearned: prev.patternsLearned + (randomType.type === \"PATTERN_LEARNED\" ? 1 : 0),\n                successRate: Math.min(99.9, prev.successRate + Math.random() * 0.1),\n                knowledgeBase: prev.knowledgeBase + Math.random() * 10,\n                currentLearning: [\n                    \"Analizează pattern-uri noi din aplicațiile create\",\n                    \"Optimizează algoritmi de generare cod\",\n                    \"\\xcenvață din feedback-ul utilizatorilor\",\n                    \"\\xcembunătățește arhitectura aplicațiilor\"\n                ]\n            }));\n        setIsLearning(true);\n        setTimeout(()=>setIsLearning(false), 1000);\n    };\n    const loadAIMetrics = async ()=>{\n        // Simulare încărcare metrici inițiale\n        setMetrics({\n            totalAppsCreated: 1247,\n            patternsLearned: 3891,\n            successRate: 94.7,\n            averageGenerationTime: 4.2,\n            knowledgeBase: 15847,\n            currentLearning: [\n                \"Analizează pattern-uri noi din aplicațiile create\",\n                \"Optimizează algoritmi de generare cod\",\n                \"\\xcenvață din feedback-ul utilizatorilor\",\n                \"\\xcembunătățește arhitectura aplicațiilor\"\n            ]\n        });\n    };\n    const getEventIcon = (type)=>{\n        switch(type){\n            case \"APP_CREATED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 34\n                }, this);\n            case \"PATTERN_LEARNED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 38\n                }, this);\n            case \"OPTIMIZATION\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 35\n                }, this);\n            case \"ERROR_CORRECTED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 38\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getEventColor = (type)=>{\n        switch(type){\n            case \"APP_CREATED\":\n                return \"bg-green-100 text-green-800\";\n            case \"PATTERN_LEARNED\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"OPTIMIZATION\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"ERROR_CORRECTED\":\n                return \"bg-orange-100 text-orange-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"AI Evolution Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this),\n                                isLearning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    className: \"bg-green-100 text-green-800 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-3 w-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Learning...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Monitorizează \\xeen timp real cum AI-ul \\xeenvață și evoluează din fiecare aplicație creată\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Apps Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: metrics.totalAppsCreated.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Patterns Learned\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: metrics.patternsLearned.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-8 w-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Success Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        metrics.successRate.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Knowledge Base\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: metrics.knowledgeBase.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-8 w-8 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Current Learning Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: metrics.currentLearning.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                Math.floor(Math.random() * 40 + 60),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                                                    value: Math.floor(Math.random() * 40 + 60),\n                                                    className: \"h-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Real-time Learning Events\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                        children: learningEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg\",\n                                                children: [\n                                                    getEventIcon(event.type),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                        className: getEventColor(event.type),\n                                                                        children: event.type.replace(\"_\", \" \")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: event.timestamp.toLocaleTimeString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4 mt-2 text-xs text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Impact: \",\n                                                                            event.impact,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Confidence: \",\n                                                                            (event.data.confidence * 100).toFixed(1),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Time: \",\n                                                                            event.data.processingTime.toFixed(0),\n                                                                            \"ms\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, event.id, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\n_s(AIEvolutionPage, \"U7OpAAVE4VH5XuPpKFgj6PrSMqk=\");\n_c = AIEvolutionPage;\nvar _c;\n$RefreshReg$(_c, \"AIEvolutionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-evolution/page.tsx\n"));

/***/ })

});