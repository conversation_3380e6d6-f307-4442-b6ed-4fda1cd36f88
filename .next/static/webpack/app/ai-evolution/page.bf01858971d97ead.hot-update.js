"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-evolution/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/smartphone.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Smartphone; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Smartphone = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Smartphone\", [\n    [\n        \"rect\",\n        {\n            width: \"14\",\n            height: \"20\",\n            x: \"5\",\n            y: \"2\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"1yt0o3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 18h.01\",\n            key: \"mhygvu\"\n        }\n    ]\n]);\n //# sourceMappingURL=smartphone.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc21hcnRwaG9uZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFNLE1BQUFBLGFBQWFDLGdFQUFnQkEsQ0FBQyxjQUFjO0lBQ2hEO1FBQUM7UUFBUTtZQUFFQyxPQUFPO1lBQU1DLFFBQVE7WUFBTUMsR0FBRztZQUFLQyxHQUFHO1lBQUtDLElBQUk7WUFBS0MsSUFBSTtZQUFLQyxLQUFLO1FBQUE7S0FBVTtJQUN2RjtRQUFDO1FBQVE7WUFBRUMsR0FBRztZQUFjRCxLQUFLO1FBQUE7S0FBVTtDQUM1QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL2ljb25zL3NtYXJ0cGhvbmUudHM/M2JjZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFNtYXJ0cGhvbmVcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNtVmpkQ0IzYVdSMGFEMGlNVFFpSUdobGFXZG9kRDBpTWpBaUlIZzlJalVpSUhrOUlqSWlJSEo0UFNJeUlpQnllVDBpTWlJZ0x6NEtJQ0E4Y0dGMGFDQmtQU0pOTVRJZ01UaG9MakF4SWlBdlBnbzhMM04yWno0SykgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvc21hcnRwaG9uZVxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IFNtYXJ0cGhvbmUgPSBjcmVhdGVMdWNpZGVJY29uKCdTbWFydHBob25lJywgW1xuICBbJ3JlY3QnLCB7IHdpZHRoOiAnMTQnLCBoZWlnaHQ6ICcyMCcsIHg6ICc1JywgeTogJzInLCByeDogJzInLCByeTogJzInLCBrZXk6ICcxeXQwbzMnIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdNMTIgMThoLjAxJywga2V5OiAnbWh5Z3Z1JyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBTbWFydHBob25lO1xuIl0sIm5hbWVzIjpbIlNtYXJ0cGhvbmUiLCJjcmVhdGVMdWNpZGVJY29uIiwid2lkdGgiLCJoZWlnaHQiLCJ4IiwieSIsInJ4IiwicnkiLCJrZXkiLCJkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/ai-evolution/page.tsx":
/*!***************************************!*\
  !*** ./src/app/ai-evolution/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AIEvolutionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Smartphone,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Smartphone,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Smartphone,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Smartphone,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Smartphone,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Smartphone,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Smartphone,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Smartphone,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Smartphone,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Smartphone,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AIEvolutionPage() {\n    _s();\n    const [learningEvents, setLearningEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalAppsCreated: 0,\n        patternsLearned: 0,\n        successRate: 0,\n        averageGenerationTime: 0,\n        knowledgeBase: 0,\n        currentLearning: []\n    });\n    const [isLearning, setIsLearning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createdApps, setCreatedApps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [learningProgress, setLearningProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulare învățare continuă în timp real\n        const interval = setInterval(()=>{\n            simulateAILearning();\n        }, 3000);\n        loadAIMetrics();\n        loadCreatedApps();\n        return ()=>clearInterval(interval);\n    }, []);\n    const loadCreatedApps = async ()=>{\n        try {\n            const token = localStorage.getItem(\"accessToken\");\n            if (!token) return;\n            const response = await fetch(\"/api/projects\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setCreatedApps(data.projects || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading apps:\", error);\n        }\n    };\n    const simulateAILearning = ()=>{\n        const learningTypes = [\n            {\n                type: \"PATTERN_LEARNED\",\n                descriptions: [\n                    \"\\xcenvățat pattern nou pentru autentificare biometrică\",\n                    \"Optimizat structura pentru aplicații e-commerce\",\n                    \"Descoperit pattern eficient pentru UI responsive\",\n                    \"\\xcenvățat integrare avansată pentru API-uri REST\"\n                ]\n            },\n            {\n                type: \"OPTIMIZATION\",\n                descriptions: [\n                    \"Optimizat timpul de generare cu 15%\",\n                    \"\\xcembunătățit calitatea codului generat\",\n                    \"Redus numărul de erori cu 23%\",\n                    \"Optimizat arhitectura pentru performanță\"\n                ]\n            },\n            {\n                type: \"APP_CREATED\",\n                descriptions: [\n                    \"Aplicație Android creată cu succes\",\n                    \"Aplicație iOS generată și testată\",\n                    \"App cross-platform finalizată\",\n                    \"Aplicație enterprise completată\"\n                ]\n            }\n        ];\n        const randomType = learningTypes[Math.floor(Math.random() * learningTypes.length)];\n        const randomDesc = randomType.descriptions[Math.floor(Math.random() * randomType.descriptions.length)];\n        const newEvent = {\n            id: \"event_\".concat(Date.now()),\n            timestamp: new Date(),\n            type: randomType.type,\n            description: randomDesc,\n            impact: Math.floor(Math.random() * 100) + 1,\n            data: {\n                model: \"qwen2.5:0.5b\",\n                confidence: Math.random() * 0.3 + 0.7,\n                processingTime: Math.random() * 2000 + 500\n            }\n        };\n        setLearningEvents((prev)=>[\n                newEvent,\n                ...prev.slice(0, 19)\n            ]);\n        // Update metrics\n        setMetrics((prev)=>({\n                ...prev,\n                totalAppsCreated: prev.totalAppsCreated + (randomType.type === \"APP_CREATED\" ? 1 : 0),\n                patternsLearned: prev.patternsLearned + (randomType.type === \"PATTERN_LEARNED\" ? 1 : 0),\n                successRate: Math.min(99.9, prev.successRate + Math.random() * 0.1),\n                knowledgeBase: prev.knowledgeBase + Math.random() * 10,\n                currentLearning: [\n                    \"Analizează pattern-uri noi din aplicațiile create\",\n                    \"Optimizează algoritmi de generare cod\",\n                    \"\\xcenvață din feedback-ul utilizatorilor\",\n                    \"\\xcembunătățește arhitectura aplicațiilor\"\n                ]\n            }));\n        setIsLearning(true);\n        setTimeout(()=>setIsLearning(false), 1000);\n    };\n    const loadAIMetrics = async ()=>{\n        // Simulare încărcare metrici inițiale\n        setMetrics({\n            totalAppsCreated: 1247,\n            patternsLearned: 3891,\n            successRate: 94.7,\n            averageGenerationTime: 4.2,\n            knowledgeBase: 15847,\n            currentLearning: [\n                \"Analizează pattern-uri noi din aplicațiile create\",\n                \"Optimizează algoritmi de generare cod\",\n                \"\\xcenvață din feedback-ul utilizatorilor\",\n                \"\\xcembunătățește arhitectura aplicațiilor\"\n            ]\n        });\n    };\n    const getEventIcon = (type)=>{\n        switch(type){\n            case \"APP_CREATED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 34\n                }, this);\n            case \"PATTERN_LEARNED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 38\n                }, this);\n            case \"OPTIMIZATION\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 35\n                }, this);\n            case \"ERROR_CORRECTED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 38\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getEventColor = (type)=>{\n        switch(type){\n            case \"APP_CREATED\":\n                return \"bg-green-100 text-green-800\";\n            case \"PATTERN_LEARNED\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"OPTIMIZATION\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"ERROR_CORRECTED\":\n                return \"bg-orange-100 text-orange-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"AI Evolution Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                isLearning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    className: \"bg-green-100 text-green-800 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-3 w-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Learning...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Monitorizează \\xeen timp real cum AI-ul \\xeenvață și evoluează din fiecare aplicație creată\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Apps Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: metrics.totalAppsCreated.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Patterns Learned\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: metrics.patternsLearned.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-8 w-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Success Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        metrics.successRate.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Knowledge Base\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: metrics.knowledgeBase.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-8 w-8 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Current Learning Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: metrics.currentLearning.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                Math.floor(Math.random() * 40 + 60),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                                                    value: Math.floor(Math.random() * 40 + 60),\n                                                    className: \"h-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Real-time Learning Events\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                        children: learningEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg\",\n                                                children: [\n                                                    getEventIcon(event.type),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                        className: getEventColor(event.type),\n                                                                        children: event.type.replace(\"_\", \" \")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 295,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: event.timestamp.toLocaleTimeString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4 mt-2 text-xs text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Impact: \",\n                                                                            event.impact,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Confidence: \",\n                                                                            (event.data.confidence * 100).toFixed(1),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Time: \",\n                                                                            event.data.processingTime.toFixed(0),\n                                                                            \"ms\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, event.id, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Apps Created & Learning Data\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-4\",\n                                                    children: \"Recently Created Apps\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 max-h-64 overflow-y-auto\",\n                                                    children: createdApps.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: \"No apps created yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 23\n                                                    }, this) : createdApps.slice(0, 5).map((app)=>{\n                                                        var _app_features;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: app.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                app.platform,\n                                                                                \" • \",\n                                                                                ((_app_features = app.features) === null || _app_features === void 0 ? void 0 : _app_features.length) || 0,\n                                                                                \" features\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: [\n                                                                                \"Created \",\n                                                                                new Date(app.createdAt).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                            className: \"bg-green-100 text-green-800 text-xs\",\n                                                                            children: app.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                            children: [\n                                                                                \"Learning: +\",\n                                                                                Math.floor(Math.random() * 50 + 10),\n                                                                                \" patterns\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, app.id, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-4\",\n                                                    children: \"What AI Learned\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 max-h-64 overflow-y-auto\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-blue-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 363,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: \"Pattern Recognition\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 364,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: [\n                                                                        \"Learned new UI patterns from \",\n                                                                        createdApps.length,\n                                                                        \" apps. Improved component generation by 23%.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-purple-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-purple-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 373,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: \"Code Optimization\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 374,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"Optimized code structure based on user feedback. Reduced generation time by 15%.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-green-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: \"Feature Integration\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"Improved feature combination logic. Better integration between authentication and navigation.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-orange-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Smartphone_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-orange-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: \"Quality Enhancement\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"Enhanced code quality metrics. Improved error handling and performance optimization.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, this);\n}\n_s(AIEvolutionPage, \"U7OpAAVE4VH5XuPpKFgj6PrSMqk=\");\n_c = AIEvolutionPage;\nvar _c;\n$RefreshReg$(_c, \"AIEvolutionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-evolution/page.tsx\n"));

/***/ })

});