"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-evolution/page",{

/***/ "(app-pages-browser)/./src/app/ai-evolution/page.tsx":
/*!***************************************!*\
  !*** ./src/app/ai-evolution/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AIEvolutionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,CheckCircle,Code,Cpu,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AIEvolutionPage() {\n    _s();\n    const [learningEvents, setLearningEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalAppsCreated: 0,\n        patternsLearned: 0,\n        successRate: 0,\n        averageGenerationTime: 0,\n        knowledgeBase: 0,\n        currentLearning: []\n    });\n    const [isLearning, setIsLearning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createdApps, setCreatedApps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [learningProgress, setLearningProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulare învățare continuă în timp real\n        const interval = setInterval(()=>{\n            simulateAILearning();\n        }, 3000);\n        loadAIMetrics();\n        return ()=>clearInterval(interval);\n    }, []);\n    const simulateAILearning = ()=>{\n        const learningTypes = [\n            {\n                type: \"PATTERN_LEARNED\",\n                descriptions: [\n                    \"\\xcenvățat pattern nou pentru autentificare biometrică\",\n                    \"Optimizat structura pentru aplicații e-commerce\",\n                    \"Descoperit pattern eficient pentru UI responsive\",\n                    \"\\xcenvățat integrare avansată pentru API-uri REST\"\n                ]\n            },\n            {\n                type: \"OPTIMIZATION\",\n                descriptions: [\n                    \"Optimizat timpul de generare cu 15%\",\n                    \"\\xcembunătățit calitatea codului generat\",\n                    \"Redus numărul de erori cu 23%\",\n                    \"Optimizat arhitectura pentru performanță\"\n                ]\n            },\n            {\n                type: \"APP_CREATED\",\n                descriptions: [\n                    \"Aplicație Android creată cu succes\",\n                    \"Aplicație iOS generată și testată\",\n                    \"App cross-platform finalizată\",\n                    \"Aplicație enterprise completată\"\n                ]\n            }\n        ];\n        const randomType = learningTypes[Math.floor(Math.random() * learningTypes.length)];\n        const randomDesc = randomType.descriptions[Math.floor(Math.random() * randomType.descriptions.length)];\n        const newEvent = {\n            id: \"event_\".concat(Date.now()),\n            timestamp: new Date(),\n            type: randomType.type,\n            description: randomDesc,\n            impact: Math.floor(Math.random() * 100) + 1,\n            data: {\n                model: \"qwen2.5:0.5b\",\n                confidence: Math.random() * 0.3 + 0.7,\n                processingTime: Math.random() * 2000 + 500\n            }\n        };\n        setLearningEvents((prev)=>[\n                newEvent,\n                ...prev.slice(0, 19)\n            ]);\n        // Update metrics\n        setMetrics((prev)=>({\n                ...prev,\n                totalAppsCreated: prev.totalAppsCreated + (randomType.type === \"APP_CREATED\" ? 1 : 0),\n                patternsLearned: prev.patternsLearned + (randomType.type === \"PATTERN_LEARNED\" ? 1 : 0),\n                successRate: Math.min(99.9, prev.successRate + Math.random() * 0.1),\n                knowledgeBase: prev.knowledgeBase + Math.random() * 10,\n                currentLearning: [\n                    \"Analizează pattern-uri noi din aplicațiile create\",\n                    \"Optimizează algoritmi de generare cod\",\n                    \"\\xcenvață din feedback-ul utilizatorilor\",\n                    \"\\xcembunătățește arhitectura aplicațiilor\"\n                ]\n            }));\n        setIsLearning(true);\n        setTimeout(()=>setIsLearning(false), 1000);\n    };\n    const loadAIMetrics = async ()=>{\n        // Simulare încărcare metrici inițiale\n        setMetrics({\n            totalAppsCreated: 1247,\n            patternsLearned: 3891,\n            successRate: 94.7,\n            averageGenerationTime: 4.2,\n            knowledgeBase: 15847,\n            currentLearning: [\n                \"Analizează pattern-uri noi din aplicațiile create\",\n                \"Optimizează algoritmi de generare cod\",\n                \"\\xcenvață din feedback-ul utilizatorilor\",\n                \"\\xcembunătățește arhitectura aplicațiilor\"\n            ]\n        });\n    };\n    const getEventIcon = (type)=>{\n        switch(type){\n            case \"APP_CREATED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 34\n                }, this);\n            case \"PATTERN_LEARNED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 38\n                }, this);\n            case \"OPTIMIZATION\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 35\n                }, this);\n            case \"ERROR_CORRECTED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 38\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getEventColor = (type)=>{\n        switch(type){\n            case \"APP_CREATED\":\n                return \"bg-green-100 text-green-800\";\n            case \"PATTERN_LEARNED\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"OPTIMIZATION\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"ERROR_CORRECTED\":\n                return \"bg-orange-100 text-orange-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"AI Evolution Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                isLearning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    className: \"bg-green-100 text-green-800 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-3 w-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Learning...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Monitorizează \\xeen timp real cum AI-ul \\xeenvață și evoluează din fiecare aplicație creată\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Apps Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: metrics.totalAppsCreated.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Patterns Learned\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: metrics.patternsLearned.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-8 w-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Success Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        metrics.successRate.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Knowledge Base\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: metrics.knowledgeBase.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-8 w-8 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Current Learning Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: metrics.currentLearning.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                Math.floor(Math.random() * 40 + 60),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                                                    value: Math.floor(Math.random() * 40 + 60),\n                                                    className: \"h-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Brain_CheckCircle_Code_Cpu_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Real-time Learning Events\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                        children: learningEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg\",\n                                                children: [\n                                                    getEventIcon(event.type),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                        className: getEventColor(event.type),\n                                                                        children: event.type.replace(\"_\", \" \")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: event.timestamp.toLocaleTimeString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4 mt-2 text-xs text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Impact: \",\n                                                                            event.impact,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Confidence: \",\n                                                                            (event.data.confidence * 100).toFixed(1),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Time: \",\n                                                                            event.data.processingTime.toFixed(0),\n                                                                            \"ms\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, event.id, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/ai-evolution/page.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\n_s(AIEvolutionPage, \"U7OpAAVE4VH5XuPpKFgj6PrSMqk=\");\n_c = AIEvolutionPage;\nvar _c;\n$RefreshReg$(_c, \"AIEvolutionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-evolution/page.tsx\n"));

/***/ })

});