"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/interactive-creation/page",{

/***/ "(app-pages-browser)/./src/app/interactive-creation/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/interactive-creation/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InteractiveCreationPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction InteractiveCreationPage() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [livePreview, setLivePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentProject, setCurrentProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Mesaj de bun venit\n        const welcomeMessage = {\n            id: \"welcome\",\n            type: \"ai\",\n            content: \"Salut! Sunt AI-ul tău pentru crearea aplicațiilor mobile. Spune-mi ce vrei să construim \\xeempreună și voi genera codul \\xeen timp real pe măsură ce vorbim. Poți să-mi ceri să modific, să adaug funcții sau să optimizez orice parte din aplicație.\",\n            timestamp: new Date()\n        };\n        setMessages([\n            welcomeMessage\n        ]);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isGenerating) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            type: \"user\",\n            content: inputMessage,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputMessage(\"\");\n        setIsGenerating(true);\n        // Simulare răspuns AI cu generare cod\n        setTimeout(()=>{\n            const aiResponse = generateAIResponse(inputMessage);\n            const aiMessage = {\n                id: \"ai_\".concat(Date.now()),\n                type: \"ai\",\n                content: aiResponse.content,\n                timestamp: new Date(),\n                codeGenerated: aiResponse.code,\n                preview: aiResponse.preview\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n            if (aiResponse.code) {\n                updateLivePreview(aiResponse.code, aiResponse.platform);\n            }\n            setIsGenerating(false);\n        }, 2000 + Math.random() * 2000);\n    };\n    const generateAIResponse = (userInput)=>{\n        const input = userInput.toLowerCase();\n        if (input.includes(\"login\") || input.includes(\"autentificare\")) {\n            return {\n                content: \"Perfect! Creez un sistem de login pentru tine. Voi genera un ecran de login cu validare și animații frumoase.\",\n                code: '// Login Screen Component\\n@Composable\\nfun LoginScreen() {\\n    var email by remember { mutableStateOf(\"\") }\\n    var password by remember { mutableStateOf(\"\") }\\n    var isLoading by remember { mutableStateOf(false) }\\n    \\n    Column(\\n        modifier = Modifier\\n            .fillMaxSize()\\n            .padding(24.dp),\\n        horizontalAlignment = Alignment.CenterHorizontally,\\n        verticalArrangement = Arrangement.Center\\n    ) {\\n        Text(\\n            text = \"Welcome Back\",\\n            style = MaterialTheme.typography.headlineLarge,\\n            fontWeight = FontWeight.Bold\\n        )\\n        \\n        Spacer(modifier = Modifier.height(32.dp))\\n        \\n        OutlinedTextField(\\n            value = email,\\n            onValueChange = { email = it },\\n            label = { Text(\"Email\") },\\n            modifier = Modifier.fillMaxWidth(),\\n            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email)\\n        )\\n        \\n        Spacer(modifier = Modifier.height(16.dp))\\n        \\n        OutlinedTextField(\\n            value = password,\\n            onValueChange = { password = it },\\n            label = { Text(\"Password\") },\\n            modifier = Modifier.fillMaxWidth(),\\n            visualTransformation = PasswordVisualTransformation(),\\n            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password)\\n        )\\n        \\n        Spacer(modifier = Modifier.height(24.dp))\\n        \\n        Button(\\n            onClick = { \\n                isLoading = true\\n                // Handle login logic\\n            },\\n            modifier = Modifier.fillMaxWidth(),\\n            enabled = !isLoading\\n        ) {\\n            if (isLoading) {\\n                CircularProgressIndicator(\\n                    modifier = Modifier.size(16.dp),\\n                    color = MaterialTheme.colorScheme.onPrimary\\n                )\\n            } else {\\n                Text(\"Sign In\")\\n            }\\n        }\\n    }\\n}',\n                platform: \"Android\",\n                preview: \"Login screen cu c\\xe2mpuri pentru email și parolă, buton de login cu indicator de \\xeencărcare\"\n            };\n        }\n        if (input.includes(\"dashboard\") || input.includes(\"home\")) {\n            return {\n                content: \"Creez un dashboard modern cu statistici și navigare rapidă. Voi adăuga carduri interactive și grafice.\",\n                code: '// Dashboard Screen\\n@Composable\\nfun DashboardScreen() {\\n    LazyColumn(\\n        modifier = Modifier.fillMaxSize(),\\n        contentPadding = PaddingValues(16.dp),\\n        verticalArrangement = Arrangement.spacedBy(16.dp)\\n    ) {\\n        item {\\n            Text(\\n                text = \"Dashboard\",\\n                style = MaterialTheme.typography.headlineLarge,\\n                fontWeight = FontWeight.Bold\\n            )\\n        }\\n        \\n        item {\\n            LazyRow(\\n                horizontalArrangement = Arrangement.spacedBy(12.dp)\\n            ) {\\n                items(3) { index ->\\n                    StatsCard(\\n                        title = when(index) {\\n                            0 -> \"Total Users\"\\n                            1 -> \"Revenue\"\\n                            else -> \"Growth\"\\n                        },\\n                        value = when(index) {\\n                            0 -> \"1,234\"\\n                            1 -> \"$12,345\"\\n                            else -> \"+23%\"\\n                        },\\n                        icon = when(index) {\\n                            0 -> Icons.Default.People\\n                            1 -> Icons.Default.AttachMoney\\n                            else -> Icons.Default.TrendingUp\\n                        }\\n                    )\\n                }\\n            }\\n        }\\n        \\n        item {\\n            Card(\\n                modifier = Modifier.fillMaxWidth()\\n            ) {\\n                Column(\\n                    modifier = Modifier.padding(16.dp)\\n                ) {\\n                    Text(\\n                        text = \"Recent Activity\",\\n                        style = MaterialTheme.typography.titleMedium,\\n                        fontWeight = FontWeight.SemiBold\\n                    )\\n                    Spacer(modifier = Modifier.height(12.dp))\\n                    // Activity list here\\n                }\\n            }\\n        }\\n    }\\n}\\n\\n@Composable\\nfun StatsCard(title: String, value: String, icon: ImageVector) {\\n    Card(\\n        modifier = Modifier.width(120.dp)\\n    ) {\\n        Column(\\n            modifier = Modifier.padding(12.dp),\\n            horizontalAlignment = Alignment.CenterHorizontally\\n        ) {\\n            Icon(icon, contentDescription = null)\\n            Spacer(modifier = Modifier.height(8.dp))\\n            Text(value, fontWeight = FontWeight.Bold)\\n            Text(title, fontSize = 12.sp)\\n        }\\n    }\\n}',\n                platform: \"Android\",\n                preview: \"Dashboard cu carduri de statistici, activitate recentă și navigare intuitivă\"\n            };\n        }\n        if (input.includes(\"chat\") || input.includes(\"mesaje\")) {\n            return {\n                content: \"Construiesc o interfață de chat \\xeen timp real cu bule de mesaje și funcții avansate.\",\n                code: '// Chat Screen\\n@Composable\\nfun ChatScreen() {\\n    var messageText by remember { mutableStateOf(\"\") }\\n    val messages = remember { mutableStateListOf<ChatMessage>() }\\n    \\n    Column(\\n        modifier = Modifier.fillMaxSize()\\n    ) {\\n        // Chat messages\\n        LazyColumn(\\n            modifier = Modifier\\n                .weight(1f)\\n                .padding(horizontal = 16.dp),\\n            reverseLayout = true\\n        ) {\\n            items(messages.reversed()) { message ->\\n                ChatBubble(\\n                    message = message,\\n                    isCurrentUser = message.isFromCurrentUser\\n                )\\n            }\\n        }\\n        \\n        // Message input\\n        Row(\\n            modifier = Modifier\\n                .fillMaxWidth()\\n                .padding(16.dp),\\n            verticalAlignment = Alignment.CenterVertically\\n        ) {\\n            OutlinedTextField(\\n                value = messageText,\\n                onValueChange = { messageText = it },\\n                modifier = Modifier.weight(1f),\\n                placeholder = { Text(\"Type a message...\") },\\n                shape = RoundedCornerShape(24.dp)\\n            )\\n            \\n            Spacer(modifier = Modifier.width(8.dp))\\n            \\n            FloatingActionButton(\\n                onClick = {\\n                    if (messageText.isNotBlank()) {\\n                        messages.add(\\n                            ChatMessage(\\n                                text = messageText,\\n                                isFromCurrentUser = true,\\n                                timestamp = System.currentTimeMillis()\\n                            )\\n                        )\\n                        messageText = \"\"\\n                    }\\n                },\\n                modifier = Modifier.size(48.dp)\\n            ) {\\n                Icon(Icons.Default.Send, contentDescription = \"Send\")\\n            }\\n        }\\n    }\\n}\\n\\n@Composable\\nfun ChatBubble(message: ChatMessage, isCurrentUser: Boolean) {\\n    Row(\\n        modifier = Modifier\\n            .fillMaxWidth()\\n            .padding(vertical = 4.dp),\\n        horizontalArrangement = if (isCurrentUser) Arrangement.End else Arrangement.Start\\n    ) {\\n        Card(\\n            colors = CardDefaults.cardColors(\\n                containerColor = if (isCurrentUser) \\n                    MaterialTheme.colorScheme.primary \\n                else \\n                    MaterialTheme.colorScheme.surfaceVariant\\n            ),\\n            shape = RoundedCornerShape(\\n                topStart = 16.dp,\\n                topEnd = 16.dp,\\n                bottomStart = if (isCurrentUser) 16.dp else 4.dp,\\n                bottomEnd = if (isCurrentUser) 4.dp else 16.dp\\n            )\\n        ) {\\n            Text(\\n                text = message.text,\\n                modifier = Modifier.padding(12.dp),\\n                color = if (isCurrentUser) \\n                    MaterialTheme.colorScheme.onPrimary \\n                else \\n                    MaterialTheme.colorScheme.onSurfaceVariant\\n            )\\n        }\\n    }\\n}',\n                platform: \"Android\",\n                preview: \"Interfață de chat cu bule de mesaje, c\\xe2mp de input și buton de trimitere\"\n            };\n        }\n        // Răspuns generic\n        return {\n            content: \"\\xcențeleg! Vrei să creez \".concat(userInput, \". Să \\xeencep să generez codul pentru această funcționalitate. Voi construi o implementare modernă și eficientă.\"),\n            code: \"// Generated code for: \".concat(userInput, \"\\n@Composable\\nfun CustomComponent() {\\n    // Implementation based on your request: \").concat(userInput, '\\n    Column(\\n        modifier = Modifier\\n            .fillMaxSize()\\n            .padding(16.dp)\\n    ) {\\n        Text(\\n            text = \"Custom Feature: ').concat(userInput, '\",\\n            style = MaterialTheme.typography.headlineMedium\\n        )\\n        \\n        // Add more implementation here\\n        Card(\\n            modifier = Modifier\\n                .fillMaxWidth()\\n                .padding(vertical = 8.dp)\\n        ) {\\n            Text(\\n                text = \"This feature is being implemented...\",\\n                modifier = Modifier.padding(16.dp)\\n            )\\n        }\\n    }\\n}'),\n            platform: \"Android\",\n            preview: \"Componentă personalizată pentru: \".concat(userInput)\n        };\n    };\n    const updateLivePreview = (code, platform)=>{\n        setLivePreview({\n            platform,\n            code: {\n                \"main.kt\": code\n            },\n            preview: \"Live preview updated with new code\"\n        });\n        setCurrentProject(\"Project_\".concat(Date.now()));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Interactive Creation Studio\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"Creează aplicații mobile \\xeen timp real prin conversație cu AI-ul\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-[700px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Live Phone Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this),\n                                            livePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                children: livePreview.platform\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-64 h-[500px] bg-gray-900 rounded-[2.5rem] p-2 shadow-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-full bg-white rounded-[2rem] overflow-hidden relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-6 bg-gray-100 flex items-center justify-between px-4 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"9:41\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-2 bg-green-500 rounded-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 440,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-2 bg-gray-300 rounded-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-2 bg-gray-300 rounded-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 p-4 h-[calc(100%-1.5rem)]\",\n                                                        children: livePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PhonePreview, {\n                                                            preview: livePreview.preview\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center h-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Start chatting to see your app\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-[700px] flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"AI Conversation\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this),\n                                            isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                className: \"bg-blue-100 text-blue-800 animate-pulse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Generating...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"flex-1 flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 overflow-y-auto space-y-4 mb-4\",\n                                            children: [\n                                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex \".concat(message.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-w-[80%] rounded-lg p-3 \".concat(message.type === \"user\" ? \"bg-blue-600 text-white\" : \"bg-gray-100 text-gray-900\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-1\",\n                                                                    children: [\n                                                                        message.type === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs opacity-75\",\n                                                                            children: message.timestamp.toLocaleTimeString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: message.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                message.codeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 p-2 bg-gray-900 rounded text-green-400 text-xs\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-3 w-3 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Code generated\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, message.id, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-start\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-100 rounded-lg p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 519,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                                            style: {\n                                                                                animationDelay: \"0.1s\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 520,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                                            style: {\n                                                                                animationDelay: \"0.2s\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    value: inputMessage,\n                                                    onChange: (e)=>setInputMessage(e.target.value),\n                                                    placeholder: \"Descrie ce vrei să construim...\",\n                                                    onKeyPress: (e)=>e.key === \"Enter\" && handleSendMessage(),\n                                                    disabled: isGenerating\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleSendMessage,\n                                                    disabled: !inputMessage.trim() || isGenerating,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-[700px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Generated Code & Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Test\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Export\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: livePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-900 rounded-lg p-4 h-64 overflow-y-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-green-400 text-xs\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        children: Object.values(livePreview.code)[0]\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3\",\n                                                        children: \"My Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 max-h-48 overflow-y-auto\",\n                                                        children: projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium text-sm\",\n                                                                                children: project.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                                lineNumber: 586,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: project.platform\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                                lineNumber: 587,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 585,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        className: \"text-xs\",\n                                                                        children: project.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 589,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, project.id, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-96\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: \"Start Creating\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"\\xcencepe o conversație cu AI-ul pentru a vedea codul generat \\xeen timp real\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n            lineNumber: 408,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n        lineNumber: 407,\n        columnNumber: 5\n    }, this);\n}\n_s(InteractiveCreationPage, \"FbFL1XuMhb0K2jNl8oN11XX4XQw=\");\n_c = InteractiveCreationPage;\nvar _c;\n$RefreshReg$(_c, \"InteractiveCreationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/interactive-creation/page.tsx\n"));

/***/ })

});