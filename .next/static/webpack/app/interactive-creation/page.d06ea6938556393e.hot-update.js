"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/interactive-creation/page",{

/***/ "(app-pages-browser)/./src/app/interactive-creation/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/interactive-creation/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InteractiveCreationPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Download,Edit3,MessageSquare,Play,Send,Smartphone,User,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction InteractiveCreationPage() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [livePreview, setLivePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentProject, setCurrentProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Mesaj de bun venit\n        const welcomeMessage = {\n            id: \"welcome\",\n            type: \"ai\",\n            content: \"Salut! Sunt AI-ul tău pentru crearea aplicațiilor mobile. Spune-mi ce vrei să construim \\xeempreună și voi genera codul \\xeen timp real pe măsură ce vorbim. Poți să-mi ceri să modific, să adaug funcții sau să optimizez orice parte din aplicație.\",\n            timestamp: new Date()\n        };\n        setMessages([\n            welcomeMessage\n        ]);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isGenerating) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            type: \"user\",\n            content: inputMessage,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputMessage(\"\");\n        setIsGenerating(true);\n        // Simulare răspuns AI cu generare cod\n        setTimeout(()=>{\n            const aiResponse = generateAIResponse(inputMessage);\n            const aiMessage = {\n                id: \"ai_\".concat(Date.now()),\n                type: \"ai\",\n                content: aiResponse.content,\n                timestamp: new Date(),\n                codeGenerated: aiResponse.code,\n                preview: aiResponse.preview\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n            if (aiResponse.code) {\n                updateLivePreview(aiResponse.code, aiResponse.platform);\n            }\n            setIsGenerating(false);\n        }, 2000 + Math.random() * 2000);\n    };\n    const generateAIResponse = (userInput)=>{\n        const input = userInput.toLowerCase();\n        if (input.includes(\"login\") || input.includes(\"autentificare\")) {\n            return {\n                content: \"Perfect! Creez un sistem de login pentru tine. Voi genera un ecran de login cu validare și animații frumoase.\",\n                code: '// Login Screen Component\\n@Composable\\nfun LoginScreen() {\\n    var email by remember { mutableStateOf(\"\") }\\n    var password by remember { mutableStateOf(\"\") }\\n    var isLoading by remember { mutableStateOf(false) }\\n    \\n    Column(\\n        modifier = Modifier\\n            .fillMaxSize()\\n            .padding(24.dp),\\n        horizontalAlignment = Alignment.CenterHorizontally,\\n        verticalArrangement = Arrangement.Center\\n    ) {\\n        Text(\\n            text = \"Welcome Back\",\\n            style = MaterialTheme.typography.headlineLarge,\\n            fontWeight = FontWeight.Bold\\n        )\\n        \\n        Spacer(modifier = Modifier.height(32.dp))\\n        \\n        OutlinedTextField(\\n            value = email,\\n            onValueChange = { email = it },\\n            label = { Text(\"Email\") },\\n            modifier = Modifier.fillMaxWidth(),\\n            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email)\\n        )\\n        \\n        Spacer(modifier = Modifier.height(16.dp))\\n        \\n        OutlinedTextField(\\n            value = password,\\n            onValueChange = { password = it },\\n            label = { Text(\"Password\") },\\n            modifier = Modifier.fillMaxWidth(),\\n            visualTransformation = PasswordVisualTransformation(),\\n            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password)\\n        )\\n        \\n        Spacer(modifier = Modifier.height(24.dp))\\n        \\n        Button(\\n            onClick = { \\n                isLoading = true\\n                // Handle login logic\\n            },\\n            modifier = Modifier.fillMaxWidth(),\\n            enabled = !isLoading\\n        ) {\\n            if (isLoading) {\\n                CircularProgressIndicator(\\n                    modifier = Modifier.size(16.dp),\\n                    color = MaterialTheme.colorScheme.onPrimary\\n                )\\n            } else {\\n                Text(\"Sign In\")\\n            }\\n        }\\n    }\\n}',\n                platform: \"Android\",\n                preview: \"Login screen cu c\\xe2mpuri pentru email și parolă, buton de login cu indicator de \\xeencărcare\"\n            };\n        }\n        if (input.includes(\"dashboard\") || input.includes(\"home\")) {\n            return {\n                content: \"Creez un dashboard modern cu statistici și navigare rapidă. Voi adăuga carduri interactive și grafice.\",\n                code: '// Dashboard Screen\\n@Composable\\nfun DashboardScreen() {\\n    LazyColumn(\\n        modifier = Modifier.fillMaxSize(),\\n        contentPadding = PaddingValues(16.dp),\\n        verticalArrangement = Arrangement.spacedBy(16.dp)\\n    ) {\\n        item {\\n            Text(\\n                text = \"Dashboard\",\\n                style = MaterialTheme.typography.headlineLarge,\\n                fontWeight = FontWeight.Bold\\n            )\\n        }\\n        \\n        item {\\n            LazyRow(\\n                horizontalArrangement = Arrangement.spacedBy(12.dp)\\n            ) {\\n                items(3) { index ->\\n                    StatsCard(\\n                        title = when(index) {\\n                            0 -> \"Total Users\"\\n                            1 -> \"Revenue\"\\n                            else -> \"Growth\"\\n                        },\\n                        value = when(index) {\\n                            0 -> \"1,234\"\\n                            1 -> \"$12,345\"\\n                            else -> \"+23%\"\\n                        },\\n                        icon = when(index) {\\n                            0 -> Icons.Default.People\\n                            1 -> Icons.Default.AttachMoney\\n                            else -> Icons.Default.TrendingUp\\n                        }\\n                    )\\n                }\\n            }\\n        }\\n        \\n        item {\\n            Card(\\n                modifier = Modifier.fillMaxWidth()\\n            ) {\\n                Column(\\n                    modifier = Modifier.padding(16.dp)\\n                ) {\\n                    Text(\\n                        text = \"Recent Activity\",\\n                        style = MaterialTheme.typography.titleMedium,\\n                        fontWeight = FontWeight.SemiBold\\n                    )\\n                    Spacer(modifier = Modifier.height(12.dp))\\n                    // Activity list here\\n                }\\n            }\\n        }\\n    }\\n}\\n\\n@Composable\\nfun StatsCard(title: String, value: String, icon: ImageVector) {\\n    Card(\\n        modifier = Modifier.width(120.dp)\\n    ) {\\n        Column(\\n            modifier = Modifier.padding(12.dp),\\n            horizontalAlignment = Alignment.CenterHorizontally\\n        ) {\\n            Icon(icon, contentDescription = null)\\n            Spacer(modifier = Modifier.height(8.dp))\\n            Text(value, fontWeight = FontWeight.Bold)\\n            Text(title, fontSize = 12.sp)\\n        }\\n    }\\n}',\n                platform: \"Android\",\n                preview: \"Dashboard cu carduri de statistici, activitate recentă și navigare intuitivă\"\n            };\n        }\n        if (input.includes(\"chat\") || input.includes(\"mesaje\")) {\n            return {\n                content: \"Construiesc o interfață de chat \\xeen timp real cu bule de mesaje și funcții avansate.\",\n                code: '// Chat Screen\\n@Composable\\nfun ChatScreen() {\\n    var messageText by remember { mutableStateOf(\"\") }\\n    val messages = remember { mutableStateListOf<ChatMessage>() }\\n    \\n    Column(\\n        modifier = Modifier.fillMaxSize()\\n    ) {\\n        // Chat messages\\n        LazyColumn(\\n            modifier = Modifier\\n                .weight(1f)\\n                .padding(horizontal = 16.dp),\\n            reverseLayout = true\\n        ) {\\n            items(messages.reversed()) { message ->\\n                ChatBubble(\\n                    message = message,\\n                    isCurrentUser = message.isFromCurrentUser\\n                )\\n            }\\n        }\\n        \\n        // Message input\\n        Row(\\n            modifier = Modifier\\n                .fillMaxWidth()\\n                .padding(16.dp),\\n            verticalAlignment = Alignment.CenterVertically\\n        ) {\\n            OutlinedTextField(\\n                value = messageText,\\n                onValueChange = { messageText = it },\\n                modifier = Modifier.weight(1f),\\n                placeholder = { Text(\"Type a message...\") },\\n                shape = RoundedCornerShape(24.dp)\\n            )\\n            \\n            Spacer(modifier = Modifier.width(8.dp))\\n            \\n            FloatingActionButton(\\n                onClick = {\\n                    if (messageText.isNotBlank()) {\\n                        messages.add(\\n                            ChatMessage(\\n                                text = messageText,\\n                                isFromCurrentUser = true,\\n                                timestamp = System.currentTimeMillis()\\n                            )\\n                        )\\n                        messageText = \"\"\\n                    }\\n                },\\n                modifier = Modifier.size(48.dp)\\n            ) {\\n                Icon(Icons.Default.Send, contentDescription = \"Send\")\\n            }\\n        }\\n    }\\n}\\n\\n@Composable\\nfun ChatBubble(message: ChatMessage, isCurrentUser: Boolean) {\\n    Row(\\n        modifier = Modifier\\n            .fillMaxWidth()\\n            .padding(vertical = 4.dp),\\n        horizontalArrangement = if (isCurrentUser) Arrangement.End else Arrangement.Start\\n    ) {\\n        Card(\\n            colors = CardDefaults.cardColors(\\n                containerColor = if (isCurrentUser) \\n                    MaterialTheme.colorScheme.primary \\n                else \\n                    MaterialTheme.colorScheme.surfaceVariant\\n            ),\\n            shape = RoundedCornerShape(\\n                topStart = 16.dp,\\n                topEnd = 16.dp,\\n                bottomStart = if (isCurrentUser) 16.dp else 4.dp,\\n                bottomEnd = if (isCurrentUser) 4.dp else 16.dp\\n            )\\n        ) {\\n            Text(\\n                text = message.text,\\n                modifier = Modifier.padding(12.dp),\\n                color = if (isCurrentUser) \\n                    MaterialTheme.colorScheme.onPrimary \\n                else \\n                    MaterialTheme.colorScheme.onSurfaceVariant\\n            )\\n        }\\n    }\\n}',\n                platform: \"Android\",\n                preview: \"Interfață de chat cu bule de mesaje, c\\xe2mp de input și buton de trimitere\"\n            };\n        }\n        // Răspuns generic\n        return {\n            content: \"\\xcențeleg! Vrei să creez \".concat(userInput, \". Să \\xeencep să generez codul pentru această funcționalitate. Voi construi o implementare modernă și eficientă.\"),\n            code: \"// Generated code for: \".concat(userInput, \"\\n@Composable\\nfun CustomComponent() {\\n    // Implementation based on your request: \").concat(userInput, '\\n    Column(\\n        modifier = Modifier\\n            .fillMaxSize()\\n            .padding(16.dp)\\n    ) {\\n        Text(\\n            text = \"Custom Feature: ').concat(userInput, '\",\\n            style = MaterialTheme.typography.headlineMedium\\n        )\\n        \\n        // Add more implementation here\\n        Card(\\n            modifier = Modifier\\n                .fillMaxWidth()\\n                .padding(vertical = 8.dp)\\n        ) {\\n            Text(\\n                text = \"This feature is being implemented...\",\\n                modifier = Modifier.padding(16.dp)\\n            )\\n        }\\n    }\\n}'),\n            platform: \"Android\",\n            preview: \"Componentă personalizată pentru: \".concat(userInput)\n        };\n    };\n    const updateLivePreview = (code, platform)=>{\n        setLivePreview({\n            platform,\n            code: {\n                \"main.kt\": code\n            },\n            preview: \"Live preview updated with new code\"\n        });\n        setCurrentProject(\"Project_\".concat(Date.now()));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Interactive Creation Studio\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"Creează aplicații mobile \\xeen timp real prin conversație cu AI-ul\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-[700px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Live Phone Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this),\n                                            livePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                children: livePreview.platform\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-64 h-[500px] bg-gray-900 rounded-[2.5rem] p-2 shadow-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-full bg-white rounded-[2rem] overflow-hidden relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-6 bg-gray-100 flex items-center justify-between px-4 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"9:41\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-2 bg-green-500 rounded-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-2 bg-gray-300 rounded-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-2 bg-gray-300 rounded-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 p-4 h-[calc(100%-1.5rem)]\",\n                                                        children: livePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PhonePreview, {\n                                                            preview: livePreview.preview\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center h-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Start chatting to see your app\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-[700px] flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"AI Conversation\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this),\n                                            isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                className: \"bg-blue-100 text-blue-800 animate-pulse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Generating...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"flex-1 flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 overflow-y-auto space-y-4 mb-4\",\n                                            children: [\n                                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex \".concat(message.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"max-w-[80%] rounded-lg p-3 \".concat(message.type === \"user\" ? \"bg-blue-600 text-white\" : \"bg-gray-100 text-gray-900\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-1\",\n                                                                    children: [\n                                                                        message.type === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs opacity-75\",\n                                                                            children: message.timestamp.toLocaleTimeString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 500,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: message.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                message.codeGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 p-2 bg-gray-900 rounded text-green-400 text-xs\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-3 w-3 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 507,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Code generated\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, message.id, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-start\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-100 rounded-lg p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 520,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                                            style: {\n                                                                                animationDelay: \"0.1s\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                                            style: {\n                                                                                animationDelay: \"0.2s\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                            lineNumber: 522,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                    lineNumber: 519,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    value: inputMessage,\n                                                    onChange: (e)=>setInputMessage(e.target.value),\n                                                    placeholder: \"Descrie ce vrei să construim...\",\n                                                    onKeyPress: (e)=>e.key === \"Enter\" && handleSendMessage(),\n                                                    disabled: isGenerating\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleSendMessage,\n                                                    disabled: !inputMessage.trim() || isGenerating,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-[700px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Generated Code & Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Test\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Export\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: livePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-900 rounded-lg p-4 h-64 overflow-y-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-green-400 text-xs\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        children: Object.values(livePreview.code)[0]\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3\",\n                                                        children: \"My Projects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 max-h-48 overflow-y-auto\",\n                                                        children: projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium text-sm\",\n                                                                                children: project.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                                lineNumber: 587,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: project.platform\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                                lineNumber: 588,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        className: \"text-xs\",\n                                                                        children: project.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, project.id, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-96\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Code_Download_Edit3_MessageSquare_Play_Send_Smartphone_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: \"Start Creating\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"\\xcencepe o conversație cu AI-ul pentru a vedea codul generat \\xeen timp real\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n                    lineNumber: 420,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n            lineNumber: 409,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/interactive-creation/page.tsx\",\n        lineNumber: 408,\n        columnNumber: 5\n    }, this);\n}\n_s(InteractiveCreationPage, \"qYN5pYVT5dqQNA4mefzt3j5RKjE=\");\n_c = InteractiveCreationPage;\nvar _c;\n$RefreshReg$(_c, \"InteractiveCreationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/interactive-creation/page.tsx\n"));

/***/ })

});