"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/realtime-creation/page",{

/***/ "(app-pages-browser)/./src/app/realtime-creation/page.tsx":
/*!********************************************!*\
  !*** ./src/app/realtime-creation/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RealtimeCreationPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Code,Download,Eye,Layers,Play,Smartphone,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Code,Download,Eye,Layers,Play,Smartphone,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Code,Download,Eye,Layers,Play,Smartphone,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Code,Download,Eye,Layers,Play,Smartphone,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Code,Download,Eye,Layers,Play,Smartphone,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Code,Download,Eye,Layers,Play,Smartphone,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Code,Download,Eye,Layers,Play,Smartphone,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Code,Download,Eye,Layers,Play,Smartphone,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Code,Download,Eye,Layers,Play,Smartphone,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction RealtimeCreationPage() {\n    _s();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSession, setSelectedSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newAppName, setNewAppName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newAppDescription, setNewAppDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedPlatform, setSelectedPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ANDROID\");\n    const [selectedFeatures, setSelectedFeatures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedApps, setSavedApps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentPreview, setCurrentPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [continuousLearning, setContinuousLearning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [autoGeneration, setAutoGeneration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const features = [\n        \"user-authentication\",\n        \"push-notifications\",\n        \"location-services\",\n        \"camera-integration\",\n        \"payment-integration\",\n        \"real-time-sync\",\n        \"offline-mode\",\n        \"analytics\",\n        \"social-sharing\",\n        \"dark-mode\"\n    ];\n    const currentSession = sessions.find((s)=>s.id === selectedSession);\n    // Load saved apps on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSavedApps();\n    }, []);\n    const loadSavedApps = async ()=>{\n        try {\n            const response = await fetch(\"/api/apps/saved\");\n            if (response.ok) {\n                const data = await response.json();\n                setSavedApps(data.apps || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading saved apps:\", error);\n        }\n    };\n    const startNewCreation = async ()=>{\n        if (!newAppName.trim() || !newAppDescription.trim()) {\n            alert(\"Please fill in app name and description\");\n            return;\n        }\n        setIsCreating(true);\n        try {\n            // Create new session\n            const newSession1 = {\n                id: Date.now().toString(),\n                name: newAppName,\n                platform: selectedPlatform,\n                status: \"active\",\n                progress: 0,\n                currentStep: \"Initializing project...\",\n                createdAt: new Date().toISOString(),\n                expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()\n            };\n            setSessions((prev)=>[\n                    ...prev,\n                    newSession1\n                ]);\n            setSelectedSession(newSession1.id);\n            // Simulate progress updates\n            const progressInterval = setInterval(()=>{\n                setSessions((prev)=>prev.map((session)=>{\n                        if (session.id === newSession1.id && session.progress < 90) {\n                            const newProgress = session.progress + Math.random() * 15;\n                            const steps = [\n                                \"Initializing project...\",\n                                \"Setting up dependencies...\",\n                                \"Generating UI components...\",\n                                \"Implementing features...\",\n                                \"Adding navigation...\",\n                                \"Configuring state management...\",\n                                \"Setting up API integration...\",\n                                \"Adding authentication...\",\n                                \"Implementing push notifications...\",\n                                \"Finalizing build...\"\n                            ];\n                            const stepIndex = Math.floor(newProgress / 100 * steps.length);\n                            return {\n                                ...session,\n                                progress: Math.min(newProgress, 90),\n                                currentStep: steps[stepIndex] || \"Processing...\"\n                            };\n                        }\n                        return session;\n                    }));\n            }, 1500);\n            // Start app generation\n            const response = await fetch(\"/api/apps/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"x-user-id\": \"realtime-user\"\n                },\n                body: JSON.stringify({\n                    name: newAppName,\n                    description: newAppDescription,\n                    platform: selectedPlatform,\n                    features: selectedFeatures\n                })\n            });\n            clearInterval(progressInterval);\n            if (response.ok) {\n                const result = await response.json();\n                // Update session with completion\n                setSessions((prev)=>prev.map((s)=>s.id === newSession1.id ? {\n                            ...s,\n                            status: \"completed\",\n                            progress: 100,\n                            currentStep: \"Generation completed!\"\n                        } : s));\n                // Add to saved apps\n                const savedApp = {\n                    id: result.app.id,\n                    name: result.app.name,\n                    platform: result.app.platform,\n                    status: result.app.status,\n                    createdAt: new Date().toISOString(),\n                    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),\n                    daysLeft: 7\n                };\n                setSavedApps((prev)=>[\n                        savedApp,\n                        ...prev\n                    ]);\n                setCurrentPreview(result.app);\n                // Clear form\n                setNewAppName(\"\");\n                setNewAppDescription(\"\");\n                setSelectedFeatures([]);\n            } else {\n                throw new Error(\"Failed to generate app\");\n            }\n        } catch (error) {\n            console.error(\"Error creating app:\", error);\n            alert(\"Error creating app. Please try again.\");\n            // Remove failed session\n            setSessions((prev)=>prev.filter((s)=>s.id !== newSession.id));\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const deleteApp = async (appId)=>{\n        if (confirm(\"Are you sure you want to delete this app?\")) {\n            setSavedApps((prev)=>prev.filter((app)=>app.id !== appId));\n        }\n    };\n    const viewAppDetails = async (appId)=>{\n        try {\n            const response = await fetch(\"/api/apps/\".concat(appId));\n            if (response.ok) {\n                const data = await response.json();\n                setCurrentPreview(data.app);\n            }\n        } catch (error) {\n            console.error(\"Error loading app details:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Real-time Creation Monitor\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Urmărește \\xeen timp real cum AI-ul creează aplicații mobile pas cu pas\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        className: \"bg-blue-600 hover:bg-blue-700\",\n                        onClick: ()=>window.location.reload(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this),\n                            \"Refresh Monitor\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Active Creations\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"secondary\",\n                                                children: sessions.length\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                sessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg border cursor-pointer transition-colors \".concat(selectedSession === session.id ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        onClick: ()=>setSelectedSession(session.id),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium\",\n                                                                        children: session.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: session.status === \"active\" ? \"default\" : session.status === \"completed\" ? \"secondary\" : \"outline\",\n                                                                        className: \"text-xs\",\n                                                                        children: session.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mb-2\",\n                                                                children: session.platform\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Progress\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                lineNumber: 262,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    Math.round(session.progress),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                                        value: session.progress,\n                                                                        className: \"h-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-2\",\n                                                                children: session.currentStep\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, session.id, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                sessions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-8 h-8 mx-auto mb-2 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: \"No active creations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 p-4 border rounded-lg bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Start New Creation\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            placeholder: \"App name\",\n                                                            value: newAppName,\n                                                            onChange: (e)=>setNewAppName(e.target.value),\n                                                            disabled: isCreating\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                            placeholder: \"App description\",\n                                                            value: newAppDescription,\n                                                            onChange: (e)=>setNewAppDescription(e.target.value),\n                                                            rows: 2,\n                                                            disabled: isCreating\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                            value: selectedPlatform,\n                                                            onValueChange: setSelectedPlatform,\n                                                            disabled: isCreating,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                            value: \"ANDROID\",\n                                                                            children: \"Android\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                            lineNumber: 301,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                            value: \"IOS\",\n                                                                            children: \"iOS\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                            lineNumber: 302,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                            value: \"CROSS_PLATFORM\",\n                                                                            children: \"Cross-Platform\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            className: \"w-full\",\n                                                            size: \"sm\",\n                                                            onClick: startNewCreation,\n                                                            disabled: isCreating || !newAppName.trim() || !newAppDescription.trim(),\n                                                            children: isCreating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Creating...\"\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Start Creation\"\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Live Phone Preview\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-64 h-[500px] bg-black rounded-[2.5rem] p-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-full bg-white rounded-[2rem] overflow-hidden\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-6 bg-gray-100 flex items-center justify-between px-4 text-xs\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"9:41\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                    lineNumber: 350,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex gap-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-4 h-2 bg-green-500 rounded-sm\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                            lineNumber: 352,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-4 h-2 bg-gray-300 rounded-sm\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                            lineNumber: 353,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-4 h-2 bg-gray-300 rounded-sm\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                            lineNumber: 354,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                    lineNumber: 351,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-4 h-full\",\n                                                                            children: currentSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-center mb-4\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                                                className: \"text-lg font-bold\",\n                                                                                                children: currentSession.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                lineNumber: 363,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm text-gray-600\",\n                                                                                                children: [\n                                                                                                    currentSession.platform,\n                                                                                                    \" App\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                lineNumber: 364,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                        lineNumber: 362,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-3\",\n                                                                                        children: [\n                                                                                            currentSession.progress > 20 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"h-8 bg-blue-100 rounded flex items-center px-3\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"w-4 h-4 bg-blue-500 rounded mr-2\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                        lineNumber: 371,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"text-sm\",\n                                                                                                        children: \"Search...\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                        lineNumber: 372,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                lineNumber: 370,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            currentSession.progress > 40 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"grid grid-cols-2 gap-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"h-20 bg-gray-100 rounded flex items-center justify-center\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-xs\",\n                                                                                                            children: \"Item 1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                            lineNumber: 379,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                        lineNumber: 378,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"h-20 bg-gray-100 rounded flex items-center justify-center\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-xs\",\n                                                                                                            children: \"Item 2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                            lineNumber: 382,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                        lineNumber: 381,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                lineNumber: 377,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            currentSession.progress > 60 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"h-12 bg-green-500 rounded flex items-center justify-center\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-white text-sm font-medium\",\n                                                                                                    children: \"Action Button\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                    lineNumber: 389,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                lineNumber: 388,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            currentSession.progress > 80 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"space-y-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"h-6 bg-gray-200 rounded\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                        lineNumber: 395,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"h-6 bg-gray-200 rounded w-3/4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                        lineNumber: 396,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"h-6 bg-gray-200 rounded w-1/2\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                        lineNumber: 397,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                                lineNumber: 394,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                        lineNumber: 368,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                lineNumber: 361,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center text-gray-500 mt-20\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                        className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                        lineNumber: 404,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: \"Start creating an app to see live preview\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                        lineNumber: 405,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                lineNumber: 403,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                            lineNumber: 359,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            currentSession && currentSession.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-4 left-1/2 transform -translate-x-1/2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 bg-white px-3 py-1 rounded-full shadow-lg border\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs\",\n                                                                            children: \"Building...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                            lineNumber: 417,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    children: \"Creation Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: currentSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Overall Progress\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                Math.round(currentSession.progress),\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                                    value: currentSession.progress,\n                                                                    className: \"h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Current: \",\n                                                                        currentSession.currentStep\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-sm\",\n                                                                    children: \"Steps Completed:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                [\n                                                                    {\n                                                                        step: \"Project Setup\",\n                                                                        completed: currentSession.progress > 10\n                                                                    },\n                                                                    {\n                                                                        step: \"Dependencies\",\n                                                                        completed: currentSession.progress > 25\n                                                                    },\n                                                                    {\n                                                                        step: \"UI Framework\",\n                                                                        completed: currentSession.progress > 40\n                                                                    },\n                                                                    {\n                                                                        step: \"Components\",\n                                                                        completed: currentSession.progress > 55\n                                                                    },\n                                                                    {\n                                                                        step: \"Navigation\",\n                                                                        completed: currentSession.progress > 70\n                                                                    },\n                                                                    {\n                                                                        step: \"Features\",\n                                                                        completed: currentSession.progress > 85\n                                                                    },\n                                                                    {\n                                                                        step: \"Build\",\n                                                                        completed: currentSession.progress >= 100\n                                                                    }\n                                                                ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-3 h-3 rounded-full \".concat(item.completed ? \"bg-green-500\" : \"bg-gray-300\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                lineNumber: 459,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm \".concat(item.completed ? \"text-green-700\" : \"text-gray-500\"),\n                                                                                children: item.step\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                                lineNumber: 462,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-8 h-8 mx-auto mb-2 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Select an active creation to see progress\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Saved Apps (7-day storage)\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: savedApps.length\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Apps are automatically deleted after 7 days. Download them before they expire.\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: savedApps.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                            children: savedApps.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-4 hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium\",\n                                                                    children: app.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 499,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: app.platform\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 text-sm text-gray-600 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        app.daysLeft,\n                                                                        \" days left\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    className: \"flex-1\",\n                                                                    onClick: ()=>viewAppDetails(app.id),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"View\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    onClick: ()=>{\n                                                                        // Simulate download\n                                                                        const link = document.createElement(\"a\");\n                                                                        link.href = \"data:text/plain;charset=utf-8,\".concat(encodeURIComponent(\"App: \".concat(app.name, \"\\nPlatform: \").concat(app.platform, \"\\nGenerated: \").concat(app.createdAt)));\n                                                                        link.download = \"\".concat(app.name, \".txt\");\n                                                                        link.click();\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                            lineNumber: 531,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Download\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"destructive\",\n                                                                    onClick: ()=>deleteApp(app.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, app.id, true, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Code_Download_Eye_Layers_Play_Smartphone_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-8 h-8 mx-auto mb-2 opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: \"No saved apps yet\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs\",\n                                                    children: \"Generated apps will appear here\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, this);\n}\n_s(RealtimeCreationPage, \"6V0Dadj5YUKcS3ZZQBAvY4tYHNM=\");\n_c = RealtimeCreationPage;\nvar _c;\n$RefreshReg$(_c, \"RealtimeCreationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/realtime-creation/page.tsx\n"));

/***/ })

});