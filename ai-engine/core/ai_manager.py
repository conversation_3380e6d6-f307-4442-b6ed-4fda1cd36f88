"""
AI Manager - Core AI functionality with Ollama integration
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any
import httpx
import logging
from datetime import datetime

from models.responses import ChatResponse
from utils.logger import setup_logger

logger = setup_logger(__name__)


class AIManager:
    """Manages AI interactions with Ollama and learning capabilities"""
    
    def __init__(self):
        self.ollama_host = "http://localhost:11434"
        self.models = {
            "mobile_dev": "mobile-dev-specialist",
            "code_review": "codellama:13b",
            "general": "mistral:7b"
        }
        self.client = None
        self.conversation_contexts = {}
        self.performance_metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0,
            "model_usage": {}
        }
    
    async def initialize(self):
        """Initialize AI Manager"""
        try:
            self.client = httpx.AsyncClient(timeout=30.0)
            
            # Check Ollama connection
            await self.check_ollama_health()
            
            # Ensure required models are available
            await self.ensure_models_available()
            
            logger.info("✅ AI Manager initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize AI Manager: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.client:
            await self.client.aclose()
        logger.info("AI Manager cleanup complete")
    
    async def check_ollama_health(self) -> bool:
        """Check if Ollama is running and accessible"""
        try:
            if not self.client:
                return False
                
            response = await self.client.get(f"{self.ollama_host}/api/tags")
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Ollama health check failed: {e}")
            return False
    
    async def ensure_models_available(self):
        """Ensure all required models are available"""
        try:
            response = await self.client.get(f"{self.ollama_host}/api/tags")
            if response.status_code != 200:
                raise Exception("Failed to get model list from Ollama")
            
            available_models = [model["name"] for model in response.json().get("models", [])]
            
            for model_type, model_name in self.models.items():
                if model_name not in available_models:
                    logger.warning(f"Model {model_name} not found, attempting to pull...")
                    await self.pull_model(model_name)
            
        except Exception as e:
            logger.error(f"Failed to ensure models available: {e}")
            # Continue with available models
    
    async def pull_model(self, model_name: str):
        """Pull a model from Ollama"""
        try:
            logger.info(f"Pulling model: {model_name}")
            
            response = await self.client.post(
                f"{self.ollama_host}/api/pull",
                json={"name": model_name},
                timeout=300.0  # 5 minutes timeout for model pulling
            )
            
            if response.status_code == 200:
                logger.info(f"✅ Model {model_name} pulled successfully")
            else:
                logger.error(f"❌ Failed to pull model {model_name}: {response.text}")
                
        except Exception as e:
            logger.error(f"Error pulling model {model_name}: {e}")
    
    async def process_chat(
        self,
        message: str,
        context: Dict[str, Any],
        user_id: str,
        conversation_id: Optional[str] = None
    ) -> ChatResponse:
        """Process chat message with AI"""
        start_time = time.time()
        
        try:
            self.performance_metrics["total_requests"] += 1
            
            # Determine best model for the request
            model_name = self.select_model(message, context)
            
            # Build conversation context
            conversation_context = self.build_conversation_context(
                conversation_id, message, context
            )
            
            # Generate AI response
            ai_response = await self.generate_response(
                model_name, conversation_context, message
            )
            
            # Update conversation context
            if conversation_id:
                self.update_conversation_context(
                    conversation_id, message, ai_response
                )
            
            # Update metrics
            response_time = time.time() - start_time
            self.update_metrics(model_name, response_time, True)
            
            return ChatResponse(
                message=ai_response,
                context=context,
                model_used=model_name,
                response_time=response_time,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            logger.error(f"Chat processing failed: {e}")
            self.performance_metrics["failed_requests"] += 1
            
            # Return fallback response
            return ChatResponse(
                message="I apologize, but I'm experiencing technical difficulties. Please try again in a moment.",
                context=context,
                model_used="fallback",
                response_time=time.time() - start_time,
                timestamp=datetime.now().isoformat(),
                error=str(e)
            )
    
    def select_model(self, message: str, context: Dict[str, Any]) -> str:
        """Select the best model for the given message and context"""
        message_lower = message.lower()
        
        # Mobile development related
        mobile_keywords = [
            "android", "ios", "mobile", "app", "application",
            "kotlin", "swift", "react native", "flutter"
        ]
        
        # Code review related
        code_keywords = [
            "review", "analyze", "bug", "error", "optimize",
            "refactor", "performance", "security"
        ]
        
        if any(keyword in message_lower for keyword in mobile_keywords):
            return self.models["mobile_dev"]
        elif any(keyword in message_lower for keyword in code_keywords):
            return self.models["code_review"]
        else:
            return self.models["general"]
    
    def build_conversation_context(
        self,
        conversation_id: Optional[str],
        current_message: str,
        context: Dict[str, Any]
    ) -> List[Dict[str, str]]:
        """Build conversation context for AI"""
        messages = []
        
        # System prompt
        system_prompt = self.get_system_prompt(context)
        messages.append({"role": "system", "content": system_prompt})
        
        # Previous conversation history
        if conversation_id and conversation_id in self.conversation_contexts:
            history = self.conversation_contexts[conversation_id]
            messages.extend(history[-10:])  # Keep last 10 messages
        
        # Current message
        messages.append({"role": "user", "content": current_message})
        
        return messages
    
    def get_system_prompt(self, context: Dict[str, Any]) -> str:
        """Get system prompt based on context"""
        base_prompt = """You are an expert mobile application developer and AI assistant for AndroidWeb Enterprise. 
        You specialize in creating Android and iOS applications through conversational AI.
        
        Your capabilities include:
        - Generating complete mobile application code
        - Providing architecture recommendations
        - Debugging and optimizing code
        - Explaining complex concepts simply
        - Creating UI/UX designs
        
        Always provide practical, actionable advice and code examples when relevant.
        Be concise but thorough in your explanations."""
        
        # Add context-specific instructions
        if context.get("project_type") == "android":
            base_prompt += "\n\nFocus on Android development using Kotlin and modern Android architecture components."
        elif context.get("project_type") == "ios":
            base_prompt += "\n\nFocus on iOS development using Swift and modern iOS frameworks."
        elif context.get("project_type") == "cross_platform":
            base_prompt += "\n\nFocus on cross-platform development using React Native or Flutter."
        
        return base_prompt
    
    async def generate_response(
        self,
        model_name: str,
        messages: List[Dict[str, str]],
        current_message: str
    ) -> str:
        """Generate AI response using Ollama"""
        try:
            # Prepare request payload
            payload = {
                "model": model_name,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_tokens": 2048
                }
            }
            
            # Make request to Ollama
            response = await self.client.post(
                f"{self.ollama_host}/api/chat",
                json=payload,
                timeout=60.0
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("message", {}).get("content", "")
            else:
                logger.error(f"Ollama API error: {response.status_code} - {response.text}")
                return "I apologize, but I'm having trouble processing your request right now."
                
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return "I encountered an error while processing your request. Please try again."
    
    def update_conversation_context(
        self,
        conversation_id: str,
        user_message: str,
        ai_response: str
    ):
        """Update conversation context"""
        if conversation_id not in self.conversation_contexts:
            self.conversation_contexts[conversation_id] = []
        
        context = self.conversation_contexts[conversation_id]
        context.append({"role": "user", "content": user_message})
        context.append({"role": "assistant", "content": ai_response})
        
        # Keep only last 20 messages to prevent context overflow
        if len(context) > 20:
            self.conversation_contexts[conversation_id] = context[-20:]
    
    def update_metrics(self, model_name: str, response_time: float, success: bool):
        """Update performance metrics"""
        if success:
            self.performance_metrics["successful_requests"] += 1
        
        # Update average response time
        total_successful = self.performance_metrics["successful_requests"]
        current_avg = self.performance_metrics["average_response_time"]
        self.performance_metrics["average_response_time"] = (
            (current_avg * (total_successful - 1) + response_time) / total_successful
        )
        
        # Update model usage
        if model_name not in self.performance_metrics["model_usage"]:
            self.performance_metrics["model_usage"][model_name] = 0
        self.performance_metrics["model_usage"][model_name] += 1
    
    async def analyze_code(
        self,
        code: str,
        language: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze code quality and provide suggestions"""
        try:
            analysis_prompt = f"""
            Analyze the following {language} code and provide:
            1. Code quality score (1-10)
            2. Potential issues or bugs
            3. Performance improvements
            4. Security considerations
            5. Best practices recommendations
            
            Code:
            ```{language}
            {code}
            ```
            
            Provide your analysis in JSON format.
            """
            
            messages = [
                {"role": "system", "content": "You are a code analysis expert."},
                {"role": "user", "content": analysis_prompt}
            ]
            
            response = await self.generate_response(
                self.models["code_review"], messages, analysis_prompt
            )
            
            # Try to parse JSON response
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                return {
                    "analysis": response,
                    "quality_score": 7,
                    "issues": [],
                    "suggestions": []
                }
                
        except Exception as e:
            logger.error(f"Code analysis failed: {e}")
            return {
                "error": str(e),
                "quality_score": 0,
                "issues": ["Analysis failed"],
                "suggestions": []
            }
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get AI manager metrics"""
        return {
            "performance": self.performance_metrics,
            "models_available": list(self.models.values()),
            "active_conversations": len(self.conversation_contexts),
            "ollama_connected": await self.check_ollama_health()
        }
