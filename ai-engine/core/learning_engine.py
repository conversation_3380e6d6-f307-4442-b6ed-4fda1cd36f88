"""
Learning Engine - Continuous learning and improvement system
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import logging

from utils.database import get_database
from utils.logger import setup_logger

logger = setup_logger(__name__)


class LearningEngine:
    """Manages continuous learning and AI improvement"""
    
    def __init__(self):
        self.db = None
        self.learning_data = []
        self.pattern_cache = {}
        self.feedback_threshold = 4.0  # Minimum rating for positive feedback
        self.learning_batch_size = 100
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        self.metrics = {
            "total_interactions": 0,
            "positive_feedback_count": 0,
            "negative_feedback_count": 0,
            "average_rating": 0.0,
            "learning_cycles": 0,
            "patterns_discovered": 0
        }
    
    async def initialize(self):
        """Initialize Learning Engine"""
        try:
            self.db = get_database()
            
            # Load existing learning data
            await self.load_learning_data()
            
            # Start background learning task
            asyncio.create_task(self.continuous_learning_loop())
            
            logger.info("✅ Learning Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Learning Engine: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup resources"""
        # Save current learning state
        await self.save_learning_state()
        logger.info("Learning Engine cleanup complete")
    
    def is_healthy(self) -> bool:
        """Check if learning engine is healthy"""
        return self.db is not None
    
    async def load_learning_data(self):
        """Load existing learning data from database"""
        try:
            # Load recent interactions for pattern analysis
            query = """
            SELECT 
                ald.*,
                c.messages,
                c.ai_context,
                p.platform,
                p.template_type
            FROM ai_learning_data ald
            JOIN conversations c ON ald.conversation_id = c.id
            LEFT JOIN projects p ON c.project_id = p.id
            WHERE ald.created_at > NOW() - INTERVAL '30 days'
            ORDER BY ald.created_at DESC
            LIMIT 1000
            """
            
            results = await self.db.fetch_all(query)
            
            for row in results:
                self.learning_data.append({
                    "id": row["id"],
                    "conversation_id": row["conversation_id"],
                    "user_feedback": row["user_feedback"],
                    "success_metrics": row["success_metrics"],
                    "code_quality_score": row["code_quality_score"],
                    "user_satisfaction": row["user_satisfaction"],
                    "learning_patterns": row["learning_patterns"],
                    "messages": row["messages"],
                    "ai_context": row["ai_context"],
                    "platform": row["platform"],
                    "template_type": row["template_type"],
                    "created_at": row["created_at"]
                })
            
            logger.info(f"Loaded {len(self.learning_data)} learning data points")
            
        except Exception as e:
            logger.error(f"Failed to load learning data: {e}")
    
    async def record_interaction(
        self,
        user_id: str,
        conversation_id: str,
        input_message: str,
        ai_response: str,
        context: Dict[str, Any]
    ):
        """Record AI interaction for learning"""
        try:
            self.metrics["total_interactions"] += 1
            
            # Analyze interaction quality
            quality_score = await self.analyze_interaction_quality(
                input_message, ai_response, context
            )
            
            # Extract patterns
            patterns = await self.extract_patterns(
                input_message, ai_response, context
            )
            
            # Store in database
            query = """
            INSERT INTO ai_learning_data (
                conversation_id,
                code_quality_score,
                learning_patterns,
                processing_time,
                tokens_used,
                created_at
            ) VALUES ($1, $2, $3, $4, $5, $6)
            """
            
            await self.db.execute(
                query,
                conversation_id,
                quality_score,
                json.dumps(patterns),
                context.get("processing_time", 0),
                context.get("tokens_used", 0),
                datetime.now()
            )
            
            logger.debug(f"Recorded interaction for conversation {conversation_id}")
            
        except Exception as e:
            logger.error(f"Failed to record interaction: {e}")
    
    async def record_generation(
        self,
        user_id: str,
        project_id: str,
        requirements: str,
        generated_code: str,
        platform: str
    ):
        """Record app generation for learning"""
        try:
            # Analyze code quality
            code_quality = await self.analyze_generated_code(generated_code, platform)
            
            # Extract generation patterns
            patterns = await self.extract_generation_patterns(
                requirements, generated_code, platform
            )
            
            # Store learning data
            # This would be stored in the database with project association
            
            logger.debug(f"Recorded generation for project {project_id}")
            
        except Exception as e:
            logger.error(f"Failed to record generation: {e}")
    
    async def process_feedback(
        self,
        user_id: str,
        conversation_id: Optional[str],
        project_id: Optional[str],
        rating: int,
        feedback_text: Optional[str],
        feedback_type: str
    ):
        """Process user feedback for learning"""
        try:
            # Update metrics
            if rating >= self.feedback_threshold:
                self.metrics["positive_feedback_count"] += 1
            else:
                self.metrics["negative_feedback_count"] += 1
            
            # Update average rating
            total_feedback = (
                self.metrics["positive_feedback_count"] + 
                self.metrics["negative_feedback_count"]
            )
            current_avg = self.metrics["average_rating"]
            self.metrics["average_rating"] = (
                (current_avg * (total_feedback - 1) + rating) / total_feedback
            )
            
            # Store feedback in database
            if conversation_id:
                query = """
                UPDATE ai_learning_data 
                SET user_feedback = $1, user_satisfaction = $2
                WHERE conversation_id = $3
                """
                await self.db.execute(query, rating, rating / 5.0, conversation_id)
            
            # Analyze feedback for patterns
            if feedback_text:
                await self.analyze_feedback_patterns(
                    feedback_text, rating, feedback_type
                )
            
            logger.debug(f"Processed feedback: rating={rating}, type={feedback_type}")
            
        except Exception as e:
            logger.error(f"Failed to process feedback: {e}")
    
    async def analyze_interaction_quality(
        self,
        input_message: str,
        ai_response: str,
        context: Dict[str, Any]
    ) -> float:
        """Analyze the quality of an AI interaction"""
        try:
            quality_factors = []
            
            # Response length appropriateness
            response_length = len(ai_response)
            if 50 <= response_length <= 2000:
                quality_factors.append(0.8)
            elif response_length < 50:
                quality_factors.append(0.4)
            else:
                quality_factors.append(0.6)
            
            # Code presence (if relevant)
            if "```" in ai_response:
                quality_factors.append(0.9)
            
            # Helpful keywords
            helpful_keywords = [
                "example", "here's how", "you can", "try this",
                "solution", "approach", "consider"
            ]
            if any(keyword in ai_response.lower() for keyword in helpful_keywords):
                quality_factors.append(0.8)
            
            # Context relevance
            if context.get("project_type") and context["project_type"] in ai_response.lower():
                quality_factors.append(0.7)
            
            return np.mean(quality_factors) if quality_factors else 0.5
            
        except Exception as e:
            logger.error(f"Failed to analyze interaction quality: {e}")
            return 0.5
    
    async def extract_patterns(
        self,
        input_message: str,
        ai_response: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract learning patterns from interaction"""
        try:
            patterns = {
                "input_keywords": [],
                "response_type": "general",
                "code_generated": False,
                "platform_mentioned": None,
                "complexity_level": "medium"
            }
            
            # Extract keywords from input
            input_lower = input_message.lower()
            keywords = ["android", "ios", "mobile", "app", "kotlin", "swift", "react", "flutter"]
            patterns["input_keywords"] = [kw for kw in keywords if kw in input_lower]
            
            # Determine response type
            if "```" in ai_response:
                patterns["response_type"] = "code"
                patterns["code_generated"] = True
            elif "?" in ai_response:
                patterns["response_type"] = "question"
            elif len(ai_response) > 500:
                patterns["response_type"] = "detailed_explanation"
            
            # Platform detection
            if "android" in input_lower or "kotlin" in input_lower:
                patterns["platform_mentioned"] = "android"
            elif "ios" in input_lower or "swift" in input_lower:
                patterns["platform_mentioned"] = "ios"
            
            # Complexity assessment
            complex_keywords = ["architecture", "design pattern", "optimization", "performance"]
            if any(kw in input_lower for kw in complex_keywords):
                patterns["complexity_level"] = "high"
            elif len(input_message) < 50:
                patterns["complexity_level"] = "low"
            
            return patterns
            
        except Exception as e:
            logger.error(f"Failed to extract patterns: {e}")
            return {}
    
    async def analyze_generated_code(self, code: str, platform: str) -> float:
        """Analyze quality of generated code"""
        try:
            quality_score = 0.5  # Base score
            
            # Code structure analysis
            if platform.lower() == "android":
                if "class" in code and "fun" in code:
                    quality_score += 0.2
                if "import" in code:
                    quality_score += 0.1
                if "override" in code:
                    quality_score += 0.1
            elif platform.lower() == "ios":
                if "class" in code or "struct" in code:
                    quality_score += 0.2
                if "import" in code:
                    quality_score += 0.1
                if "func" in code:
                    quality_score += 0.1
            
            # General code quality indicators
            if len(code) > 100:  # Substantial code
                quality_score += 0.1
            if "// " in code or "/* " in code:  # Comments
                quality_score += 0.1
            
            return min(quality_score, 1.0)
            
        except Exception as e:
            logger.error(f"Failed to analyze generated code: {e}")
            return 0.5
    
    async def extract_generation_patterns(
        self,
        requirements: str,
        generated_code: str,
        platform: str
    ) -> Dict[str, Any]:
        """Extract patterns from app generation"""
        try:
            patterns = {
                "requirements_complexity": "medium",
                "code_structure": [],
                "platform": platform,
                "features_implemented": [],
                "architecture_pattern": "unknown"
            }
            
            # Analyze requirements complexity
            req_lower = requirements.lower()
            complex_features = ["database", "api", "authentication", "payment", "real-time"]
            if any(feature in req_lower for feature in complex_features):
                patterns["requirements_complexity"] = "high"
            elif len(requirements) < 100:
                patterns["requirements_complexity"] = "low"
            
            # Detect architecture patterns
            if "mvvm" in generated_code.lower():
                patterns["architecture_pattern"] = "mvvm"
            elif "mvc" in generated_code.lower():
                patterns["architecture_pattern"] = "mvc"
            elif "repository" in generated_code.lower():
                patterns["architecture_pattern"] = "repository"
            
            return patterns
            
        except Exception as e:
            logger.error(f"Failed to extract generation patterns: {e}")
            return {}
    
    async def analyze_feedback_patterns(
        self,
        feedback_text: str,
        rating: int,
        feedback_type: str
    ):
        """Analyze feedback for learning patterns"""
        try:
            # Extract sentiment and keywords from feedback
            feedback_lower = feedback_text.lower()
            
            positive_keywords = ["good", "great", "helpful", "perfect", "excellent"]
            negative_keywords = ["bad", "wrong", "unhelpful", "confusing", "error"]
            
            sentiment = "neutral"
            if any(kw in feedback_lower for kw in positive_keywords):
                sentiment = "positive"
            elif any(kw in feedback_lower for kw in negative_keywords):
                sentiment = "negative"
            
            # Store pattern for future reference
            pattern_key = f"{feedback_type}_{sentiment}"
            if pattern_key not in self.pattern_cache:
                self.pattern_cache[pattern_key] = []
            
            self.pattern_cache[pattern_key].append({
                "text": feedback_text,
                "rating": rating,
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Failed to analyze feedback patterns: {e}")
    
    async def continuous_learning_loop(self):
        """Background task for continuous learning"""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                
                # Perform learning cycle
                await self.perform_learning_cycle()
                
                self.metrics["learning_cycles"] += 1
                
            except Exception as e:
                logger.error(f"Learning loop error: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def perform_learning_cycle(self):
        """Perform a learning cycle to improve AI"""
        try:
            logger.info("🧠 Starting learning cycle...")
            
            # Analyze recent patterns
            patterns = await self.analyze_recent_patterns()
            
            # Update model preferences based on feedback
            await self.update_model_preferences()
            
            # Clean old data
            await self.cleanup_old_data()
            
            logger.info("✅ Learning cycle completed")
            
        except Exception as e:
            logger.error(f"Learning cycle failed: {e}")
    
    async def analyze_recent_patterns(self) -> Dict[str, Any]:
        """Analyze recent interaction patterns"""
        try:
            # Get recent high-rated interactions
            recent_data = [
                item for item in self.learning_data
                if item.get("user_feedback", 0) >= self.feedback_threshold
                and item["created_at"] > datetime.now() - timedelta(days=7)
            ]
            
            if not recent_data:
                return {}
            
            # Extract successful patterns
            successful_patterns = {}
            for item in recent_data:
                patterns = item.get("learning_patterns", {})
                if isinstance(patterns, str):
                    patterns = json.loads(patterns)
                
                for key, value in patterns.items():
                    if key not in successful_patterns:
                        successful_patterns[key] = []
                    successful_patterns[key].append(value)
            
            self.metrics["patterns_discovered"] = len(successful_patterns)
            
            return successful_patterns
            
        except Exception as e:
            logger.error(f"Failed to analyze recent patterns: {e}")
            return {}
    
    async def update_model_preferences(self):
        """Update model selection preferences based on learning"""
        try:
            # This would update model selection logic based on performance
            # For now, just log the intent
            logger.debug("Model preferences updated based on learning data")
            
        except Exception as e:
            logger.error(f"Failed to update model preferences: {e}")
    
    async def cleanup_old_data(self):
        """Clean up old learning data"""
        try:
            # Remove data older than 90 days
            cutoff_date = datetime.now() - timedelta(days=90)
            
            query = """
            DELETE FROM ai_learning_data 
            WHERE created_at < $1
            """
            
            await self.db.execute(query, cutoff_date)
            
            # Clean in-memory cache
            self.learning_data = [
                item for item in self.learning_data
                if item["created_at"] > cutoff_date
            ]
            
            logger.debug("Old learning data cleaned up")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")
    
    async def save_learning_state(self):
        """Save current learning state"""
        try:
            # Save pattern cache and metrics to database or file
            # This ensures learning state persists across restarts
            logger.debug("Learning state saved")
            
        except Exception as e:
            logger.error(f"Failed to save learning state: {e}")
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get learning engine metrics"""
        return {
            "learning_metrics": self.metrics,
            "pattern_cache_size": len(self.pattern_cache),
            "learning_data_points": len(self.learning_data),
            "last_learning_cycle": datetime.now().isoformat()
        }
