"""
WebSocket Manager - Real-time communication between frontend and AI engine
"""

import asyncio
import json
import logging
from typing import Dict, List, Set, Optional, Any
from datetime import datetime
import uuid

from fastapi import WebSocket, WebSocketDisconnect
from utils.logger import setup_logger

logger = setup_logger(__name__)


class WebSocketManager:
    """Manages WebSocket connections and real-time communication"""
    
    def __init__(self):
        # Active connections by conversation ID
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        
        # Connection metadata
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}
        
        # Message queue for offline users
        self.message_queue: Dict[str, List[Dict[str, Any]]] = {}
        
        # Statistics
        self.stats = {
            "total_connections": 0,
            "active_connections": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "connection_errors": 0
        }
        
        logger.info("WebSocket Manager initialized")
    
    async def add_connection(
        self, 
        conversation_id: str, 
        websocket: WebSocket,
        user_id: Optional[str] = None
    ):
        """Add a new WebSocket connection"""
        try:
            await websocket.accept()
            
            # Add to active connections
            if conversation_id not in self.active_connections:
                self.active_connections[conversation_id] = set()
            
            self.active_connections[conversation_id].add(websocket)
            
            # Store connection metadata
            self.connection_metadata[websocket] = {
                "conversation_id": conversation_id,
                "user_id": user_id,
                "connected_at": datetime.now(),
                "last_activity": datetime.now(),
                "connection_id": str(uuid.uuid4())
            }
            
            # Update statistics
            self.stats["total_connections"] += 1
            self.stats["active_connections"] = len(self.connection_metadata)
            
            logger.info(f"WebSocket connected: conversation={conversation_id}, user={user_id}")
            
            # Send queued messages if any
            await self.send_queued_messages(conversation_id, websocket)
            
            # Notify about successful connection
            await self.send_to_connection(websocket, {
                "type": "connection_established",
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat(),
                "connection_id": self.connection_metadata[websocket]["connection_id"]
            })
            
        except Exception as e:
            logger.error(f"Failed to add WebSocket connection: {e}")
            self.stats["connection_errors"] += 1
            raise
    
    async def remove_connection(self, conversation_id: str, websocket: WebSocket):
        """Remove a WebSocket connection"""
        try:
            # Remove from active connections
            if conversation_id in self.active_connections:
                self.active_connections[conversation_id].discard(websocket)
                
                # Clean up empty conversation groups
                if not self.active_connections[conversation_id]:
                    del self.active_connections[conversation_id]
            
            # Remove metadata
            if websocket in self.connection_metadata:
                connection_info = self.connection_metadata[websocket]
                del self.connection_metadata[websocket]
                
                logger.info(f"WebSocket disconnected: conversation={conversation_id}, "
                          f"duration={datetime.now() - connection_info['connected_at']}")
            
            # Update statistics
            self.stats["active_connections"] = len(self.connection_metadata)
            
        except Exception as e:
            logger.error(f"Failed to remove WebSocket connection: {e}")
    
    async def send_to_conversation(
        self, 
        conversation_id: str, 
        message: Dict[str, Any],
        exclude_websocket: Optional[WebSocket] = None
    ):
        """Send message to all connections in a conversation"""
        try:
            if conversation_id not in self.active_connections:
                # Queue message for when connections are available
                await self.queue_message(conversation_id, message)
                return
            
            connections = self.active_connections[conversation_id].copy()
            disconnected_connections = []
            
            for websocket in connections:
                if websocket == exclude_websocket:
                    continue
                
                try:
                    await self.send_to_connection(websocket, message)
                except WebSocketDisconnect:
                    disconnected_connections.append(websocket)
                except Exception as e:
                    logger.error(f"Failed to send message to WebSocket: {e}")
                    disconnected_connections.append(websocket)
            
            # Clean up disconnected connections
            for websocket in disconnected_connections:
                await self.remove_connection(conversation_id, websocket)
            
            self.stats["messages_sent"] += len(connections) - len(disconnected_connections)
            
        except Exception as e:
            logger.error(f"Failed to send message to conversation {conversation_id}: {e}")
    
    async def send_to_connection(self, websocket: WebSocket, message: Dict[str, Any]):
        """Send message to a specific WebSocket connection"""
        try:
            # Add timestamp if not present
            if "timestamp" not in message:
                message["timestamp"] = datetime.now().isoformat()
            
            # Update last activity
            if websocket in self.connection_metadata:
                self.connection_metadata[websocket]["last_activity"] = datetime.now()
            
            await websocket.send_text(json.dumps(message))
            
        except WebSocketDisconnect:
            logger.info("WebSocket disconnected during send")
            raise
        except Exception as e:
            logger.error(f"Failed to send message to WebSocket: {e}")
            raise
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """Broadcast message to all active connections"""
        try:
            all_connections = []
            for connections in self.active_connections.values():
                all_connections.extend(connections)
            
            disconnected_connections = []
            
            for websocket in all_connections:
                try:
                    await self.send_to_connection(websocket, message)
                except (WebSocketDisconnect, Exception):
                    disconnected_connections.append(websocket)
            
            # Clean up disconnected connections
            for websocket in disconnected_connections:
                for conversation_id, connections in self.active_connections.items():
                    if websocket in connections:
                        await self.remove_connection(conversation_id, websocket)
                        break
            
            logger.info(f"Broadcasted message to {len(all_connections) - len(disconnected_connections)} connections")
            
        except Exception as e:
            logger.error(f"Failed to broadcast message: {e}")
    
    async def queue_message(self, conversation_id: str, message: Dict[str, Any]):
        """Queue message for offline users"""
        try:
            if conversation_id not in self.message_queue:
                self.message_queue[conversation_id] = []
            
            # Add timestamp and queue info
            message["queued_at"] = datetime.now().isoformat()
            message["queue_id"] = str(uuid.uuid4())
            
            self.message_queue[conversation_id].append(message)
            
            # Limit queue size (keep last 100 messages)
            if len(self.message_queue[conversation_id]) > 100:
                self.message_queue[conversation_id] = self.message_queue[conversation_id][-100:]
            
            logger.debug(f"Message queued for conversation {conversation_id}")
            
        except Exception as e:
            logger.error(f"Failed to queue message: {e}")
    
    async def send_queued_messages(self, conversation_id: str, websocket: WebSocket):
        """Send queued messages to a newly connected client"""
        try:
            if conversation_id not in self.message_queue:
                return
            
            queued_messages = self.message_queue[conversation_id].copy()
            
            for message in queued_messages:
                # Mark as queued message
                message["type"] = f"queued_{message.get('type', 'message')}"
                await self.send_to_connection(websocket, message)
            
            # Clear queue after sending
            self.message_queue[conversation_id] = []
            
            logger.info(f"Sent {len(queued_messages)} queued messages to conversation {conversation_id}")
            
        except Exception as e:
            logger.error(f"Failed to send queued messages: {e}")
    
    async def handle_ai_response(
        self, 
        conversation_id: str, 
        ai_response: str,
        context: Dict[str, Any] = None
    ):
        """Handle AI response and broadcast to conversation"""
        try:
            message = {
                "type": "ai_response",
                "conversation_id": conversation_id,
                "message": ai_response,
                "context": context or {},
                "timestamp": datetime.now().isoformat()
            }
            
            await self.send_to_conversation(conversation_id, message)
            
        except Exception as e:
            logger.error(f"Failed to handle AI response: {e}")
    
    async def handle_code_generation(
        self, 
        conversation_id: str, 
        generated_code: Dict[str, str],
        generation_info: Dict[str, Any] = None
    ):
        """Handle code generation and broadcast to conversation"""
        try:
            message = {
                "type": "code_generated",
                "conversation_id": conversation_id,
                "generated_code": generated_code,
                "generation_info": generation_info or {},
                "timestamp": datetime.now().isoformat()
            }
            
            await self.send_to_conversation(conversation_id, message)
            
        except Exception as e:
            logger.error(f"Failed to handle code generation: {e}")
    
    async def handle_build_status(
        self, 
        conversation_id: str, 
        build_status: str,
        build_info: Dict[str, Any] = None
    ):
        """Handle build status updates"""
        try:
            message = {
                "type": "build_status",
                "conversation_id": conversation_id,
                "status": build_status,
                "build_info": build_info or {},
                "timestamp": datetime.now().isoformat()
            }
            
            await self.send_to_conversation(conversation_id, message)
            
        except Exception as e:
            logger.error(f"Failed to handle build status: {e}")
    
    async def handle_system_notification(
        self, 
        notification_type: str, 
        message: str,
        target_conversations: Optional[List[str]] = None
    ):
        """Handle system-wide notifications"""
        try:
            notification = {
                "type": "system_notification",
                "notification_type": notification_type,
                "message": message,
                "timestamp": datetime.now().isoformat()
            }
            
            if target_conversations:
                for conversation_id in target_conversations:
                    await self.send_to_conversation(conversation_id, notification)
            else:
                await self.broadcast_to_all(notification)
            
        except Exception as e:
            logger.error(f"Failed to handle system notification: {e}")
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get WebSocket connection statistics"""
        try:
            active_conversations = len(self.active_connections)
            total_active_connections = sum(len(connections) for connections in self.active_connections.values())
            
            # Calculate average connection duration
            now = datetime.now()
            connection_durations = [
                (now - metadata["connected_at"]).total_seconds()
                for metadata in self.connection_metadata.values()
            ]
            avg_duration = sum(connection_durations) / len(connection_durations) if connection_durations else 0
            
            return {
                **self.stats,
                "active_conversations": active_conversations,
                "total_active_connections": total_active_connections,
                "average_connection_duration": avg_duration,
                "queued_messages": sum(len(queue) for queue in self.message_queue.values()),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get connection stats: {e}")
            return self.stats
    
    async def cleanup_inactive_connections(self, timeout_seconds: int = 300):
        """Clean up inactive connections (5 minutes default)"""
        try:
            now = datetime.now()
            inactive_connections = []
            
            for websocket, metadata in self.connection_metadata.items():
                last_activity = metadata["last_activity"]
                if (now - last_activity).total_seconds() > timeout_seconds:
                    inactive_connections.append((websocket, metadata["conversation_id"]))
            
            for websocket, conversation_id in inactive_connections:
                logger.info(f"Cleaning up inactive connection: {conversation_id}")
                await self.remove_connection(conversation_id, websocket)
                try:
                    await websocket.close()
                except:
                    pass  # Connection might already be closed
            
            if inactive_connections:
                logger.info(f"Cleaned up {len(inactive_connections)} inactive connections")
            
        except Exception as e:
            logger.error(f"Failed to cleanup inactive connections: {e}")
    
    async def start_cleanup_task(self):
        """Start background task for cleaning up inactive connections"""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(60)  # Check every minute
                    await self.cleanup_inactive_connections()
                except Exception as e:
                    logger.error(f"Cleanup task error: {e}")
        
        asyncio.create_task(cleanup_loop())
