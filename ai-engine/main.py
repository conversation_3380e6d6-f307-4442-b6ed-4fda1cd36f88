"""
AndroidWeb Enterprise AI Engine
FastAPI-based AI service with Ollama integration and continuous learning
"""

import asyncio
import logging
import os
from contextlib import asynccontextmanager
from typing import Dict, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from core.ai_manager import AIManager
from core.learning_engine import LearningEngine
from core.mobile_generator import MobileGenerator
from core.websocket_manager import WebSocketManager
from models.requests import (
    ChatRequest,
    GenerateAppRequest,
    FeedbackRequest,
    AnalyzeCodeRequest
)
from models.responses import (
    ChatResponse,
    AppGenerationResponse,
    HealthResponse,
    MetricsResponse
)
from utils.auth import verify_token
from utils.database import get_database
from utils.logger import setup_logger

# Setup logging
logger = setup_logger(__name__)

# Global managers
ai_manager: Optional[AIManager] = None
learning_engine: Optional[LearningEngine] = None
mobile_generator: Optional[MobileGenerator] = None
websocket_manager: Optional[WebSocketManager] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global ai_manager, learning_engine, mobile_generator, websocket_manager
    
    logger.info("🚀 Starting AndroidWeb AI Engine...")
    
    try:
        # Initialize core components
        ai_manager = AIManager()
        learning_engine = LearningEngine()
        mobile_generator = MobileGenerator()
        websocket_manager = WebSocketManager()
        
        # Start background services
        await ai_manager.initialize()
        await learning_engine.initialize()
        await mobile_generator.initialize()
        
        logger.info("✅ AI Engine initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize AI Engine: {e}")
        raise
    finally:
        # Cleanup
        logger.info("🔄 Shutting down AI Engine...")
        if ai_manager:
            await ai_manager.cleanup()
        if learning_engine:
            await learning_engine.cleanup()
        if mobile_generator:
            await mobile_generator.cleanup()
        logger.info("✅ AI Engine shutdown complete")


# Create FastAPI app
app = FastAPI(
    title="AndroidWeb Enterprise AI Engine",
    description="AI-powered mobile app generation with continuous learning",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3003"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Check Ollama connection
        ollama_status = await ai_manager.check_ollama_health() if ai_manager else False
        
        # Check database connection
        db_status = await get_database().check_health()
        
        # Check learning engine
        learning_status = learning_engine.is_healthy() if learning_engine else False
        
        return HealthResponse(
            status="healthy" if all([ollama_status, db_status, learning_status]) else "unhealthy",
            ollama_connected=ollama_status,
            database_connected=db_status,
            learning_engine_active=learning_status,
            timestamp=asyncio.get_event_loop().time()
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            ollama_connected=False,
            database_connected=False,
            learning_engine_active=False,
            timestamp=asyncio.get_event_loop().time(),
            error=str(e)
        )


# Metrics endpoint
@app.get("/metrics", response_model=MetricsResponse)
async def get_metrics(user=Depends(verify_token)):
    """Get AI engine metrics"""
    try:
        if not ai_manager or not learning_engine:
            raise HTTPException(status_code=503, detail="AI Engine not initialized")
        
        ai_metrics = await ai_manager.get_metrics()
        learning_metrics = await learning_engine.get_metrics()
        
        return MetricsResponse(
            ai_metrics=ai_metrics,
            learning_metrics=learning_metrics,
            timestamp=asyncio.get_event_loop().time()
        )
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Chat endpoint
@app.post("/chat", response_model=ChatResponse)
async def chat_with_ai(request: ChatRequest, user=Depends(verify_token)):
    """Chat with AI for app development assistance"""
    try:
        if not ai_manager:
            raise HTTPException(status_code=503, detail="AI Manager not initialized")
        
        # Process chat request
        response = await ai_manager.process_chat(
            message=request.message,
            context=request.context,
            user_id=user["user_id"],
            conversation_id=request.conversation_id
        )
        
        # Learn from interaction
        if learning_engine:
            await learning_engine.record_interaction(
                user_id=user["user_id"],
                conversation_id=request.conversation_id,
                input_message=request.message,
                ai_response=response.message,
                context=request.context
            )
        
        return response
        
    except Exception as e:
        logger.error(f"Chat processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# App generation endpoint
@app.post("/generate-app", response_model=AppGenerationResponse)
async def generate_mobile_app(request: GenerateAppRequest, user=Depends(verify_token)):
    """Generate mobile application based on requirements"""
    try:
        if not mobile_generator:
            raise HTTPException(status_code=503, detail="Mobile Generator not initialized")
        
        # Generate app
        response = await mobile_generator.generate_app(
            requirements=request.requirements,
            platform=request.platform,
            template_type=request.template_type,
            user_id=user["user_id"],
            project_id=request.project_id
        )
        
        # Learn from generation
        if learning_engine:
            await learning_engine.record_generation(
                user_id=user["user_id"],
                project_id=request.project_id,
                requirements=request.requirements,
                generated_code=response.source_code,
                platform=request.platform
            )
        
        return response
        
    except Exception as e:
        logger.error(f"App generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Feedback endpoint
@app.post("/feedback")
async def submit_feedback(request: FeedbackRequest, user=Depends(verify_token)):
    """Submit feedback for AI learning"""
    try:
        if not learning_engine:
            raise HTTPException(status_code=503, detail="Learning Engine not initialized")
        
        await learning_engine.process_feedback(
            user_id=user["user_id"],
            conversation_id=request.conversation_id,
            project_id=request.project_id,
            rating=request.rating,
            feedback_text=request.feedback_text,
            feedback_type=request.feedback_type
        )
        
        return {"status": "success", "message": "Feedback recorded successfully"}
        
    except Exception as e:
        logger.error(f"Feedback processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Code analysis endpoint
@app.post("/analyze-code")
async def analyze_code(request: AnalyzeCodeRequest, user=Depends(verify_token)):
    """Analyze code quality and provide suggestions"""
    try:
        if not ai_manager:
            raise HTTPException(status_code=503, detail="AI Manager not initialized")
        
        analysis = await ai_manager.analyze_code(
            code=request.code,
            language=request.language,
            context=request.context
        )
        
        return analysis
        
    except Exception as e:
        logger.error(f"Code analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# WebSocket endpoint for real-time chat
@app.websocket("/ws/chat/{conversation_id}")
async def websocket_chat(websocket: WebSocket, conversation_id: str):
    """WebSocket endpoint for real-time chat"""
    try:
        await websocket.accept()
        
        if not websocket_manager:
            await websocket.close(code=1011, reason="WebSocket Manager not initialized")
            return
        
        # Add connection to manager
        await websocket_manager.add_connection(conversation_id, websocket)
        
        try:
            while True:
                # Receive message
                data = await websocket.receive_json()
                
                # Process message
                if ai_manager:
                    response = await ai_manager.process_chat(
                        message=data.get("message", ""),
                        context=data.get("context", {}),
                        user_id=data.get("user_id"),
                        conversation_id=conversation_id
                    )
                    
                    # Send response
                    await websocket.send_json({
                        "type": "ai_response",
                        "message": response.message,
                        "context": response.context,
                        "timestamp": response.timestamp
                    })
                
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected for conversation {conversation_id}")
        finally:
            await websocket_manager.remove_connection(conversation_id, websocket)
            
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await websocket.close(code=1011, reason=str(e))


# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail, "status_code": exc.status_code}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "status_code": 500}
    )


if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Run server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("AI_ENGINE_PORT", 8000)),
        reload=os.getenv("ENVIRONMENT") == "development",
        log_level="info"
    )
