"""
Request models for AI Engine API
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class Platform(str, Enum):
    """Supported mobile platforms"""
    ANDROID = "android"
    IOS = "ios"
    CROSS_PLATFORM = "cross_platform"


class FeedbackType(str, Enum):
    """Types of user feedback"""
    GENERAL = "general"
    CODE_QUALITY = "code_quality"
    RESPONSE_ACCURACY = "response_accuracy"
    USER_EXPERIENCE = "user_experience"


class ChatRequest(BaseModel):
    """Request model for chat endpoint"""
    message: str = Field(..., description="User message to AI")
    context: Dict[str, Any] = Field(default_factory=dict, description="Conversation context")
    conversation_id: Optional[str] = Field(None, description="Conversation ID for context")
    project_id: Optional[str] = Field(None, description="Associated project ID")
    
    class Config:
        schema_extra = {
            "example": {
                "message": "How do I create a login screen for Android?",
                "context": {
                    "project_type": "android",
                    "language": "kotlin",
                    "ui_framework": "jetpack_compose"
                },
                "conversation_id": "conv_123",
                "project_id": "proj_456"
            }
        }


class GenerateAppRequest(BaseModel):
    """Request model for app generation endpoint"""
    requirements: str = Field(..., description="App requirements description")
    platform: Platform = Field(..., description="Target platform")
    template_type: Optional[str] = Field(None, description="Template type to use")
    project_id: str = Field(..., description="Project ID")
    additional_context: Dict[str, Any] = Field(default_factory=dict, description="Additional context")
    
    class Config:
        schema_extra = {
            "example": {
                "requirements": "Create a todo app with user authentication and cloud sync",
                "platform": "android",
                "template_type": "productivity",
                "project_id": "proj_789",
                "additional_context": {
                    "ui_style": "material_design",
                    "backend": "firebase"
                }
            }
        }


class FeedbackRequest(BaseModel):
    """Request model for feedback endpoint"""
    conversation_id: Optional[str] = Field(None, description="Conversation ID")
    project_id: Optional[str] = Field(None, description="Project ID")
    rating: int = Field(..., ge=1, le=5, description="Rating from 1 to 5")
    feedback_text: Optional[str] = Field(None, description="Detailed feedback text")
    feedback_type: FeedbackType = Field(FeedbackType.GENERAL, description="Type of feedback")
    
    class Config:
        schema_extra = {
            "example": {
                "conversation_id": "conv_123",
                "project_id": "proj_456",
                "rating": 4,
                "feedback_text": "The code was helpful but could use more comments",
                "feedback_type": "code_quality"
            }
        }


class AnalyzeCodeRequest(BaseModel):
    """Request model for code analysis endpoint"""
    code: str = Field(..., description="Code to analyze")
    language: str = Field(..., description="Programming language")
    context: Dict[str, Any] = Field(default_factory=dict, description="Analysis context")
    
    class Config:
        schema_extra = {
            "example": {
                "code": "class MainActivity : AppCompatActivity() { ... }",
                "language": "kotlin",
                "context": {
                    "platform": "android",
                    "framework": "android_sdk"
                }
            }
        }


class ModelRequest(BaseModel):
    """Request model for model management"""
    model_name: str = Field(..., description="Name of the model")
    action: str = Field(..., description="Action to perform (pull, remove, list)")
    
    class Config:
        schema_extra = {
            "example": {
                "model_name": "mistral:7b",
                "action": "pull"
            }
        }


class LearningDataRequest(BaseModel):
    """Request model for learning data submission"""
    interaction_data: Dict[str, Any] = Field(..., description="Interaction data for learning")
    user_id: str = Field(..., description="User ID")
    session_id: Optional[str] = Field(None, description="Session ID")
    
    class Config:
        schema_extra = {
            "example": {
                "interaction_data": {
                    "input": "How to create a button?",
                    "output": "Here's how to create a button...",
                    "quality_score": 0.85,
                    "user_satisfaction": 4
                },
                "user_id": "user_123",
                "session_id": "session_456"
            }
        }


class BatchProcessRequest(BaseModel):
    """Request model for batch processing"""
    requests: List[Dict[str, Any]] = Field(..., description="List of requests to process")
    batch_type: str = Field(..., description="Type of batch processing")
    priority: int = Field(default=1, ge=1, le=5, description="Processing priority")
    
    class Config:
        schema_extra = {
            "example": {
                "requests": [
                    {"message": "Create login screen", "context": {"platform": "android"}},
                    {"message": "Add validation", "context": {"platform": "android"}}
                ],
                "batch_type": "chat_batch",
                "priority": 2
            }
        }


class ConfigUpdateRequest(BaseModel):
    """Request model for configuration updates"""
    config_section: str = Field(..., description="Configuration section to update")
    config_data: Dict[str, Any] = Field(..., description="Configuration data")
    
    class Config:
        schema_extra = {
            "example": {
                "config_section": "ai_models",
                "config_data": {
                    "default_model": "mobile-dev-specialist",
                    "temperature": 0.7,
                    "max_tokens": 2048
                }
            }
        }


class HealthCheckRequest(BaseModel):
    """Request model for health check with details"""
    include_metrics: bool = Field(default=False, description="Include performance metrics")
    check_models: bool = Field(default=True, description="Check model availability")
    
    class Config:
        schema_extra = {
            "example": {
                "include_metrics": True,
                "check_models": True
            }
        }
