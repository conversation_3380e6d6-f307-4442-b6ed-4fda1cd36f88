"""
Response models for AI Engine API
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime


class ChatResponse(BaseModel):
    """Response model for chat endpoint"""
    message: str = Field(..., description="AI response message")
    context: Dict[str, Any] = Field(default_factory=dict, description="Updated context")
    model_used: str = Field(..., description="AI model used for response")
    response_time: float = Field(..., description="Response time in seconds")
    timestamp: str = Field(..., description="Response timestamp")
    confidence_score: Optional[float] = Field(None, description="Confidence score of response")
    suggestions: List[str] = Field(default_factory=list, description="Follow-up suggestions")
    error: Optional[str] = Field(None, description="Error message if any")
    
    class Config:
        schema_extra = {
            "example": {
                "message": "Here's how to create a login screen in Android with Kotlin...",
                "context": {"platform": "android", "language": "kotlin"},
                "model_used": "mobile-dev-specialist",
                "response_time": 1.23,
                "timestamp": "2024-01-15T10:30:00Z",
                "confidence_score": 0.92,
                "suggestions": ["Add input validation", "Implement remember me feature"],
                "error": None
            }
        }


class AppGenerationResponse(BaseModel):
    """Response model for app generation endpoint"""
    project_id: str = Field(..., description="Project ID")
    source_code: Dict[str, str] = Field(..., description="Generated source code files")
    build_config: Dict[str, Any] = Field(..., description="Build configuration")
    assets: Dict[str, str] = Field(default_factory=dict, description="Generated assets")
    documentation: str = Field(..., description="Generated documentation")
    generation_time: float = Field(..., description="Generation time in seconds")
    quality_score: float = Field(..., description="Estimated code quality score")
    platform: str = Field(..., description="Target platform")
    template_used: Optional[str] = Field(None, description="Template used")
    warnings: List[str] = Field(default_factory=list, description="Generation warnings")
    next_steps: List[str] = Field(default_factory=list, description="Suggested next steps")
    
    class Config:
        schema_extra = {
            "example": {
                "project_id": "proj_789",
                "source_code": {
                    "MainActivity.kt": "class MainActivity : AppCompatActivity() { ... }",
                    "activity_main.xml": "<?xml version=\"1.0\" encoding=\"utf-8\"?> ..."
                },
                "build_config": {
                    "gradle_version": "8.0",
                    "compile_sdk": 34,
                    "min_sdk": 24
                },
                "assets": {
                    "app_icon.png": "base64_encoded_icon",
                    "splash_screen.png": "base64_encoded_splash"
                },
                "documentation": "# Todo App Documentation\n\nThis app provides...",
                "generation_time": 5.67,
                "quality_score": 0.88,
                "platform": "android",
                "template_used": "productivity",
                "warnings": ["Consider adding input validation"],
                "next_steps": ["Test on device", "Add unit tests", "Configure CI/CD"]
            }
        }


class HealthResponse(BaseModel):
    """Response model for health check endpoint"""
    status: str = Field(..., description="Overall health status")
    ollama_connected: bool = Field(..., description="Ollama connection status")
    database_connected: bool = Field(..., description="Database connection status")
    learning_engine_active: bool = Field(..., description="Learning engine status")
    timestamp: float = Field(..., description="Health check timestamp")
    error: Optional[str] = Field(None, description="Error message if unhealthy")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional health details")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "healthy",
                "ollama_connected": True,
                "database_connected": True,
                "learning_engine_active": True,
                "timestamp": **********.0,
                "error": None,
                "details": {
                    "uptime": "2 days, 3 hours",
                    "memory_usage": "2.1GB",
                    "active_models": 3
                }
            }
        }


class MetricsResponse(BaseModel):
    """Response model for metrics endpoint"""
    ai_metrics: Dict[str, Any] = Field(..., description="AI engine metrics")
    learning_metrics: Dict[str, Any] = Field(..., description="Learning engine metrics")
    timestamp: float = Field(..., description="Metrics timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "ai_metrics": {
                    "total_requests": 1250,
                    "successful_requests": 1198,
                    "failed_requests": 52,
                    "average_response_time": 1.45,
                    "model_usage": {
                        "mobile-dev-specialist": 800,
                        "codellama:13b": 300,
                        "mistral:7b": 150
                    }
                },
                "learning_metrics": {
                    "total_interactions": 1250,
                    "positive_feedback_count": 980,
                    "negative_feedback_count": 45,
                    "average_rating": 4.2,
                    "learning_cycles": 24,
                    "patterns_discovered": 156
                },
                "timestamp": **********.0
            }
        }


class CodeAnalysisResponse(BaseModel):
    """Response model for code analysis endpoint"""
    quality_score: float = Field(..., description="Overall code quality score (0-1)")
    issues: List[Dict[str, Any]] = Field(default_factory=list, description="Identified issues")
    suggestions: List[Dict[str, Any]] = Field(default_factory=list, description="Improvement suggestions")
    complexity_metrics: Dict[str, Any] = Field(default_factory=dict, description="Code complexity metrics")
    security_analysis: Dict[str, Any] = Field(default_factory=dict, description="Security analysis results")
    performance_insights: List[str] = Field(default_factory=list, description="Performance insights")
    best_practices: List[str] = Field(default_factory=list, description="Best practices recommendations")
    analysis_time: float = Field(..., description="Analysis time in seconds")
    
    class Config:
        schema_extra = {
            "example": {
                "quality_score": 0.85,
                "issues": [
                    {
                        "type": "warning",
                        "line": 15,
                        "message": "Variable 'result' is never used",
                        "severity": "low"
                    }
                ],
                "suggestions": [
                    {
                        "type": "improvement",
                        "description": "Consider using data classes for better structure",
                        "impact": "medium"
                    }
                ],
                "complexity_metrics": {
                    "cyclomatic_complexity": 3,
                    "lines_of_code": 120,
                    "maintainability_index": 78
                },
                "security_analysis": {
                    "vulnerabilities_found": 0,
                    "security_score": 0.95,
                    "recommendations": ["Add input validation"]
                },
                "performance_insights": [
                    "Consider using lazy initialization for heavy objects"
                ],
                "best_practices": [
                    "Add documentation comments",
                    "Use consistent naming conventions"
                ],
                "analysis_time": 0.89
            }
        }


class ModelListResponse(BaseModel):
    """Response model for model list endpoint"""
    models: List[Dict[str, Any]] = Field(..., description="Available models")
    total_count: int = Field(..., description="Total number of models")
    
    class Config:
        schema_extra = {
            "example": {
                "models": [
                    {
                        "name": "mobile-dev-specialist",
                        "size": "4.1GB",
                        "modified": "2024-01-15T10:30:00Z",
                        "status": "ready"
                    },
                    {
                        "name": "codellama:13b",
                        "size": "7.3GB",
                        "modified": "2024-01-14T15:20:00Z",
                        "status": "ready"
                    }
                ],
                "total_count": 2
            }
        }


class BatchProcessResponse(BaseModel):
    """Response model for batch processing endpoint"""
    batch_id: str = Field(..., description="Batch processing ID")
    status: str = Field(..., description="Batch processing status")
    total_requests: int = Field(..., description="Total number of requests")
    completed_requests: int = Field(..., description="Number of completed requests")
    failed_requests: int = Field(..., description="Number of failed requests")
    results: List[Dict[str, Any]] = Field(default_factory=list, description="Processing results")
    processing_time: float = Field(..., description="Total processing time")
    
    class Config:
        schema_extra = {
            "example": {
                "batch_id": "batch_123",
                "status": "completed",
                "total_requests": 10,
                "completed_requests": 9,
                "failed_requests": 1,
                "results": [
                    {"request_id": 1, "status": "success", "response": "..."},
                    {"request_id": 2, "status": "failed", "error": "..."}
                ],
                "processing_time": 12.34
            }
        }


class LearningStatsResponse(BaseModel):
    """Response model for learning statistics"""
    total_interactions: int = Field(..., description="Total recorded interactions")
    feedback_summary: Dict[str, int] = Field(..., description="Feedback summary by rating")
    learning_progress: Dict[str, Any] = Field(..., description="Learning progress metrics")
    pattern_insights: List[Dict[str, Any]] = Field(default_factory=list, description="Discovered patterns")
    model_improvements: List[str] = Field(default_factory=list, description="Recent model improvements")
    
    class Config:
        schema_extra = {
            "example": {
                "total_interactions": 5000,
                "feedback_summary": {
                    "5_stars": 2800,
                    "4_stars": 1500,
                    "3_stars": 500,
                    "2_stars": 150,
                    "1_star": 50
                },
                "learning_progress": {
                    "accuracy_improvement": 0.15,
                    "response_time_improvement": 0.08,
                    "user_satisfaction_trend": "increasing"
                },
                "pattern_insights": [
                    {
                        "pattern": "android_ui_questions",
                        "frequency": 450,
                        "success_rate": 0.92
                    }
                ],
                "model_improvements": [
                    "Improved Android Jetpack Compose responses",
                    "Better error handling suggestions"
                ]
            }
        }


class ErrorResponse(BaseModel):
    """Response model for errors"""
    error: str = Field(..., description="Error message")
    error_code: str = Field(..., description="Error code")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: str = Field(..., description="Error timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "error": "Model not found",
                "error_code": "MODEL_NOT_FOUND",
                "details": {
                    "requested_model": "nonexistent-model",
                    "available_models": ["mistral:7b", "codellama:13b"]
                },
                "timestamp": "2024-01-15T10:30:00Z"
            }
        }
