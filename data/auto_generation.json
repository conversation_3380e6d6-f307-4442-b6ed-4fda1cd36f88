{"isRunning": true, "lastRun": "", "nextRun": "2025-07-16T07:25:37.395Z", "totalAutoGenerations": 0, "errors": ["2025-07-16T08:20:37.411Z: <!DOCTYPE html><html><head><style data-next-hide-fouc=\"true\">body{display:none}</style><noscript data-next-hide-fouc=\"true\"><style>body{display:block}</style></noscript><meta charSet=\"utf-8\"/><meta name=\"viewport\" content=\"width=device-width\"/><meta name=\"next-head-count\" content=\"2\"/><noscript data-n-css=\"\"></noscript><script defer=\"\" nomodule=\"\" src=\"/_next/static/chunks/polyfills.js\"></script><script src=\"/_next/static/chunks/fallback/webpack.js\" defer=\"\"></script><script src=\"/_next/static/chunks/fallback/main.js\" defer=\"\"></script><script src=\"/_next/static/chunks/fallback/pages/_app.js\" defer=\"\"></script><script src=\"/_next/static/chunks/fallback/pages/_error.js\" defer=\"\"></script><noscript id=\"__next_css__DO_NOT_USE__\"></noscript></head><body><div id=\"__next\"></div><script src=\"/_next/static/chunks/fallback/react-refresh.js\"></script><script id=\"__NEXT_DATA__\" type=\"application/json\">{\"props\":{\"pageProps\":{\"statusCode\":500}},\"page\":\"/_error\",\"query\":{},\"buildId\":\"development\",\"isFallback\":false,\"err\":{\"name\":\"ModuleBuildError\",\"source\":\"server\",\"message\":\"Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\\nError: \\n  × Expression expected\\n     ╭─[/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx:215:1]\\n 215 │     }, 2000) // Check every 2 seconds\\n 216 │   }\\n 217 │ \\n 218 │       } else {\\n     ·         ────\\n 219 │         const error = await response.json()\\n 220 │         alert(`Generation failed: ${error.error}`)\\n 221 │       }\\n     ╰────\\n\\n\\nCaused by:\\n    Syntax Error\",\"stack\":\"ModuleBuildError: Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\\nError: \\n  \\u001b[38;2;255;30;30m×\\u001b[0m Expression expected\\n     ╭─[\\u001b[38;2;92;157;255;1;4m/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\\u001b[0m:215:1]\\n \\u001b[2m215\\u001b[0m │     }, 2000) // Check every 2 seconds\\n \\u001b[2m216\\u001b[0m │   }\\n \\u001b[2m217\\u001b[0m │ \\n \\u001b[2m218\\u001b[0m │       } else {\\n     · \\u001b[38;2;246;87;248m        ────\\u001b[0m\\n \\u001b[2m219\\u001b[0m │         const error = await response.json()\\n \\u001b[2m220\\u001b[0m │         alert(`Generation failed: ${error.error}`)\\n \\u001b[2m221\\u001b[0m │       }\\n     ╰────\\n\\n\\nCaused by:\\n    Syntax Error\\n    at processResult (/home/<USER>/Desktop/proiecte/androidweb/node_modules/next/dist/compiled/webpack/bundle5.js:28:400590)\\n    at /home/<USER>/Desktop/proiecte/androidweb/node_modules/next/dist/compiled/webpack/bundle5.js:28:402302\\n    at /home/<USER>/Desktop/proiecte/androidweb/node_modules/next/dist/compiled/loader-runner/LoaderRunner.js:1:8645\\n    at /home/<USER>/Desktop/proiecte/androidweb/node_modules/next/dist/compiled/loader-runner/LoaderRunner.js:1:5019\\n    at r.callback (/home/<USER>/Desktop/proiecte/androidweb/node_modules/next/dist/compiled/loader-runner/LoaderRunner.js:1:4039)\"},\"gip\":true,\"scriptLoader\":[]}</script></body></html>", "2025-07-16T08:25:37.412Z: <!DOCTYPE html><html><head><style data-next-hide-fouc=\"true\">body{display:none}</style><noscript data-next-hide-fouc=\"true\"><style>body{display:block}</style></noscript><meta charSet=\"utf-8\"/><meta name=\"viewport\" content=\"width=device-width\"/><meta name=\"next-head-count\" content=\"2\"/><noscript data-n-css=\"\"></noscript><script defer=\"\" nomodule=\"\" src=\"/_next/static/chunks/polyfills.js\"></script><script src=\"/_next/static/chunks/fallback/webpack.js\" defer=\"\"></script><script src=\"/_next/static/chunks/fallback/main.js\" defer=\"\"></script><script src=\"/_next/static/chunks/fallback/pages/_app.js\" defer=\"\"></script><script src=\"/_next/static/chunks/fallback/pages/_error.js\" defer=\"\"></script><noscript id=\"__next_css__DO_NOT_USE__\"></noscript></head><body><div id=\"__next\"></div><script src=\"/_next/static/chunks/fallback/react-refresh.js\"></script><script id=\"__NEXT_DATA__\" type=\"application/json\">{\"props\":{\"pageProps\":{\"statusCode\":500}},\"page\":\"/_error\",\"query\":{},\"buildId\":\"development\",\"isFallback\":false,\"err\":{\"name\":\"ModuleBuildError\",\"source\":\"server\",\"message\":\"Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\\nError: \\n  × Expression expected\\n     ╭─[/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx:215:1]\\n 215 │     }, 2000) // Check every 2 seconds\\n 216 │   }\\n 217 │ \\n 218 │       } else {\\n     ·         ────\\n 219 │         const error = await response.json()\\n 220 │         alert(`Generation failed: ${error.error}`)\\n 221 │       }\\n     ╰────\\n\\n\\nCaused by:\\n    Syntax Error\",\"stack\":\"ModuleBuildError: Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\\nError: \\n  \\u001b[38;2;255;30;30m×\\u001b[0m Expression expected\\n     ╭─[\\u001b[38;2;92;157;255;1;4m/home/<USER>/Desktop/proiecte/androidweb/src/app/realtime-creation/page.tsx\\u001b[0m:215:1]\\n \\u001b[2m215\\u001b[0m │     }, 2000) // Check every 2 seconds\\n \\u001b[2m216\\u001b[0m │   }\\n \\u001b[2m217\\u001b[0m │ \\n \\u001b[2m218\\u001b[0m │       } else {\\n     · \\u001b[38;2;246;87;248m        ────\\u001b[0m\\n \\u001b[2m219\\u001b[0m │         const error = await response.json()\\n \\u001b[2m220\\u001b[0m │         alert(`Generation failed: ${error.error}`)\\n \\u001b[2m221\\u001b[0m │       }\\n     ╰────\\n\\n\\nCaused by:\\n    Syntax Error\\n    at processResult (/home/<USER>/Desktop/proiecte/androidweb/node_modules/next/dist/compiled/webpack/bundle5.js:28:400590)\\n    at /home/<USER>/Desktop/proiecte/androidweb/node_modules/next/dist/compiled/webpack/bundle5.js:28:402302\\n    at /home/<USER>/Desktop/proiecte/androidweb/node_modules/next/dist/compiled/loader-runner/LoaderRunner.js:1:8645\\n    at /home/<USER>/Desktop/proiecte/androidweb/node_modules/next/dist/compiled/loader-runner/LoaderRunner.js:1:5019\\n    at r.callback (/home/<USER>/Desktop/proiecte/androidweb/node_modules/next/dist/compiled/loader-runner/LoaderRunner.js:1:4039)\"},\"gip\":true,\"scriptLoader\":[]}</script></body></html>"]}