{"name": "@prisma/client", "version": "4.16.2", "description": "Prisma Client is an auto-generated, type-safe and modern JavaScript/TypeScript ORM for Node.js that's tailored to your data. Supports MySQL, PostgreSQL, MariaDB, SQLite databases.", "keywords": ["orm", "prisma2", "prisma", "client", "query", "database", "sql", "postgres", "postgresql", "mysql", "sqlite", "ma<PERSON>b", "mssql", "typescript", "query-builder"], "main": "index.js", "browser": "index-browser.js", "types": "index.d.ts", "license": "Apache-2.0", "engines": {"node": ">=14.17"}, "homepage": "https://www.prisma.io", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/client"}, "author": "<PERSON> <suchane<PERSON>@prisma.io>", "bugs": "https://github.com/prisma/prisma/issues", "files": ["README.md", "runtime", "!runtime/*.map", "scripts", "generator-build", "edge.js", "edge.d.ts", "index.js", "index.d.ts", "index-browser.js", "extension.js", "extension.d.ts"], "devDependencies": {"@codspeed/benchmark.js-plugin": "1.1.0", "@faker-js/faker": "8.0.2", "@fast-check/jest": "1.6.2", "@jest/create-cache-key-function": "29.5.0", "@jest/globals": "29.5.0", "@jest/test-sequencer": "29.5.0", "@opentelemetry/api": "1.4.1", "@opentelemetry/context-async-hooks": "1.13.0", "@opentelemetry/instrumentation": "0.39.1", "@opentelemetry/resources": "1.13.0", "@opentelemetry/sdk-trace-base": "1.13.0", "@opentelemetry/semantic-conventions": "1.13.0", "@prisma/mini-proxy": "0.7.0", "@swc-node/register": "1.6.5", "@swc/core": "1.3.64", "@swc/jest": "0.2.26", "@timsuchanek/copy": "1.4.5", "@types/debug": "4.1.8", "@types/fs-extra": "9.0.13", "@types/jest": "29.5.2", "@types/js-levenshtein": "1.1.1", "@types/mssql": "8.1.2", "@types/node": "18.16.16", "@types/pg": "8.10.2", "@types/yeoman-generator": "5.2.11", "arg": "5.0.2", "benchmark": "2.1.4", "ci-info": "3.8.0", "decimal.js": "10.4.3", "env-paths": "2.2.1", "esbuild": "0.15.13", "execa": "5.1.1", "expect-type": "0.16.0", "flat-map-polyfill": "0.3.8", "fs-extra": "11.1.1", "get-own-enumerable-property-symbols": "3.0.2", "get-stream": "6.0.1", "globby": "11.1.0", "indent-string": "4.0.0", "is-obj": "2.0.0", "is-regexp": "2.1.0", "jest": "29.5.0", "jest-junit": "16.0.0", "jest-serializer-ansi-escapes": "2.0.1", "jest-snapshot": "29.5.0", "js-levenshtein": "1.1.6", "kleur": "4.1.5", "klona": "2.0.6", "lz-string": "1.5.0", "mariadb": "3.1.2", "memfs": "3.5.3", "mssql": "9.1.1", "new-github-issue-url": "0.2.1", "node-fetch": "2.6.11", "p-retry": "4.6.2", "pg": "8.9.0", "pkg-up": "3.1.0", "pluralize": "8.0.0", "resolve": "1.22.2", "rimraf": "3.0.2", "simple-statistics": "7.8.3", "sort-keys": "4.2.0", "source-map-support": "0.5.21", "sql-template-tag": "5.0.3", "stacktrace-parser": "0.1.10", "strip-ansi": "6.0.1", "strip-indent": "3.0.0", "ts-node": "10.9.1", "ts-pattern": "4.3.0", "tsd": "0.28.1", "typescript": "4.9.5", "undici": "5.22.1", "yeoman-generator": "5.9.0", "yo": "4.3.1", "zx": "7.2.2", "@prisma/debug": "4.16.2", "@prisma/engines": "4.16.2", "@prisma/fetch-engine": "4.16.2", "@prisma/generator-helper": "4.16.2", "@prisma/get-platform": "4.16.2", "@prisma/instrumentation": "4.16.2", "@prisma/internals": "4.16.2", "@prisma/migrate": "4.16.2"}, "peerDependencies": {"prisma": "*"}, "peerDependenciesMeta": {"prisma": {"optional": true}}, "dependencies": {"@prisma/engines-version": "4.16.1-1.4bc8b6e1b66cb932731fb1bdbbc550d1e010de81"}, "sideEffects": false, "scripts": {"dev": "DEV=true node -r esbuild-register helpers/build.ts", "build": "node -r esbuild-register helpers/build.ts", "test": "jest --silent", "test:e2e": "node -r esbuild-register tests/e2e/_utils/run.ts", "test:functional": "node -r esbuild-register helpers/functional-test/run-tests.ts", "test:memory": "node -r esbuild-register helpers/memory-tests.ts", "test:functional:code": "node -r esbuild-register helpers/functional-test/run-tests.ts --no-types", "test:functional:types": "node -r esbuild-register helpers/functional-test/run-tests.ts --types-only", "test-notypes": "jest --testPathIgnorePatterns src/__tests__/types/types.test.ts", "generate": "node scripts/postinstall.js", "postinstall": "node scripts/postinstall.js", "new-test": "NODE_OPTIONS='-r ts-node/register' yo ./helpers/generator-test/index.ts"}}