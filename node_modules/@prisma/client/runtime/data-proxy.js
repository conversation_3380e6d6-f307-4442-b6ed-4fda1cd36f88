"use strict";var fl=Object.create;var Er=Object.defineProperty;var gl=Object.getOwnPropertyDescriptor;var yl=Object.getOwnPropertyNames;var hl=Object.getPrototypeOf,xl=Object.prototype.hasOwnProperty;var U=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Rt=(e,t)=>{for(var r in t)Er(e,r,{get:t[r],enumerable:!0})},Ki=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of yl(t))!xl.call(e,i)&&i!==r&&Er(e,i,{get:()=>t[i],enumerable:!(n=gl(t,i))||n.enumerable});return e};var K=(e,t,r)=>(r=e!=null?fl(hl(e)):{},Ki(t||!e||!e.__esModule?Er(r,"default",{value:e,enumerable:!0}):r,e)),El=e=>Ki(Er({},"__esModule",{value:!0}),e);var ro=U((rm,to)=>{var st=1e3,at=st*60,lt=at*60,Ye=lt*24,wl=Ye*7,Tl=Ye*365.25;to.exports=function(e,t){t=t||{};var r=typeof e;if(r==="string"&&e.length>0)return Pl(e);if(r==="number"&&isFinite(e))return t.long?Al(e):Ml(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function Pl(e){if(e=String(e),!(e.length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!!t){var r=parseFloat(t[1]),n=(t[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return r*Tl;case"weeks":case"week":case"w":return r*wl;case"days":case"day":case"d":return r*Ye;case"hours":case"hour":case"hrs":case"hr":case"h":return r*lt;case"minutes":case"minute":case"mins":case"min":case"m":return r*at;case"seconds":case"second":case"secs":case"sec":case"s":return r*st;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}function Ml(e){var t=Math.abs(e);return t>=Ye?Math.round(e/Ye)+"d":t>=lt?Math.round(e/lt)+"h":t>=at?Math.round(e/at)+"m":t>=st?Math.round(e/st)+"s":e+"ms"}function Al(e){var t=Math.abs(e);return t>=Ye?wr(e,t,Ye,"day"):t>=lt?wr(e,t,lt,"hour"):t>=at?wr(e,t,at,"minute"):t>=st?wr(e,t,st,"second"):e+" ms"}function wr(e,t,r,n){var i=t>=r*1.5;return Math.round(e/r)+" "+n+(i?"s":"")}});var Sn=U((nm,no)=>{function vl(e){r.debug=r,r.default=r,r.coerce=l,r.disable=o,r.enable=i,r.enabled=s,r.humanize=ro(),r.destroy=u,Object.keys(e).forEach(c=>{r[c]=e[c]}),r.names=[],r.skips=[],r.formatters={};function t(c){let p=0;for(let d=0;d<c.length;d++)p=(p<<5)-p+c.charCodeAt(d),p|=0;return r.colors[Math.abs(p)%r.colors.length]}r.selectColor=t;function r(c){let p,d=null,m,g;function f(...E){if(!f.enabled)return;let x=f,b=Number(new Date),h=b-(p||b);x.diff=h,x.prev=p,x.curr=b,p=b,E[0]=r.coerce(E[0]),typeof E[0]!="string"&&E.unshift("%O");let y=0;E[0]=E[0].replace(/%([a-zA-Z%])/g,(A,P)=>{if(A==="%%")return"%";y++;let k=r.formatters[P];if(typeof k=="function"){let B=E[y];A=k.call(x,B),E.splice(y,1),y--}return A}),r.formatArgs.call(x,E),(x.log||r.log).apply(x,E)}return f.namespace=c,f.useColors=r.useColors(),f.color=r.selectColor(c),f.extend=n,f.destroy=r.destroy,Object.defineProperty(f,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(m!==r.namespaces&&(m=r.namespaces,g=r.enabled(c)),g),set:E=>{d=E}}),typeof r.init=="function"&&r.init(f),f}function n(c,p){let d=r(this.namespace+(typeof p>"u"?":":p)+c);return d.log=this.log,d}function i(c){r.save(c),r.namespaces=c,r.names=[],r.skips=[];let p,d=(typeof c=="string"?c:"").split(/[\s,]+/),m=d.length;for(p=0;p<m;p++)!d[p]||(c=d[p].replace(/\*/g,".*?"),c[0]==="-"?r.skips.push(new RegExp("^"+c.slice(1)+"$")):r.names.push(new RegExp("^"+c+"$")))}function o(){let c=[...r.names.map(a),...r.skips.map(a).map(p=>"-"+p)].join(",");return r.enable(""),c}function s(c){if(c[c.length-1]==="*")return!0;let p,d;for(p=0,d=r.skips.length;p<d;p++)if(r.skips[p].test(c))return!1;for(p=0,d=r.names.length;p<d;p++)if(r.names[p].test(c))return!0;return!1}function a(c){return c.toString().substring(2,c.toString().length-2).replace(/\.\*\?$/,"*")}function l(c){return c instanceof Error?c.stack||c.message:c}function u(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return r.enable(r.load()),r}no.exports=vl});var io=U((fe,Tr)=>{fe.formatArgs=Fl;fe.save=Rl;fe.load=Dl;fe.useColors=Cl;fe.storage=Ol();fe.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();fe.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function Cl(){return typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function Fl(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+Tr.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;e.splice(1,0,t,"color: inherit");let r=0,n=0;e[0].replace(/%[a-zA-Z%]/g,i=>{i!=="%%"&&(r++,i==="%c"&&(n=r))}),e.splice(n,0,t)}fe.log=console.debug||console.log||(()=>{});function Rl(e){try{e?fe.storage.setItem("debug",e):fe.storage.removeItem("debug")}catch{}}function Dl(){let e;try{e=fe.storage.getItem("debug")}catch{}return!e&&typeof process<"u"&&"env"in process&&(e=process.env.DEBUG),e}function Ol(){try{return localStorage}catch{}}Tr.exports=Sn()(fe);var{formatters:Sl}=Tr.exports;Sl.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}});var so=U((im,oo)=>{"use strict";oo.exports=(e,t=process.argv)=>{let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),i=t.indexOf("--");return n!==-1&&(i===-1||n<i)}});var uo=U((om,lo)=>{"use strict";var Il=require("os"),ao=require("tty"),ge=so(),{env:G}=process,Ve;ge("no-color")||ge("no-colors")||ge("color=false")||ge("color=never")?Ve=0:(ge("color")||ge("colors")||ge("color=true")||ge("color=always"))&&(Ve=1);"FORCE_COLOR"in G&&(G.FORCE_COLOR==="true"?Ve=1:G.FORCE_COLOR==="false"?Ve=0:Ve=G.FORCE_COLOR.length===0?1:Math.min(parseInt(G.FORCE_COLOR,10),3));function In(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function kn(e,t){if(Ve===0)return 0;if(ge("color=16m")||ge("color=full")||ge("color=truecolor"))return 3;if(ge("color=256"))return 2;if(e&&!t&&Ve===void 0)return 0;let r=Ve||0;if(G.TERM==="dumb")return r;if(process.platform==="win32"){let n=Il.release().split(".");return Number(n[0])>=10&&Number(n[2])>=10586?Number(n[2])>=14931?3:2:1}if("CI"in G)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(n=>n in G)||G.CI_NAME==="codeship"?1:r;if("TEAMCITY_VERSION"in G)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(G.TEAMCITY_VERSION)?1:0;if(G.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in G){let n=parseInt((G.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(G.TERM_PROGRAM){case"iTerm.app":return n>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(G.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(G.TERM)||"COLORTERM"in G?1:r}function kl(e){let t=kn(e,e&&e.isTTY);return In(t)}lo.exports={supportsColor:kl,stdout:In(kn(!0,ao.isatty(1))),stderr:In(kn(!0,ao.isatty(2)))}});var po=U((z,Mr)=>{var $l=require("tty"),Pr=require("util");z.init=Vl;z.log=ql;z.formatArgs=_l;z.save=jl;z.load=Bl;z.useColors=Nl;z.destroy=Pr.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");z.colors=[6,2,3,4,5,1];try{let e=uo();e&&(e.stderr||e).level>=2&&(z.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}z.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(i,o)=>o.toUpperCase()),n=process.env[t];return/^(yes|on|true|enabled)$/i.test(n)?n=!0:/^(no|off|false|disabled)$/i.test(n)?n=!1:n==="null"?n=null:n=Number(n),e[r]=n,e},{});function Nl(){return"colors"in z.inspectOpts?Boolean(z.inspectOpts.colors):$l.isatty(process.stderr.fd)}function _l(e){let{namespace:t,useColors:r}=this;if(r){let n=this.color,i="\x1B[3"+(n<8?n:"8;5;"+n),o=`  ${i};1m${t} \x1B[0m`;e[0]=o+e[0].split(`
`).join(`
`+o),e.push(i+"m+"+Mr.exports.humanize(this.diff)+"\x1B[0m")}else e[0]=Ll()+t+" "+e[0]}function Ll(){return z.inspectOpts.hideDate?"":new Date().toISOString()+" "}function ql(...e){return process.stderr.write(Pr.format(...e)+`
`)}function jl(e){e?process.env.DEBUG=e:delete process.env.DEBUG}function Bl(){return process.env.DEBUG}function Vl(e){e.inspectOpts={};let t=Object.keys(z.inspectOpts);for(let r=0;r<t.length;r++)e.inspectOpts[t[r]]=z.inspectOpts[t[r]]}Mr.exports=Sn()(z);var{formatters:co}=Mr.exports;co.o=function(e){return this.inspectOpts.colors=this.useColors,Pr.inspect(e,this.inspectOpts).split(`
`).map(t=>t.trim()).join(" ")};co.O=function(e){return this.inspectOpts.colors=this.useColors,Pr.inspect(e,this.inspectOpts)}});var mo=U((sm,$n)=>{typeof process>"u"||process.type==="renderer"||process.browser===!0||process.__nwjs?$n.exports=io():$n.exports=po()});var go=U((lm,Ql)=>{Ql.exports={name:"dotenv",version:"16.0.3",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{require:"./lib/main.js",types:"./lib/main.d.ts",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard","lint-readme":"standard-markdown",pretest:"npm run lint && npm run dts-check",test:"tap tests/*.js --100 -Rspec",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@types/node":"^17.0.9",decache:"^4.6.1",dtslint:"^3.7.0",sinon:"^12.0.1",standard:"^16.0.4","standard-markdown":"^7.1.0","standard-version":"^9.3.2",tap:"^15.1.6",tar:"^6.1.11",typescript:"^4.5.4"},engines:{node:">=12"}}});var ho=U((um,Fr)=>{var Jl=require("fs"),yo=require("path"),Gl=require("os"),Wl=go(),Hl=Wl.version,zl=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function Yl(e){let t={},r=e.toString();r=r.replace(/\r\n?/mg,`
`);let n;for(;(n=zl.exec(r))!=null;){let i=n[1],o=n[2]||"";o=o.trim();let s=o[0];o=o.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),s==='"'&&(o=o.replace(/\\n/g,`
`),o=o.replace(/\\r/g,"\r")),t[i]=o}return t}function _n(e){console.log(`[dotenv@${Hl}][DEBUG] ${e}`)}function Zl(e){return e[0]==="~"?yo.join(Gl.homedir(),e.slice(1)):e}function Xl(e){let t=yo.resolve(process.cwd(),".env"),r="utf8",n=Boolean(e&&e.debug),i=Boolean(e&&e.override);e&&(e.path!=null&&(t=Zl(e.path)),e.encoding!=null&&(r=e.encoding));try{let o=Cr.parse(Jl.readFileSync(t,{encoding:r}));return Object.keys(o).forEach(function(s){Object.prototype.hasOwnProperty.call(process.env,s)?(i===!0&&(process.env[s]=o[s]),n&&_n(i===!0?`"${s}" is already defined in \`process.env\` and WAS overwritten`:`"${s}" is already defined in \`process.env\` and was NOT overwritten`)):process.env[s]=o[s]}),{parsed:o}}catch(o){return n&&_n(`Failed to load ${t} ${o.message}`),{error:o}}}var Cr={config:Xl,parse:Yl};Fr.exports.config=Cr.config;Fr.exports.parse=Cr.parse;Fr.exports=Cr});var Po=U((ym,To)=>{"use strict";To.exports=e=>{let t=e.match(/^[ \t]*(?=\S)/gm);return t?t.reduce((r,n)=>Math.min(r,n.length),1/0):0}});var Bn=U((hm,Mo)=>{"use strict";var nu=Po();Mo.exports=e=>{let t=nu(e);if(t===0)return e;let r=new RegExp(`^[ \\t]{${t}}`,"gm");return e.replace(r,"")}});var Dr=U((wm,Ao)=>{"use strict";Ao.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var Fo=U((Pm,Co)=>{"use strict";Co.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var Or=U((Mm,Ro)=>{"use strict";var uu=Fo();Ro.exports=e=>typeof e=="string"?e.replace(uu(),""):e});var jr=U((xf,Wo)=>{"use strict";Wo.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,l,u,c,p,d,m,g,f,E,x,b,h,y=[];for(l=0;l<i;l++)y.push(l+1),y.push(t.charCodeAt(s+l));for(var T=y.length-1;a<o-3;)for(E=r.charCodeAt(s+(u=a)),x=r.charCodeAt(s+(c=a+1)),b=r.charCodeAt(s+(p=a+2)),h=r.charCodeAt(s+(d=a+3)),m=a+=4,l=0;l<T;l+=2)g=y[l],f=y[l+1],u=e(g,u,c,E,f),c=e(u,c,p,x,f),p=e(c,p,d,b,f),m=e(p,d,m,h,f),y[l]=m,d=p,p=c,c=u,u=g;for(;a<o;)for(E=r.charCodeAt(s+(u=a)),m=++a,l=0;l<T;l+=2)g=y[l],y[l]=m=e(g,u,m,E,y[l+1]),u=g;return m}}()});var ts=U((li,ui)=>{(function(e,t){typeof require=="function"&&typeof li=="object"&&typeof ui=="object"?ui.exports=t():e.pluralize=t()})(li,function(){var e=[],t=[],r={},n={},i={};function o(m){return typeof m=="string"?new RegExp("^"+m+"$","i"):m}function s(m,g){return m===g?g:m===m.toLowerCase()?g.toLowerCase():m===m.toUpperCase()?g.toUpperCase():m[0]===m[0].toUpperCase()?g.charAt(0).toUpperCase()+g.substr(1).toLowerCase():g.toLowerCase()}function a(m,g){return m.replace(/\$(\d{1,2})/g,function(f,E){return g[E]||""})}function l(m,g){return m.replace(g[0],function(f,E){var x=a(g[1],arguments);return s(f===""?m[E-1]:f,x)})}function u(m,g,f){if(!m.length||r.hasOwnProperty(m))return g;for(var E=f.length;E--;){var x=f[E];if(x[0].test(g))return l(g,x)}return g}function c(m,g,f){return function(E){var x=E.toLowerCase();return g.hasOwnProperty(x)?s(E,x):m.hasOwnProperty(x)?s(E,m[x]):u(x,E,f)}}function p(m,g,f,E){return function(x){var b=x.toLowerCase();return g.hasOwnProperty(b)?!0:m.hasOwnProperty(b)?!1:u(b,b,f)===b}}function d(m,g,f){var E=g===1?d.singular(m):d.plural(m);return(f?g+" ":"")+E}return d.plural=c(i,n,e),d.isPlural=p(i,n,e),d.singular=c(n,i,t),d.isSingular=p(n,i,t),d.addPluralRule=function(m,g){e.push([o(m),g])},d.addSingularRule=function(m,g){t.push([o(m),g])},d.addUncountableRule=function(m){if(typeof m=="string"){r[m.toLowerCase()]=!0;return}d.addPluralRule(m,"$0"),d.addSingularRule(m,"$0")},d.addIrregularRule=function(m,g){g=g.toLowerCase(),m=m.toLowerCase(),i[m]=g,n[g]=m},[["I","we"],["me","us"],["he","they"],["she","they"],["them","them"],["myself","ourselves"],["yourself","yourselves"],["itself","themselves"],["herself","themselves"],["himself","themselves"],["themself","themselves"],["is","are"],["was","were"],["has","have"],["this","these"],["that","those"],["echo","echoes"],["dingo","dingoes"],["volcano","volcanoes"],["tornado","tornadoes"],["torpedo","torpedoes"],["genus","genera"],["viscus","viscera"],["stigma","stigmata"],["stoma","stomata"],["dogma","dogmata"],["lemma","lemmata"],["schema","schemata"],["anathema","anathemata"],["ox","oxen"],["axe","axes"],["die","dice"],["yes","yeses"],["foot","feet"],["eave","eaves"],["goose","geese"],["tooth","teeth"],["quiz","quizzes"],["human","humans"],["proof","proofs"],["carve","carves"],["valve","valves"],["looey","looies"],["thief","thieves"],["groove","grooves"],["pickaxe","pickaxes"],["passerby","passersby"]].forEach(function(m){return d.addIrregularRule(m[0],m[1])}),[[/s?$/i,"s"],[/[^\u0000-\u007F]$/i,"$0"],[/([^aeiou]ese)$/i,"$1"],[/(ax|test)is$/i,"$1es"],[/(alias|[^aou]us|t[lm]as|gas|ris)$/i,"$1es"],[/(e[mn]u)s?$/i,"$1s"],[/([^l]ias|[aeiou]las|[ejzr]as|[iu]am)$/i,"$1"],[/(alumn|syllab|vir|radi|nucle|fung|cact|stimul|termin|bacill|foc|uter|loc|strat)(?:us|i)$/i,"$1i"],[/(alumn|alg|vertebr)(?:a|ae)$/i,"$1ae"],[/(seraph|cherub)(?:im)?$/i,"$1im"],[/(her|at|gr)o$/i,"$1oes"],[/(agend|addend|millenni|dat|extrem|bacteri|desiderat|strat|candelabr|errat|ov|symposi|curricul|automat|quor)(?:a|um)$/i,"$1a"],[/(apheli|hyperbat|periheli|asyndet|noumen|phenomen|criteri|organ|prolegomen|hedr|automat)(?:a|on)$/i,"$1a"],[/sis$/i,"ses"],[/(?:(kni|wi|li)fe|(ar|l|ea|eo|oa|hoo)f)$/i,"$1$2ves"],[/([^aeiouy]|qu)y$/i,"$1ies"],[/([^ch][ieo][ln])ey$/i,"$1ies"],[/(x|ch|ss|sh|zz)$/i,"$1es"],[/(matr|cod|mur|sil|vert|ind|append)(?:ix|ex)$/i,"$1ices"],[/\b((?:tit)?m|l)(?:ice|ouse)$/i,"$1ice"],[/(pe)(?:rson|ople)$/i,"$1ople"],[/(child)(?:ren)?$/i,"$1ren"],[/eaux$/i,"$0"],[/m[ae]n$/i,"men"],["thou","you"]].forEach(function(m){return d.addPluralRule(m[0],m[1])}),[[/s$/i,""],[/(ss)$/i,"$1"],[/(wi|kni|(?:after|half|high|low|mid|non|night|[^\w]|^)li)ves$/i,"$1fe"],[/(ar|(?:wo|[ae])l|[eo][ao])ves$/i,"$1f"],[/ies$/i,"y"],[/\b([pl]|zomb|(?:neck|cross)?t|coll|faer|food|gen|goon|group|lass|talk|goal|cut)ies$/i,"$1ie"],[/\b(mon|smil)ies$/i,"$1ey"],[/\b((?:tit)?m|l)ice$/i,"$1ouse"],[/(seraph|cherub)im$/i,"$1"],[/(x|ch|ss|sh|zz|tto|go|cho|alias|[^aou]us|t[lm]as|gas|(?:her|at|gr)o|[aeiou]ris)(?:es)?$/i,"$1"],[/(analy|diagno|parenthe|progno|synop|the|empha|cri|ne)(?:sis|ses)$/i,"$1sis"],[/(movie|twelve|abuse|e[mn]u)s$/i,"$1"],[/(test)(?:is|es)$/i,"$1is"],[/(alumn|syllab|vir|radi|nucle|fung|cact|stimul|termin|bacill|foc|uter|loc|strat)(?:us|i)$/i,"$1us"],[/(agend|addend|millenni|dat|extrem|bacteri|desiderat|strat|candelabr|errat|ov|symposi|curricul|quor)a$/i,"$1um"],[/(apheli|hyperbat|periheli|asyndet|noumen|phenomen|criteri|organ|prolegomen|hedr|automat)a$/i,"$1on"],[/(alumn|alg|vertebr)ae$/i,"$1a"],[/(cod|mur|sil|vert|ind)ices$/i,"$1ex"],[/(matr|append)ices$/i,"$1ix"],[/(pe)(rson|ople)$/i,"$1rson"],[/(child)ren$/i,"$1"],[/(eau)x?$/i,"$1"],[/men$/i,"man"]].forEach(function(m){return d.addSingularRule(m[0],m[1])}),["adulthood","advice","agenda","aid","aircraft","alcohol","ammo","analytics","anime","athletics","audio","bison","blood","bream","buffalo","butter","carp","cash","chassis","chess","clothing","cod","commerce","cooperation","corps","debris","diabetes","digestion","elk","energy","equipment","excretion","expertise","firmware","flounder","fun","gallows","garbage","graffiti","hardware","headquarters","health","herpes","highjinks","homework","housework","information","jeans","justice","kudos","labour","literature","machinery","mackerel","mail","media","mews","moose","music","mud","manga","news","only","personnel","pike","plankton","pliers","police","pollution","premises","rain","research","rice","salmon","scissors","series","sewage","shambles","shrimp","software","species","staff","swine","tennis","traffic","transportation","trout","tuna","wealth","welfare","whiting","wildebeest","wildlife","you",/pok[eé]mon$/i,/[^aeiou]ese$/i,/deer$/i,/fish$/i,/measles$/i,/o[iu]s$/i,/pox$/i,/sheep$/i].forEach(d.addUncountableRule),d})});var qs=U((lx,Ls)=>{"use strict";Ls.exports=e=>Object.prototype.toString.call(e)==="[object RegExp]"});var Bs=U((ux,js)=>{"use strict";js.exports=e=>{let t=typeof e;return e!==null&&(t==="object"||t==="function")}});var Vs=U(Pi=>{"use strict";Object.defineProperty(Pi,"__esModule",{value:!0});Pi.default=e=>Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))});var Hs=U((jx,Ap)=>{Ap.exports={name:"@prisma/client",version:"4.16.2",description:"Prisma Client is an auto-generated, type-safe and modern JavaScript/TypeScript ORM for Node.js that's tailored to your data. Supports MySQL, PostgreSQL, MariaDB, SQLite databases.",keywords:["orm","prisma2","prisma","client","query","database","sql","postgres","postgresql","mysql","sqlite","mariadb","mssql","typescript","query-builder"],main:"index.js",browser:"index-browser.js",types:"index.d.ts",license:"Apache-2.0",engines:{node:">=14.17"},homepage:"https://www.prisma.io",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/client"},author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",scripts:{dev:"DEV=true node -r esbuild-register helpers/build.ts",build:"node -r esbuild-register helpers/build.ts",test:"jest --silent","test:e2e":"node -r esbuild-register tests/e2e/_utils/run.ts","test:functional":"node -r esbuild-register helpers/functional-test/run-tests.ts","test:memory":"node -r esbuild-register helpers/memory-tests.ts","test:functional:code":"node -r esbuild-register helpers/functional-test/run-tests.ts --no-types","test:functional:types":"node -r esbuild-register helpers/functional-test/run-tests.ts --types-only","test-notypes":"jest --testPathIgnorePatterns src/__tests__/types/types.test.ts",generate:"node scripts/postinstall.js",postinstall:"node scripts/postinstall.js",prepublishOnly:"pnpm run build","new-test":"NODE_OPTIONS='-r ts-node/register' yo ./helpers/generator-test/index.ts"},files:["README.md","runtime","!runtime/*.map","scripts","generator-build","edge.js","edge.d.ts","index.js","index.d.ts","index-browser.js","extension.js","extension.d.ts"],devDependencies:{"@codspeed/benchmark.js-plugin":"1.1.0","@faker-js/faker":"8.0.2","@fast-check/jest":"1.6.2","@jest/create-cache-key-function":"29.5.0","@jest/globals":"29.5.0","@jest/test-sequencer":"29.5.0","@opentelemetry/api":"1.4.1","@opentelemetry/context-async-hooks":"1.13.0","@opentelemetry/instrumentation":"0.39.1","@opentelemetry/resources":"1.13.0","@opentelemetry/sdk-trace-base":"1.13.0","@opentelemetry/semantic-conventions":"1.13.0","@prisma/debug":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/instrumentation":"workspace:*","@prisma/internals":"workspace:*","@prisma/migrate":"workspace:*","@prisma/mini-proxy":"0.7.0","@swc-node/register":"1.6.5","@swc/core":"1.3.64","@swc/jest":"0.2.26","@timsuchanek/copy":"1.4.5","@types/debug":"4.1.8","@types/fs-extra":"9.0.13","@types/jest":"29.5.2","@types/js-levenshtein":"1.1.1","@types/mssql":"8.1.2","@types/node":"18.16.16","@types/pg":"8.10.2","@types/yeoman-generator":"5.2.11",arg:"5.0.2",benchmark:"2.1.4","ci-info":"3.8.0","decimal.js":"10.4.3","env-paths":"2.2.1",esbuild:"0.15.13",execa:"5.1.1","expect-type":"0.16.0","flat-map-polyfill":"0.3.8","fs-extra":"11.1.1","get-own-enumerable-property-symbols":"3.0.2","get-stream":"6.0.1",globby:"11.1.0","indent-string":"4.0.0","is-obj":"2.0.0","is-regexp":"2.1.0",jest:"29.5.0","jest-junit":"16.0.0","jest-serializer-ansi-escapes":"2.0.1","jest-snapshot":"29.5.0","js-levenshtein":"1.1.6",kleur:"4.1.5",klona:"2.0.6","lz-string":"1.5.0",mariadb:"3.1.2",memfs:"3.5.3",mssql:"9.1.1","new-github-issue-url":"0.2.1","node-fetch":"2.6.11","p-retry":"4.6.2",pg:"8.9.0","pkg-up":"3.1.0",pluralize:"8.0.0",resolve:"1.22.2",rimraf:"3.0.2","simple-statistics":"7.8.3","sort-keys":"4.2.0","source-map-support":"0.5.21","sql-template-tag":"5.0.3","stacktrace-parser":"0.1.10","strip-ansi":"6.0.1","strip-indent":"3.0.0","ts-node":"10.9.1","ts-pattern":"4.3.0",tsd:"0.28.1",typescript:"4.9.5",undici:"5.22.1","yeoman-generator":"5.9.0",yo:"4.3.1",zx:"7.2.2"},peerDependencies:{prisma:"*"},peerDependenciesMeta:{prisma:{optional:!0}},dependencies:{"@prisma/engines-version":"4.16.1-1.4bc8b6e1b66cb932731fb1bdbbc550d1e010de81"},sideEffects:!1}});var cl=U((K0,vn)=>{var ul=function(){var e=String.fromCharCode,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",n={};function i(s,a){if(!n[s]){n[s]={};for(var l=0;l<s.length;l++)n[s][s.charAt(l)]=l}return n[s][a]}var o={compressToBase64:function(s){if(s==null)return"";var a=o._compress(s,6,function(l){return t.charAt(l)});switch(a.length%4){default:case 0:return a;case 1:return a+"===";case 2:return a+"==";case 3:return a+"="}},decompressFromBase64:function(s){return s==null?"":s==""?null:o._decompress(s.length,32,function(a){return i(t,s.charAt(a))})},compressToUTF16:function(s){return s==null?"":o._compress(s,15,function(a){return e(a+32)})+" "},decompressFromUTF16:function(s){return s==null?"":s==""?null:o._decompress(s.length,16384,function(a){return s.charCodeAt(a)-32})},compressToUint8Array:function(s){for(var a=o.compress(s),l=new Uint8Array(a.length*2),u=0,c=a.length;u<c;u++){var p=a.charCodeAt(u);l[u*2]=p>>>8,l[u*2+1]=p%256}return l},decompressFromUint8Array:function(s){if(s==null)return o.decompress(s);for(var a=new Array(s.length/2),l=0,u=a.length;l<u;l++)a[l]=s[l*2]*256+s[l*2+1];var c=[];return a.forEach(function(p){c.push(e(p))}),o.decompress(c.join(""))},compressToEncodedURIComponent:function(s){return s==null?"":o._compress(s,6,function(a){return r.charAt(a)})},decompressFromEncodedURIComponent:function(s){return s==null?"":s==""?null:(s=s.replace(/ /g,"+"),o._decompress(s.length,32,function(a){return i(r,s.charAt(a))}))},compress:function(s){return o._compress(s,16,function(a){return e(a)})},_compress:function(s,a,l){if(s==null)return"";var u,c,p={},d={},m="",g="",f="",E=2,x=3,b=2,h=[],y=0,T=0,A;for(A=0;A<s.length;A+=1)if(m=s.charAt(A),Object.prototype.hasOwnProperty.call(p,m)||(p[m]=x++,d[m]=!0),g=f+m,Object.prototype.hasOwnProperty.call(p,g))f=g;else{if(Object.prototype.hasOwnProperty.call(d,f)){if(f.charCodeAt(0)<256){for(u=0;u<b;u++)y=y<<1,T==a-1?(T=0,h.push(l(y)),y=0):T++;for(c=f.charCodeAt(0),u=0;u<8;u++)y=y<<1|c&1,T==a-1?(T=0,h.push(l(y)),y=0):T++,c=c>>1}else{for(c=1,u=0;u<b;u++)y=y<<1|c,T==a-1?(T=0,h.push(l(y)),y=0):T++,c=0;for(c=f.charCodeAt(0),u=0;u<16;u++)y=y<<1|c&1,T==a-1?(T=0,h.push(l(y)),y=0):T++,c=c>>1}E--,E==0&&(E=Math.pow(2,b),b++),delete d[f]}else for(c=p[f],u=0;u<b;u++)y=y<<1|c&1,T==a-1?(T=0,h.push(l(y)),y=0):T++,c=c>>1;E--,E==0&&(E=Math.pow(2,b),b++),p[g]=x++,f=String(m)}if(f!==""){if(Object.prototype.hasOwnProperty.call(d,f)){if(f.charCodeAt(0)<256){for(u=0;u<b;u++)y=y<<1,T==a-1?(T=0,h.push(l(y)),y=0):T++;for(c=f.charCodeAt(0),u=0;u<8;u++)y=y<<1|c&1,T==a-1?(T=0,h.push(l(y)),y=0):T++,c=c>>1}else{for(c=1,u=0;u<b;u++)y=y<<1|c,T==a-1?(T=0,h.push(l(y)),y=0):T++,c=0;for(c=f.charCodeAt(0),u=0;u<16;u++)y=y<<1|c&1,T==a-1?(T=0,h.push(l(y)),y=0):T++,c=c>>1}E--,E==0&&(E=Math.pow(2,b),b++),delete d[f]}else for(c=p[f],u=0;u<b;u++)y=y<<1|c&1,T==a-1?(T=0,h.push(l(y)),y=0):T++,c=c>>1;E--,E==0&&(E=Math.pow(2,b),b++)}for(c=2,u=0;u<b;u++)y=y<<1|c&1,T==a-1?(T=0,h.push(l(y)),y=0):T++,c=c>>1;for(;;)if(y=y<<1,T==a-1){h.push(l(y));break}else T++;return h.join("")},decompress:function(s){return s==null?"":s==""?null:o._decompress(s.length,32768,function(a){return s.charCodeAt(a)})},_decompress:function(s,a,l){var u=[],c,p=4,d=4,m=3,g="",f=[],E,x,b,h,y,T,A,P={val:l(0),position:a,index:1};for(E=0;E<3;E+=1)u[E]=E;for(b=0,y=Math.pow(2,2),T=1;T!=y;)h=P.val&P.position,P.position>>=1,P.position==0&&(P.position=a,P.val=l(P.index++)),b|=(h>0?1:0)*T,T<<=1;switch(c=b){case 0:for(b=0,y=Math.pow(2,8),T=1;T!=y;)h=P.val&P.position,P.position>>=1,P.position==0&&(P.position=a,P.val=l(P.index++)),b|=(h>0?1:0)*T,T<<=1;A=e(b);break;case 1:for(b=0,y=Math.pow(2,16),T=1;T!=y;)h=P.val&P.position,P.position>>=1,P.position==0&&(P.position=a,P.val=l(P.index++)),b|=(h>0?1:0)*T,T<<=1;A=e(b);break;case 2:return""}for(u[3]=A,x=A,f.push(A);;){if(P.index>s)return"";for(b=0,y=Math.pow(2,m),T=1;T!=y;)h=P.val&P.position,P.position>>=1,P.position==0&&(P.position=a,P.val=l(P.index++)),b|=(h>0?1:0)*T,T<<=1;switch(A=b){case 0:for(b=0,y=Math.pow(2,8),T=1;T!=y;)h=P.val&P.position,P.position>>=1,P.position==0&&(P.position=a,P.val=l(P.index++)),b|=(h>0?1:0)*T,T<<=1;u[d++]=e(b),A=d-1,p--;break;case 1:for(b=0,y=Math.pow(2,16),T=1;T!=y;)h=P.val&P.position,P.position>>=1,P.position==0&&(P.position=a,P.val=l(P.index++)),b|=(h>0?1:0)*T,T<<=1;u[d++]=e(b),A=d-1,p--;break;case 2:return f.join("")}if(p==0&&(p=Math.pow(2,m),m++),u[A])g=u[A];else if(A===d)g=x+x.charAt(0);else return null;f.push(g),u[d++]=x+g.charAt(0),p--,x=g,p==0&&(p=Math.pow(2,m),m++)}}};return o}();typeof vn<"u"&&vn!=null?vn.exports=ul:typeof angular<"u"&&angular!=null&&angular.module("LZString",[]).factory("LZString",function(){return ul})});var Od={};Rt(Od,{DMMF:()=>ye,DMMFClass:()=>Ge,Debug:()=>Nn,Decimal:()=>le,Extensions:()=>Fn,MetricsClient:()=>pt,NotFoundError:()=>be,PrismaClientInitializationError:()=>pe,PrismaClientKnownRequestError:()=>ne,PrismaClientRustPanicError:()=>Ne,PrismaClientUnknownRequestError:()=>he,PrismaClientValidationError:()=>H,Public:()=>Rn,Sql:()=>Z,Types:()=>Dn,decompressFromBase64:()=>dl,defineDmmfProperty:()=>So,empty:()=>es,getPrismaClient:()=>al,join:()=>Xo,makeDocument:()=>nn,makeStrictEnum:()=>ll,objectEnumValues:()=>gt,raw:()=>si,sqltag:()=>ai,transformDocument:()=>Qs,unpack:()=>on,warnEnvConflicts:()=>ml,warnOnce:()=>Nt});module.exports=El(Od);var Fn={};Rt(Fn,{defineExtension:()=>Qi,getExtensionContext:()=>Ji});function Qi(e){return typeof e=="function"?e:t=>t.$extends(e)}function Ji(e){return e}var Rn={};Rt(Rn,{validator:()=>Gi});function Gi(...e){return t=>t}var Dn={};Rt(Dn,{Extensions:()=>Wi,Public:()=>Hi,Utils:()=>zi});var Wi={};var Hi={};var zi={};var On,Yi,Zi,Xi,eo=!0;typeof process<"u"&&({FORCE_COLOR:On,NODE_DISABLE_COLORS:Yi,NO_COLOR:Zi,TERM:Xi}=process.env||{},eo=process.stdout&&process.stdout.isTTY);var bl={enabled:!Yi&&Zi==null&&Xi!=="dumb"&&(On!=null&&On!=="0"||eo)};function L(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!bl.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var qd=L(0,0),R=L(1,22),$=L(2,22),jd=L(3,23),we=L(4,24),Bd=L(7,27),Vd=L(8,28),Ud=L(9,29),Kd=L(30,39),S=L(31,39),O=L(32,39),it=L(33,39),ot=L(34,39),Qd=L(35,39),Be=L(36,39),Dt=L(37,39),br=L(90,39),Jd=L(90,39),Gd=L(40,49),Wd=L(41,49),Hd=L(42,49),zd=L(43,49),Yd=L(44,49),Zd=L(45,49),Xd=L(46,49),em=L(47,49);var vr=K(mo()),Ul=100,Ar=[];typeof process<"u"&&typeof process.stderr?.write!="function"&&(vr.default.log=console.debug??console.log);function Kl(e){let t=(0,vr.default)(e),r=Object.assign((...n)=>(t.log=r.log,n.length!==0&&Ar.push([e,...n]),Ar.length>Ul&&Ar.shift(),t("",...n)),t);return r}var Nn=Object.assign(Kl,vr.default);function fo(){Ar.length=0}var re=Nn;var qn=K(ho()),Rr=K(require("fs"));var ut=K(require("path"));function xo(e){let t=e.ignoreProcessEnv?{}:process.env,r=n=>n.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(o,s){let a=/(.?)\${([a-zA-Z0-9_]+)?}/g.exec(s);if(!a)return o;let l=a[1],u,c;if(l==="\\")c=a[0],u=c.replace("\\$","$");else{let p=a[2];c=a[0].substring(l.length),u=Object.hasOwnProperty.call(t,p)?t[p]:e.parsed[p]||"",u=r(u)}return o.replace(c,u)},n)??n;for(let n in e.parsed){let i=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n];e.parsed[n]=r(i)}for(let n in e.parsed)t[n]=e.parsed[n];return e}var Ln=re("prisma:tryLoadEnv");function Ot({rootEnvPath:e,schemaEnvPath:t},r={conflictCheck:"none"}){let n=Eo(e);r.conflictCheck!=="none"&&eu(n,t,r.conflictCheck);let i=null;return bo(n?.path,t)||(i=Eo(t)),!n&&!i&&Ln("No Environment variables loaded"),i?.dotenvResult.error?console.error(S(R("Schema Env Error: "))+i.dotenvResult.error):{message:[n?.message,i?.message].filter(Boolean).join(`
`),parsed:{...n?.dotenvResult?.parsed,...i?.dotenvResult?.parsed}}}function eu(e,t,r){let n=e?.dotenvResult.parsed,i=!bo(e?.path,t);if(n&&t&&i&&Rr.default.existsSync(t)){let o=qn.default.parse(Rr.default.readFileSync(t)),s=[];for(let a in o)n[a]===o[a]&&s.push(a);if(s.length>0){let a=ut.default.relative(process.cwd(),e.path),l=ut.default.relative(process.cwd(),t);if(r==="error"){let u=`There is a conflict between env var${s.length>1?"s":""} in ${we(a)} and ${we(l)}
Conflicting env vars:
${s.map(c=>`  ${R(c)}`).join(`
`)}

We suggest to move the contents of ${we(l)} to ${we(a)} to consolidate your env vars.
`;throw new Error(u)}else if(r==="warn"){let u=`Conflict for env var${s.length>1?"s":""} ${s.map(c=>R(c)).join(", ")} in ${we(a)} and ${we(l)}
Env vars from ${we(l)} overwrite the ones from ${we(a)}
      `;console.warn(`${it("warn(prisma)")} ${u}`)}}}}function Eo(e){return tu(e)?(Ln(`Environment variables loaded from ${e}`),{dotenvResult:xo(qn.default.config({path:e,debug:process.env.DOTENV_CONFIG_DEBUG?!0:void 0})),message:$(`Environment variables loaded from ${ut.default.relative(process.cwd(),e)}`),path:e}):(Ln(`Environment variables not found at ${e}`),null)}function bo(e,t){return e&&t&&ut.default.resolve(e)===ut.default.resolve(t)}function tu(e){return Boolean(e&&Rr.default.existsSync(e))}var wo="library";function jn(e){let t=ru();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":wo)}function ru(){let e=process.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":void 0}var iu=K(Bn());function St(e){return e instanceof Error}function Vn(e){let t=process.env.PRISMA_ENGINE_PROTOCOL;if(t==="json"||t=="graphql")return t;if(t!==void 0)throw new Error(`Invalid PRISMA_ENGINE_PROTOCOL env variable value. Expected 'graphql' or 'json', got '${t}'`);return e?.previewFeatures?.includes("jsonProtocol")?"json":"graphql"}var ye;(t=>{let e;(h=>(h.findUnique="findUnique",h.findUniqueOrThrow="findUniqueOrThrow",h.findFirst="findFirst",h.findFirstOrThrow="findFirstOrThrow",h.findMany="findMany",h.create="create",h.createMany="createMany",h.update="update",h.updateMany="updateMany",h.upsert="upsert",h.delete="delete",h.deleteMany="deleteMany",h.groupBy="groupBy",h.count="count",h.aggregate="aggregate",h.findRaw="findRaw",h.aggregateRaw="aggregateRaw"))(e=t.ModelAction||(t.ModelAction={}))})(ye||(ye={}));var kt={};Rt(kt,{error:()=>au,info:()=>su,log:()=>ou,query:()=>lu,should:()=>vo,tags:()=>It,warn:()=>Un});var It={error:S("prisma:error"),warn:it("prisma:warn"),info:Be("prisma:info"),query:ot("prisma:query")},vo={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function ou(...e){console.log(...e)}function Un(e,...t){vo.warn()&&console.warn(`${It.warn} ${e}`,...t)}function su(e,...t){console.info(`${It.info} ${e}`,...t)}function au(e,...t){console.error(`${It.error} ${e}`,...t)}function lu(e,...t){console.log(`${It.query} ${e}`,...t)}function Te(e,t){throw new Error(t)}function Sr(e){let t;return(...r)=>t||(t=e(...r).catch(n=>{throw t=void 0,n}),t)}var $t=K(require("path"));function Kn(e){return $t.default.sep===$t.default.posix.sep?e:e.split($t.default.sep).join($t.default.posix.sep)}function Qn(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var Jn=(e,t)=>e.reduce((r,n)=>(r[t(n)]=n,r),{});function ct(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}function Gn(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}function C(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var Do=new Set,Nt=(e,t,...r)=>{Do.has(e)||(Do.add(e),Un(t,...r))};var pe=class extends Error{constructor(r,n,i){super(r);this.name="PrismaClientInitializationError",this.clientVersion=n,this.errorCode=i,Error.captureStackTrace(pe)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};C(pe,"PrismaClientInitializationError");var ne=class extends Error{constructor(r,{code:n,clientVersion:i,meta:o,batchRequestIdx:s}){super(r);this.name="PrismaClientKnownRequestError",this.code=n,this.clientVersion=i,this.meta=o,Object.defineProperty(this,"batchRequestIdx",{value:s,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};C(ne,"PrismaClientKnownRequestError");var Ne=class extends Error{constructor(r,n){super(r);this.name="PrismaClientRustPanicError",this.clientVersion=n}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};C(Ne,"PrismaClientRustPanicError");var he=class extends Error{constructor(r,{clientVersion:n,batchRequestIdx:i}){super(r);this.name="PrismaClientUnknownRequestError",this.clientVersion=n,Object.defineProperty(this,"batchRequestIdx",{value:i,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};C(he,"PrismaClientUnknownRequestError");var pt=class{constructor(t){this._engine=t}prometheus(t){return this._engine.metrics({format:"prometheus",...t})}json(t){return this._engine.metrics({format:"json",...t})}};function _t(e){let t;return{get(){return t||(t={value:e()}),t.value}}}function Oo(e){return{models:Wn(e.models),enums:Wn(e.enums),types:Wn(e.types)}}function Wn(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}function So(e,t){let r=_t(()=>pu(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function pu(e){return{datamodel:{models:Hn(e.models),enums:Hn(e.enums),types:Hn(e.types)}}}function Hn(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}function Io(e,t){for(let r of t)for(let n of Object.getOwnPropertyNames(r.prototype))Object.defineProperty(e.prototype,n,Object.getOwnPropertyDescriptor(r.prototype,n)??Object.create(null))}var dt=9e15,Je=1e9,zn="0123456789abcdef",kr="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",$r="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",Yn={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-dt,maxE:dt,crypto:!1},_o,_e,F=!0,_r="[DecimalError] ",Qe=_r+"Invalid argument: ",Lo=_r+"Precision limit exceeded",qo=_r+"crypto unavailable",jo="[object Decimal]",ie=Math.floor,Q=Math.pow,du=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,mu=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,fu=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,Bo=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Me=1e7,v=7,gu=9007199254740991,yu=kr.length-1,Zn=$r.length-1,w={toStringTag:jo};w.absoluteValue=w.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),M(e)};w.ceil=function(){return M(new this.constructor(this),this.e+1,2)};w.clampedTo=w.clamp=function(e,t){var r,n=this,i=n.constructor;if(e=new i(e),t=new i(t),!e.s||!t.s)return new i(NaN);if(e.gt(t))throw Error(Qe+t);return r=n.cmp(e),r<0?e:n.cmp(t)>0?t:new i(n)};w.comparedTo=w.cmp=function(e){var t,r,n,i,o=this,s=o.d,a=(e=new o.constructor(e)).d,l=o.s,u=e.s;if(!s||!a)return!l||!u?NaN:l!==u?l:s===a?0:!s^l<0?1:-1;if(!s[0]||!a[0])return s[0]?l:a[0]?-u:0;if(l!==u)return l;if(o.e!==e.e)return o.e>e.e^l<0?1:-1;for(n=s.length,i=a.length,t=0,r=n<i?n:i;t<r;++t)if(s[t]!==a[t])return s[t]>a[t]^l<0?1:-1;return n===i?0:n>i^l<0?1:-1};w.cosine=w.cos=function(){var e,t,r=this,n=r.constructor;return r.d?r.d[0]?(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+v,n.rounding=1,r=hu(n,Jo(n,r)),n.precision=e,n.rounding=t,M(_e==2||_e==3?r.neg():r,e,t,!0)):new n(1):new n(NaN)};w.cubeRoot=w.cbrt=function(){var e,t,r,n,i,o,s,a,l,u,c=this,p=c.constructor;if(!c.isFinite()||c.isZero())return new p(c);for(F=!1,o=c.s*Q(c.s*c,1/3),!o||Math.abs(o)==1/0?(r=Y(c.d),e=c.e,(o=(e-r.length+1)%3)&&(r+=o==1||o==-2?"0":"00"),o=Q(r,1/3),e=ie((e+1)/3)-(e%3==(e<0?-1:2)),o==1/0?r="5e"+e:(r=o.toExponential(),r=r.slice(0,r.indexOf("e")+1)+e),n=new p(r),n.s=c.s):n=new p(o.toString()),s=(e=p.precision)+3;;)if(a=n,l=a.times(a).times(a),u=l.plus(c),n=_(u.plus(c).times(a),u.plus(l),s+2,1),Y(a.d).slice(0,s)===(r=Y(n.d)).slice(0,s))if(r=r.slice(s-3,s+1),r=="9999"||!i&&r=="4999"){if(!i&&(M(a,e+1,0),a.times(a).times(a).eq(c))){n=a;break}s+=4,i=1}else{(!+r||!+r.slice(1)&&r.charAt(0)=="5")&&(M(n,e+1,1),t=!n.times(n).times(n).eq(c));break}return F=!0,M(n,e,p.rounding,t)};w.decimalPlaces=w.dp=function(){var e,t=this.d,r=NaN;if(t){if(e=t.length-1,r=(e-ie(this.e/v))*v,e=t[e],e)for(;e%10==0;e/=10)r--;r<0&&(r=0)}return r};w.dividedBy=w.div=function(e){return _(this,new this.constructor(e))};w.dividedToIntegerBy=w.divToInt=function(e){var t=this,r=t.constructor;return M(_(t,new r(e),0,1,1),r.precision,r.rounding)};w.equals=w.eq=function(e){return this.cmp(e)===0};w.floor=function(){return M(new this.constructor(this),this.e+1,3)};w.greaterThan=w.gt=function(e){return this.cmp(e)>0};w.greaterThanOrEqualTo=w.gte=function(e){var t=this.cmp(e);return t==1||t===0};w.hyperbolicCosine=w.cosh=function(){var e,t,r,n,i,o=this,s=o.constructor,a=new s(1);if(!o.isFinite())return new s(o.s?1/0:NaN);if(o.isZero())return a;r=s.precision,n=s.rounding,s.precision=r+Math.max(o.e,o.sd())+4,s.rounding=1,i=o.d.length,i<32?(e=Math.ceil(i/3),t=(1/qr(4,e)).toString()):(e=16,t="2.3283064365386962890625e-10"),o=mt(s,1,o.times(t),new s(1),!0);for(var l,u=e,c=new s(8);u--;)l=o.times(o),o=a.minus(l.times(c.minus(l.times(c))));return M(o,s.precision=r,s.rounding=n,!0)};w.hyperbolicSine=w.sinh=function(){var e,t,r,n,i=this,o=i.constructor;if(!i.isFinite()||i.isZero())return new o(i);if(t=o.precision,r=o.rounding,o.precision=t+Math.max(i.e,i.sd())+4,o.rounding=1,n=i.d.length,n<3)i=mt(o,2,i,i,!0);else{e=1.4*Math.sqrt(n),e=e>16?16:e|0,i=i.times(1/qr(5,e)),i=mt(o,2,i,i,!0);for(var s,a=new o(5),l=new o(16),u=new o(20);e--;)s=i.times(i),i=i.times(a.plus(s.times(l.times(s).plus(u))))}return o.precision=t,o.rounding=r,M(i,t,r,!0)};w.hyperbolicTangent=w.tanh=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+7,n.rounding=1,_(r.sinh(),r.cosh(),n.precision=e,n.rounding=t)):new n(r.s)};w.inverseCosine=w.acos=function(){var e,t=this,r=t.constructor,n=t.abs().cmp(1),i=r.precision,o=r.rounding;return n!==-1?n===0?t.isNeg()?Pe(r,i,o):new r(0):new r(NaN):t.isZero()?Pe(r,i+4,o).times(.5):(r.precision=i+6,r.rounding=1,t=t.asin(),e=Pe(r,i+4,o).times(.5),r.precision=i,r.rounding=o,e.minus(t))};w.inverseHyperbolicCosine=w.acosh=function(){var e,t,r=this,n=r.constructor;return r.lte(1)?new n(r.eq(1)?0:NaN):r.isFinite()?(e=n.precision,t=n.rounding,n.precision=e+Math.max(Math.abs(r.e),r.sd())+4,n.rounding=1,F=!1,r=r.times(r).minus(1).sqrt().plus(r),F=!0,n.precision=e,n.rounding=t,r.ln()):new n(r)};w.inverseHyperbolicSine=w.asinh=function(){var e,t,r=this,n=r.constructor;return!r.isFinite()||r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+2*Math.max(Math.abs(r.e),r.sd())+6,n.rounding=1,F=!1,r=r.times(r).plus(1).sqrt().plus(r),F=!0,n.precision=e,n.rounding=t,r.ln())};w.inverseHyperbolicTangent=w.atanh=function(){var e,t,r,n,i=this,o=i.constructor;return i.isFinite()?i.e>=0?new o(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=o.precision,t=o.rounding,n=i.sd(),Math.max(n,e)<2*-i.e-1?M(new o(i),e,t,!0):(o.precision=r=n-i.e,i=_(i.plus(1),new o(1).minus(i),r+e,1),o.precision=e+4,o.rounding=1,i=i.ln(),o.precision=e,o.rounding=t,i.times(.5))):new o(NaN)};w.inverseSine=w.asin=function(){var e,t,r,n,i=this,o=i.constructor;return i.isZero()?new o(i):(t=i.abs().cmp(1),r=o.precision,n=o.rounding,t!==-1?t===0?(e=Pe(o,r+4,n).times(.5),e.s=i.s,e):new o(NaN):(o.precision=r+6,o.rounding=1,i=i.div(new o(1).minus(i.times(i)).sqrt().plus(1)).atan(),o.precision=r,o.rounding=n,i.times(2)))};w.inverseTangent=w.atan=function(){var e,t,r,n,i,o,s,a,l,u=this,c=u.constructor,p=c.precision,d=c.rounding;if(u.isFinite()){if(u.isZero())return new c(u);if(u.abs().eq(1)&&p+4<=Zn)return s=Pe(c,p+4,d).times(.25),s.s=u.s,s}else{if(!u.s)return new c(NaN);if(p+4<=Zn)return s=Pe(c,p+4,d).times(.5),s.s=u.s,s}for(c.precision=a=p+10,c.rounding=1,r=Math.min(28,a/v+2|0),e=r;e;--e)u=u.div(u.times(u).plus(1).sqrt().plus(1));for(F=!1,t=Math.ceil(a/v),n=1,l=u.times(u),s=new c(u),i=u;e!==-1;)if(i=i.times(l),o=s.minus(i.div(n+=2)),i=i.times(l),s=o.plus(i.div(n+=2)),s.d[t]!==void 0)for(e=t;s.d[e]===o.d[e]&&e--;);return r&&(s=s.times(2<<r-1)),F=!0,M(s,c.precision=p,c.rounding=d,!0)};w.isFinite=function(){return!!this.d};w.isInteger=w.isInt=function(){return!!this.d&&ie(this.e/v)>this.d.length-2};w.isNaN=function(){return!this.s};w.isNegative=w.isNeg=function(){return this.s<0};w.isPositive=w.isPos=function(){return this.s>0};w.isZero=function(){return!!this.d&&this.d[0]===0};w.lessThan=w.lt=function(e){return this.cmp(e)<0};w.lessThanOrEqualTo=w.lte=function(e){return this.cmp(e)<1};w.logarithm=w.log=function(e){var t,r,n,i,o,s,a,l,u=this,c=u.constructor,p=c.precision,d=c.rounding,m=5;if(e==null)e=new c(10),t=!0;else{if(e=new c(e),r=e.d,e.s<0||!r||!r[0]||e.eq(1))return new c(NaN);t=e.eq(10)}if(r=u.d,u.s<0||!r||!r[0]||u.eq(1))return new c(r&&!r[0]?-1/0:u.s!=1?NaN:r?0:1/0);if(t)if(r.length>1)o=!0;else{for(i=r[0];i%10===0;)i/=10;o=i!==1}if(F=!1,a=p+m,s=Ke(u,a),n=t?Nr(c,a+10):Ke(e,a),l=_(s,n,a,1),Lt(l.d,i=p,d))do if(a+=10,s=Ke(u,a),n=t?Nr(c,a+10):Ke(e,a),l=_(s,n,a,1),!o){+Y(l.d).slice(i+1,i+15)+1==1e14&&(l=M(l,p+1,0));break}while(Lt(l.d,i+=10,d));return F=!0,M(l,p,d)};w.minus=w.sub=function(e){var t,r,n,i,o,s,a,l,u,c,p,d,m=this,g=m.constructor;if(e=new g(e),!m.d||!e.d)return!m.s||!e.s?e=new g(NaN):m.d?e.s=-e.s:e=new g(e.d||m.s!==e.s?m:NaN),e;if(m.s!=e.s)return e.s=-e.s,m.plus(e);if(u=m.d,d=e.d,a=g.precision,l=g.rounding,!u[0]||!d[0]){if(d[0])e.s=-e.s;else if(u[0])e=new g(m);else return new g(l===3?-0:0);return F?M(e,a,l):e}if(r=ie(e.e/v),c=ie(m.e/v),u=u.slice(),o=c-r,o){for(p=o<0,p?(t=u,o=-o,s=d.length):(t=d,r=c,s=u.length),n=Math.max(Math.ceil(a/v),s)+2,o>n&&(o=n,t.length=1),t.reverse(),n=o;n--;)t.push(0);t.reverse()}else{for(n=u.length,s=d.length,p=n<s,p&&(s=n),n=0;n<s;n++)if(u[n]!=d[n]){p=u[n]<d[n];break}o=0}for(p&&(t=u,u=d,d=t,e.s=-e.s),s=u.length,n=d.length-s;n>0;--n)u[s++]=0;for(n=d.length;n>o;){if(u[--n]<d[n]){for(i=n;i&&u[--i]===0;)u[i]=Me-1;--u[i],u[n]+=Me}u[n]-=d[n]}for(;u[--s]===0;)u.pop();for(;u[0]===0;u.shift())--r;return u[0]?(e.d=u,e.e=Lr(u,r),F?M(e,a,l):e):new g(l===3?-0:0)};w.modulo=w.mod=function(e){var t,r=this,n=r.constructor;return e=new n(e),!r.d||!e.s||e.d&&!e.d[0]?new n(NaN):!e.d||r.d&&!r.d[0]?M(new n(r),n.precision,n.rounding):(F=!1,n.modulo==9?(t=_(r,e.abs(),0,3,1),t.s*=e.s):t=_(r,e,0,n.modulo,1),t=t.times(e),F=!0,r.minus(t))};w.naturalExponential=w.exp=function(){return Xn(this)};w.naturalLogarithm=w.ln=function(){return Ke(this)};w.negated=w.neg=function(){var e=new this.constructor(this);return e.s=-e.s,M(e)};w.plus=w.add=function(e){var t,r,n,i,o,s,a,l,u,c,p=this,d=p.constructor;if(e=new d(e),!p.d||!e.d)return!p.s||!e.s?e=new d(NaN):p.d||(e=new d(e.d||p.s===e.s?p:NaN)),e;if(p.s!=e.s)return e.s=-e.s,p.minus(e);if(u=p.d,c=e.d,a=d.precision,l=d.rounding,!u[0]||!c[0])return c[0]||(e=new d(p)),F?M(e,a,l):e;if(o=ie(p.e/v),n=ie(e.e/v),u=u.slice(),i=o-n,i){for(i<0?(r=u,i=-i,s=c.length):(r=c,n=o,s=u.length),o=Math.ceil(a/v),s=o>s?o+1:s+1,i>s&&(i=s,r.length=1),r.reverse();i--;)r.push(0);r.reverse()}for(s=u.length,i=c.length,s-i<0&&(i=s,r=c,c=u,u=r),t=0;i;)t=(u[--i]=u[i]+c[i]+t)/Me|0,u[i]%=Me;for(t&&(u.unshift(t),++n),s=u.length;u[--s]==0;)u.pop();return e.d=u,e.e=Lr(u,n),F?M(e,a,l):e};w.precision=w.sd=function(e){var t,r=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Qe+e);return r.d?(t=Vo(r.d),e&&r.e+1>t&&(t=r.e+1)):t=NaN,t};w.round=function(){var e=this,t=e.constructor;return M(new t(e),e.e+1,t.rounding)};w.sine=w.sin=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+v,n.rounding=1,r=Eu(n,Jo(n,r)),n.precision=e,n.rounding=t,M(_e>2?r.neg():r,e,t,!0)):new n(NaN)};w.squareRoot=w.sqrt=function(){var e,t,r,n,i,o,s=this,a=s.d,l=s.e,u=s.s,c=s.constructor;if(u!==1||!a||!a[0])return new c(!u||u<0&&(!a||a[0])?NaN:a?s:1/0);for(F=!1,u=Math.sqrt(+s),u==0||u==1/0?(t=Y(a),(t.length+l)%2==0&&(t+="0"),u=Math.sqrt(t),l=ie((l+1)/2)-(l<0||l%2),u==1/0?t="5e"+l:(t=u.toExponential(),t=t.slice(0,t.indexOf("e")+1)+l),n=new c(t)):n=new c(u.toString()),r=(l=c.precision)+3;;)if(o=n,n=o.plus(_(s,o,r+2,1)).times(.5),Y(o.d).slice(0,r)===(t=Y(n.d)).slice(0,r))if(t=t.slice(r-3,r+1),t=="9999"||!i&&t=="4999"){if(!i&&(M(o,l+1,0),o.times(o).eq(s))){n=o;break}r+=4,i=1}else{(!+t||!+t.slice(1)&&t.charAt(0)=="5")&&(M(n,l+1,1),e=!n.times(n).eq(s));break}return F=!0,M(n,l,c.rounding,e)};w.tangent=w.tan=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+10,n.rounding=1,r=r.sin(),r.s=1,r=_(r,new n(1).minus(r.times(r)).sqrt(),e+10,0),n.precision=e,n.rounding=t,M(_e==2||_e==4?r.neg():r,e,t,!0)):new n(NaN)};w.times=w.mul=function(e){var t,r,n,i,o,s,a,l,u,c=this,p=c.constructor,d=c.d,m=(e=new p(e)).d;if(e.s*=c.s,!d||!d[0]||!m||!m[0])return new p(!e.s||d&&!d[0]&&!m||m&&!m[0]&&!d?NaN:!d||!m?e.s/0:e.s*0);for(r=ie(c.e/v)+ie(e.e/v),l=d.length,u=m.length,l<u&&(o=d,d=m,m=o,s=l,l=u,u=s),o=[],s=l+u,n=s;n--;)o.push(0);for(n=u;--n>=0;){for(t=0,i=l+n;i>n;)a=o[i]+m[n]*d[i-n-1]+t,o[i--]=a%Me|0,t=a/Me|0;o[i]=(o[i]+t)%Me|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=Lr(o,r),F?M(e,p.precision,p.rounding):e};w.toBinary=function(e,t){return ti(this,2,e,t)};w.toDecimalPlaces=w.toDP=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(de(e,0,Je),t===void 0?t=n.rounding:de(t,0,8),M(r,e+r.e+1,t))};w.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Re(n,!0):(de(e,0,Je),t===void 0?t=i.rounding:de(t,0,8),n=M(new i(n),e+1,t),r=Re(n,!0,e+1)),n.isNeg()&&!n.isZero()?"-"+r:r};w.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?r=Re(i):(de(e,0,Je),t===void 0?t=o.rounding:de(t,0,8),n=M(new o(i),e+i.e+1,t),r=Re(n,!1,e+n.e+1)),i.isNeg()&&!i.isZero()?"-"+r:r};w.toFraction=function(e){var t,r,n,i,o,s,a,l,u,c,p,d,m=this,g=m.d,f=m.constructor;if(!g)return new f(m);if(u=r=new f(1),n=l=new f(0),t=new f(n),o=t.e=Vo(g)-m.e-1,s=o%v,t.d[0]=Q(10,s<0?v+s:s),e==null)e=o>0?t:u;else{if(a=new f(e),!a.isInt()||a.lt(u))throw Error(Qe+a);e=a.gt(t)?o>0?t:u:a}for(F=!1,a=new f(Y(g)),c=f.precision,f.precision=o=g.length*v*2;p=_(a,t,0,1,1),i=r.plus(p.times(n)),i.cmp(e)!=1;)r=n,n=i,i=u,u=l.plus(p.times(i)),l=i,i=t,t=a.minus(p.times(i)),a=i;return i=_(e.minus(r),n,0,1,1),l=l.plus(i.times(u)),r=r.plus(i.times(n)),l.s=u.s=m.s,d=_(u,n,o,1).minus(m).abs().cmp(_(l,r,o,1).minus(m).abs())<1?[u,n]:[l,r],f.precision=c,F=!0,d};w.toHexadecimal=w.toHex=function(e,t){return ti(this,16,e,t)};w.toNearest=function(e,t){var r=this,n=r.constructor;if(r=new n(r),e==null){if(!r.d)return r;e=new n(1),t=n.rounding}else{if(e=new n(e),t===void 0?t=n.rounding:de(t,0,8),!r.d)return e.s?r:e;if(!e.d)return e.s&&(e.s=r.s),e}return e.d[0]?(F=!1,r=_(r,e,0,t,1).times(e),F=!0,M(r)):(e.s=r.s,r=e),r};w.toNumber=function(){return+this};w.toOctal=function(e,t){return ti(this,8,e,t)};w.toPower=w.pow=function(e){var t,r,n,i,o,s,a=this,l=a.constructor,u=+(e=new l(e));if(!a.d||!e.d||!a.d[0]||!e.d[0])return new l(Q(+a,u));if(a=new l(a),a.eq(1))return a;if(n=l.precision,o=l.rounding,e.eq(1))return M(a,n,o);if(t=ie(e.e/v),t>=e.d.length-1&&(r=u<0?-u:u)<=gu)return i=Uo(l,a,r,n),e.s<0?new l(1).div(i):M(i,n,o);if(s=a.s,s<0){if(t<e.d.length-1)return new l(NaN);if((e.d[t]&1)==0&&(s=1),a.e==0&&a.d[0]==1&&a.d.length==1)return a.s=s,a}return r=Q(+a,u),t=r==0||!isFinite(r)?ie(u*(Math.log("0."+Y(a.d))/Math.LN10+a.e+1)):new l(r+"").e,t>l.maxE+1||t<l.minE-1?new l(t>0?s/0:0):(F=!1,l.rounding=a.s=1,r=Math.min(12,(t+"").length),i=Xn(e.times(Ke(a,n+r)),n),i.d&&(i=M(i,n+5,1),Lt(i.d,n,o)&&(t=n+10,i=M(Xn(e.times(Ke(a,t+r)),t),t+5,1),+Y(i.d).slice(n+1,n+15)+1==1e14&&(i=M(i,n+1,0)))),i.s=s,F=!0,l.rounding=o,M(i,n,o))};w.toPrecision=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Re(n,n.e<=i.toExpNeg||n.e>=i.toExpPos):(de(e,1,Je),t===void 0?t=i.rounding:de(t,0,8),n=M(new i(n),e,t),r=Re(n,e<=n.e||n.e<=i.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+r:r};w.toSignificantDigits=w.toSD=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(de(e,1,Je),t===void 0?t=n.rounding:de(t,0,8)),M(new n(r),e,t)};w.toString=function(){var e=this,t=e.constructor,r=Re(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()&&!e.isZero()?"-"+r:r};w.truncated=w.trunc=function(){return M(new this.constructor(this),this.e+1,1)};w.valueOf=w.toJSON=function(){var e=this,t=e.constructor,r=Re(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()?"-"+r:r};function Y(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=v-n.length,r&&(o+=Ue(r)),o+=n;s=e[t],n=s+"",r=v-n.length,r&&(o+=Ue(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function de(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Qe+e)}function Lt(e,t,r,n){var i,o,s,a;for(o=e[0];o>=10;o/=10)--t;return--t<0?(t+=v,i=0):(i=Math.ceil((t+1)/v),t%=v),o=Q(10,v-t),a=e[i]%o|0,n==null?t<3?(t==0?a=a/100|0:t==1&&(a=a/10|0),s=r<4&&a==99999||r>3&&a==49999||a==5e4||a==0):s=(r<4&&a+1==o||r>3&&a+1==o/2)&&(e[i+1]/o/100|0)==Q(10,t-2)-1||(a==o/2||a==0)&&(e[i+1]/o/100|0)==0:t<4?(t==0?a=a/1e3|0:t==1?a=a/100|0:t==2&&(a=a/10|0),s=(n||r<4)&&a==9999||!n&&r>3&&a==4999):s=((n||r<4)&&a+1==o||!n&&r>3&&a+1==o/2)&&(e[i+1]/o/1e3|0)==Q(10,t-3)-1,s}function Ir(e,t,r){for(var n,i=[0],o,s=0,a=e.length;s<a;){for(o=i.length;o--;)i[o]*=t;for(i[0]+=zn.indexOf(e.charAt(s++)),n=0;n<i.length;n++)i[n]>r-1&&(i[n+1]===void 0&&(i[n+1]=0),i[n+1]+=i[n]/r|0,i[n]%=r)}return i.reverse()}function hu(e,t){var r,n,i;if(t.isZero())return t;n=t.d.length,n<32?(r=Math.ceil(n/3),i=(1/qr(4,r)).toString()):(r=16,i="2.3283064365386962890625e-10"),e.precision+=r,t=mt(e,1,t.times(i),new e(1));for(var o=r;o--;){var s=t.times(t);t=s.times(s).minus(s).times(8).plus(1)}return e.precision-=r,t}var _=function(){function e(n,i,o){var s,a=0,l=n.length;for(n=n.slice();l--;)s=n[l]*i+a,n[l]=s%o|0,a=s/o|0;return a&&n.unshift(a),n}function t(n,i,o,s){var a,l;if(o!=s)l=o>s?1:-1;else for(a=l=0;a<o;a++)if(n[a]!=i[a]){l=n[a]>i[a]?1:-1;break}return l}function r(n,i,o,s){for(var a=0;o--;)n[o]-=a,a=n[o]<i[o]?1:0,n[o]=a*s+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s,a,l){var u,c,p,d,m,g,f,E,x,b,h,y,T,A,P,k,B,V,ee,nt,xr=n.constructor,Cn=n.s==i.s?1:-1,te=n.d,N=i.d;if(!te||!te[0]||!N||!N[0])return new xr(!n.s||!i.s||(te?N&&te[0]==N[0]:!N)?NaN:te&&te[0]==0||!N?Cn*0:Cn/0);for(l?(m=1,c=n.e-i.e):(l=Me,m=v,c=ie(n.e/m)-ie(i.e/m)),ee=N.length,B=te.length,x=new xr(Cn),b=x.d=[],p=0;N[p]==(te[p]||0);p++);if(N[p]>(te[p]||0)&&c--,o==null?(A=o=xr.precision,s=xr.rounding):a?A=o+(n.e-i.e)+1:A=o,A<0)b.push(1),g=!0;else{if(A=A/m+2|0,p=0,ee==1){for(d=0,N=N[0],A++;(p<B||d)&&A--;p++)P=d*l+(te[p]||0),b[p]=P/N|0,d=P%N|0;g=d||p<B}else{for(d=l/(N[0]+1)|0,d>1&&(N=e(N,d,l),te=e(te,d,l),ee=N.length,B=te.length),k=ee,h=te.slice(0,ee),y=h.length;y<ee;)h[y++]=0;nt=N.slice(),nt.unshift(0),V=N[0],N[1]>=l/2&&++V;do d=0,u=t(N,h,ee,y),u<0?(T=h[0],ee!=y&&(T=T*l+(h[1]||0)),d=T/V|0,d>1?(d>=l&&(d=l-1),f=e(N,d,l),E=f.length,y=h.length,u=t(f,h,E,y),u==1&&(d--,r(f,ee<E?nt:N,E,l))):(d==0&&(u=d=1),f=N.slice()),E=f.length,E<y&&f.unshift(0),r(h,f,y,l),u==-1&&(y=h.length,u=t(N,h,ee,y),u<1&&(d++,r(h,ee<y?nt:N,y,l))),y=h.length):u===0&&(d++,h=[0]),b[p++]=d,u&&h[0]?h[y++]=te[k]||0:(h=[te[k]],y=1);while((k++<B||h[0]!==void 0)&&A--);g=h[0]!==void 0}b[0]||b.shift()}if(m==1)x.e=c,_o=g;else{for(p=1,d=b[0];d>=10;d/=10)p++;x.e=p+c*m-1,M(x,a?o+x.e+1:o,s,g)}return x}}();function M(e,t,r,n){var i,o,s,a,l,u,c,p,d,m=e.constructor;e:if(t!=null){if(p=e.d,!p)return e;for(i=1,a=p[0];a>=10;a/=10)i++;if(o=t-i,o<0)o+=v,s=t,c=p[d=0],l=c/Q(10,i-s-1)%10|0;else if(d=Math.ceil((o+1)/v),a=p.length,d>=a)if(n){for(;a++<=d;)p.push(0);c=l=0,i=1,o%=v,s=o-v+1}else break e;else{for(c=a=p[d],i=1;a>=10;a/=10)i++;o%=v,s=o-v+i,l=s<0?0:c/Q(10,i-s-1)%10|0}if(n=n||t<0||p[d+1]!==void 0||(s<0?c:c%Q(10,i-s-1)),u=r<4?(l||n)&&(r==0||r==(e.s<0?3:2)):l>5||l==5&&(r==4||n||r==6&&(o>0?s>0?c/Q(10,i-s):0:p[d-1])%10&1||r==(e.s<0?8:7)),t<1||!p[0])return p.length=0,u?(t-=e.e+1,p[0]=Q(10,(v-t%v)%v),e.e=-t||0):p[0]=e.e=0,e;if(o==0?(p.length=d,a=1,d--):(p.length=d+1,a=Q(10,v-o),p[d]=s>0?(c/Q(10,i-s)%Q(10,s)|0)*a:0),u)for(;;)if(d==0){for(o=1,s=p[0];s>=10;s/=10)o++;for(s=p[0]+=a,a=1;s>=10;s/=10)a++;o!=a&&(e.e++,p[0]==Me&&(p[0]=1));break}else{if(p[d]+=a,p[d]!=Me)break;p[d--]=0,a=1}for(o=p.length;p[--o]===0;)p.pop()}return F&&(e.e>m.maxE?(e.d=null,e.e=NaN):e.e<m.minE&&(e.e=0,e.d=[0])),e}function Re(e,t,r){if(!e.isFinite())return Qo(e);var n,i=e.e,o=Y(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+Ue(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(e.e<0?"e":"e+")+e.e):i<0?(o="0."+Ue(-i-1)+o,r&&(n=r-s)>0&&(o+=Ue(n))):i>=s?(o+=Ue(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+Ue(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=Ue(n))),o}function Lr(e,t){var r=e[0];for(t*=v;r>=10;r/=10)t++;return t}function Nr(e,t,r){if(t>yu)throw F=!0,r&&(e.precision=r),Error(Lo);return M(new e(kr),t,1,!0)}function Pe(e,t,r){if(t>Zn)throw Error(Lo);return M(new e($r),t,r,!0)}function Vo(e){var t=e.length-1,r=t*v+1;if(t=e[t],t){for(;t%10==0;t/=10)r--;for(t=e[0];t>=10;t/=10)r++}return r}function Ue(e){for(var t="";e--;)t+="0";return t}function Uo(e,t,r,n){var i,o=new e(1),s=Math.ceil(n/v+4);for(F=!1;;){if(r%2&&(o=o.times(t),$o(o.d,s)&&(i=!0)),r=ie(r/2),r===0){r=o.d.length-1,i&&o.d[r]===0&&++o.d[r];break}t=t.times(t),$o(t.d,s)}return F=!0,o}function ko(e){return e.d[e.d.length-1]&1}function Ko(e,t,r){for(var n,i=new e(t[0]),o=0;++o<t.length;)if(n=new e(t[o]),n.s)i[r](n)&&(i=n);else{i=n;break}return i}function Xn(e,t){var r,n,i,o,s,a,l,u=0,c=0,p=0,d=e.constructor,m=d.rounding,g=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:0/0);for(t==null?(F=!1,l=g):l=t,a=new d(.03125);e.e>-2;)e=e.times(a),p+=5;for(n=Math.log(Q(2,p))/Math.LN10*2+5|0,l+=n,r=o=s=new d(1),d.precision=l;;){if(o=M(o.times(e),l,1),r=r.times(++c),a=s.plus(_(o,r,l,1)),Y(a.d).slice(0,l)===Y(s.d).slice(0,l)){for(i=p;i--;)s=M(s.times(s),l,1);if(t==null)if(u<3&&Lt(s.d,l-n,m,u))d.precision=l+=10,r=o=a=new d(1),c=0,u++;else return M(s,d.precision=g,m,F=!0);else return d.precision=g,s}s=a}}function Ke(e,t){var r,n,i,o,s,a,l,u,c,p,d,m=1,g=10,f=e,E=f.d,x=f.constructor,b=x.rounding,h=x.precision;if(f.s<0||!E||!E[0]||!f.e&&E[0]==1&&E.length==1)return new x(E&&!E[0]?-1/0:f.s!=1?NaN:E?0:f);if(t==null?(F=!1,c=h):c=t,x.precision=c+=g,r=Y(E),n=r.charAt(0),Math.abs(o=f.e)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)f=f.times(e),r=Y(f.d),n=r.charAt(0),m++;o=f.e,n>1?(f=new x("0."+r),o++):f=new x(n+"."+r.slice(1))}else return u=Nr(x,c+2,h).times(o+""),f=Ke(new x(n+"."+r.slice(1)),c-g).plus(u),x.precision=h,t==null?M(f,h,b,F=!0):f;for(p=f,l=s=f=_(f.minus(1),f.plus(1),c,1),d=M(f.times(f),c,1),i=3;;){if(s=M(s.times(d),c,1),u=l.plus(_(s,new x(i),c,1)),Y(u.d).slice(0,c)===Y(l.d).slice(0,c))if(l=l.times(2),o!==0&&(l=l.plus(Nr(x,c+2,h).times(o+""))),l=_(l,new x(m),c,1),t==null)if(Lt(l.d,c-g,b,a))x.precision=c+=g,u=s=f=_(p.minus(1),p.plus(1),c,1),d=M(f.times(f),c,1),i=a=1;else return M(l,x.precision=h,b,F=!0);else return x.precision=h,l;l=u,i+=2}}function Qo(e){return String(e.s*e.s/0)}function ei(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;n++);for(i=t.length;t.charCodeAt(i-1)===48;--i);if(t=t.slice(n,i),t){if(i-=n,e.e=r=r-n-1,e.d=[],n=(r+1)%v,r<0&&(n+=v),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=v;n<i;)e.d.push(+t.slice(n,n+=v));t=t.slice(n),n=v-t.length}else n-=i;for(;n--;)t+="0";e.d.push(+t),F&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function xu(e,t){var r,n,i,o,s,a,l,u,c;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),Bo.test(t))return ei(e,t)}else if(t==="Infinity"||t==="NaN")return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(mu.test(t))r=16,t=t.toLowerCase();else if(du.test(t))r=2;else if(fu.test(t))r=8;else throw Error(Qe+t);for(o=t.search(/p/i),o>0?(l=+t.slice(o+1),t=t.substring(2,o)):t=t.slice(2),o=t.indexOf("."),s=o>=0,n=e.constructor,s&&(t=t.replace(".",""),a=t.length,o=a-o,i=Uo(n,new n(r),o,o*2)),u=Ir(t,r,Me),c=u.length-1,o=c;u[o]===0;--o)u.pop();return o<0?new n(e.s*0):(e.e=Lr(u,c),e.d=u,F=!1,s&&(e=_(e,i,a*4)),l&&(e=e.times(Math.abs(l)<54?Q(2,l):Ze.pow(2,l))),F=!0,e)}function Eu(e,t){var r,n=t.d.length;if(n<3)return t.isZero()?t:mt(e,2,t,t);r=1.4*Math.sqrt(n),r=r>16?16:r|0,t=t.times(1/qr(5,r)),t=mt(e,2,t,t);for(var i,o=new e(5),s=new e(16),a=new e(20);r--;)i=t.times(t),t=t.times(o.plus(i.times(s.times(i).minus(a))));return t}function mt(e,t,r,n,i){var o,s,a,l,u=1,c=e.precision,p=Math.ceil(c/v);for(F=!1,l=r.times(r),a=new e(n);;){if(s=_(a.times(l),new e(t++*t++),c,1),a=i?n.plus(s):n.minus(s),n=_(s.times(l),new e(t++*t++),c,1),s=a.plus(n),s.d[p]!==void 0){for(o=p;s.d[o]===a.d[o]&&o--;);if(o==-1)break}o=a,a=n,n=s,s=o,u++}return F=!0,s.d.length=p+1,s}function qr(e,t){for(var r=e;--t;)r*=e;return r}function Jo(e,t){var r,n=t.s<0,i=Pe(e,e.precision,1),o=i.times(.5);if(t=t.abs(),t.lte(o))return _e=n?4:1,t;if(r=t.divToInt(i),r.isZero())_e=n?3:2;else{if(t=t.minus(r.times(i)),t.lte(o))return _e=ko(r)?n?2:3:n?4:1,t;_e=ko(r)?n?1:4:n?3:2}return t.minus(i).abs()}function ti(e,t,r,n){var i,o,s,a,l,u,c,p,d,m=e.constructor,g=r!==void 0;if(g?(de(r,1,Je),n===void 0?n=m.rounding:de(n,0,8)):(r=m.precision,n=m.rounding),!e.isFinite())c=Qo(e);else{for(c=Re(e),s=c.indexOf("."),g?(i=2,t==16?r=r*4-3:t==8&&(r=r*3-2)):i=t,s>=0&&(c=c.replace(".",""),d=new m(1),d.e=c.length-s,d.d=Ir(Re(d),10,i),d.e=d.d.length),p=Ir(c,10,i),o=l=p.length;p[--l]==0;)p.pop();if(!p[0])c=g?"0p+0":"0";else{if(s<0?o--:(e=new m(e),e.d=p,e.e=o,e=_(e,d,r,n,0,i),p=e.d,o=e.e,u=_o),s=p[r],a=i/2,u=u||p[r+1]!==void 0,u=n<4?(s!==void 0||u)&&(n===0||n===(e.s<0?3:2)):s>a||s===a&&(n===4||u||n===6&&p[r-1]&1||n===(e.s<0?8:7)),p.length=r,u)for(;++p[--r]>i-1;)p[r]=0,r||(++o,p.unshift(1));for(l=p.length;!p[l-1];--l);for(s=0,c="";s<l;s++)c+=zn.charAt(p[s]);if(g){if(l>1)if(t==16||t==8){for(s=t==16?4:3,--l;l%s;l++)c+="0";for(p=Ir(c,i,t),l=p.length;!p[l-1];--l);for(s=1,c="1.";s<l;s++)c+=zn.charAt(p[s])}else c=c.charAt(0)+"."+c.slice(1);c=c+(o<0?"p":"p+")+o}else if(o<0){for(;++o;)c="0"+c;c="0."+c}else if(++o>l)for(o-=l;o--;)c+="0";else o<l&&(c=c.slice(0,o)+"."+c.slice(o))}c=(t==16?"0x":t==2?"0b":t==8?"0o":"")+c}return e.s<0?"-"+c:c}function $o(e,t){if(e.length>t)return e.length=t,!0}function bu(e){return new this(e).abs()}function wu(e){return new this(e).acos()}function Tu(e){return new this(e).acosh()}function Pu(e,t){return new this(e).plus(t)}function Mu(e){return new this(e).asin()}function Au(e){return new this(e).asinh()}function vu(e){return new this(e).atan()}function Cu(e){return new this(e).atanh()}function Fu(e,t){e=new this(e),t=new this(t);var r,n=this.precision,i=this.rounding,o=n+4;return!e.s||!t.s?r=new this(NaN):!e.d&&!t.d?(r=Pe(this,o,1).times(t.s>0?.25:.75),r.s=e.s):!t.d||e.isZero()?(r=t.s<0?Pe(this,n,i):new this(0),r.s=e.s):!e.d||t.isZero()?(r=Pe(this,o,1).times(.5),r.s=e.s):t.s<0?(this.precision=o,this.rounding=1,r=this.atan(_(e,t,o,1)),t=Pe(this,o,1),this.precision=n,this.rounding=i,r=e.s<0?r.minus(t):r.plus(t)):r=this.atan(_(e,t,o,1)),r}function Ru(e){return new this(e).cbrt()}function Du(e){return M(e=new this(e),e.e+1,2)}function Ou(e,t,r){return new this(e).clamp(t,r)}function Su(e){if(!e||typeof e!="object")throw Error(_r+"Object expected");var t,r,n,i=e.defaults===!0,o=["precision",1,Je,"rounding",0,8,"toExpNeg",-dt,0,"toExpPos",0,dt,"maxE",0,dt,"minE",-dt,0,"modulo",0,9];for(t=0;t<o.length;t+=3)if(r=o[t],i&&(this[r]=Yn[r]),(n=e[r])!==void 0)if(ie(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(Qe+r+": "+n);if(r="crypto",i&&(this[r]=Yn[r]),(n=e[r])!==void 0)if(n===!0||n===!1||n===0||n===1)if(n)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[r]=!0;else throw Error(qo);else this[r]=!1;else throw Error(Qe+r+": "+n);return this}function Iu(e){return new this(e).cos()}function ku(e){return new this(e).cosh()}function Go(e){var t,r,n;function i(o){var s,a,l,u=this;if(!(u instanceof i))return new i(o);if(u.constructor=i,No(o)){u.s=o.s,F?!o.d||o.e>i.maxE?(u.e=NaN,u.d=null):o.e<i.minE?(u.e=0,u.d=[0]):(u.e=o.e,u.d=o.d.slice()):(u.e=o.e,u.d=o.d?o.d.slice():o.d);return}if(l=typeof o,l==="number"){if(o===0){u.s=1/o<0?-1:1,u.e=0,u.d=[0];return}if(o<0?(o=-o,u.s=-1):u.s=1,o===~~o&&o<1e7){for(s=0,a=o;a>=10;a/=10)s++;F?s>i.maxE?(u.e=NaN,u.d=null):s<i.minE?(u.e=0,u.d=[0]):(u.e=s,u.d=[o]):(u.e=s,u.d=[o]);return}else if(o*0!==0){o||(u.s=NaN),u.e=NaN,u.d=null;return}return ei(u,o.toString())}else if(l!=="string")throw Error(Qe+o);return(a=o.charCodeAt(0))===45?(o=o.slice(1),u.s=-1):(a===43&&(o=o.slice(1)),u.s=1),Bo.test(o)?ei(u,o):xu(u,o)}if(i.prototype=w,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.EUCLID=9,i.config=i.set=Su,i.clone=Go,i.isDecimal=No,i.abs=bu,i.acos=wu,i.acosh=Tu,i.add=Pu,i.asin=Mu,i.asinh=Au,i.atan=vu,i.atanh=Cu,i.atan2=Fu,i.cbrt=Ru,i.ceil=Du,i.clamp=Ou,i.cos=Iu,i.cosh=ku,i.div=$u,i.exp=Nu,i.floor=_u,i.hypot=Lu,i.ln=qu,i.log=ju,i.log10=Vu,i.log2=Bu,i.max=Uu,i.min=Ku,i.mod=Qu,i.mul=Ju,i.pow=Gu,i.random=Wu,i.round=Hu,i.sign=zu,i.sin=Yu,i.sinh=Zu,i.sqrt=Xu,i.sub=ec,i.sum=tc,i.tan=rc,i.tanh=nc,i.trunc=ic,e===void 0&&(e={}),e&&e.defaults!==!0)for(n=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function $u(e,t){return new this(e).div(t)}function Nu(e){return new this(e).exp()}function _u(e){return M(e=new this(e),e.e+1,3)}function Lu(){var e,t,r=new this(0);for(F=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)r.d&&(r=r.plus(t.times(t)));else{if(t.s)return F=!0,new this(1/0);r=t}return F=!0,r.sqrt()}function No(e){return e instanceof Ze||e&&e.toStringTag===jo||!1}function qu(e){return new this(e).ln()}function ju(e,t){return new this(e).log(t)}function Bu(e){return new this(e).log(2)}function Vu(e){return new this(e).log(10)}function Uu(){return Ko(this,arguments,"lt")}function Ku(){return Ko(this,arguments,"gt")}function Qu(e,t){return new this(e).mod(t)}function Ju(e,t){return new this(e).mul(t)}function Gu(e,t){return new this(e).pow(t)}function Wu(e){var t,r,n,i,o=0,s=new this(1),a=[];if(e===void 0?e=this.precision:de(e,1,Je),n=Math.ceil(e/v),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(n));o<n;)i=t[o],i>=429e7?t[o]=crypto.getRandomValues(new Uint32Array(1))[0]:a[o++]=i%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(n*=4);o<n;)i=t[o]+(t[o+1]<<8)+(t[o+2]<<16)+((t[o+3]&127)<<24),i>=214e7?crypto.randomBytes(4).copy(t,o):(a.push(i%1e7),o+=4);o=n/4}else throw Error(qo);else for(;o<n;)a[o++]=Math.random()*1e7|0;for(n=a[--o],e%=v,n&&e&&(i=Q(10,v-e),a[o]=(n/i|0)*i);a[o]===0;o--)a.pop();if(o<0)r=0,a=[0];else{for(r=-1;a[0]===0;r-=v)a.shift();for(n=1,i=a[0];i>=10;i/=10)n++;n<v&&(r-=v-n)}return s.e=r,s.d=a,s}function Hu(e){return M(e=new this(e),e.e+1,this.rounding)}function zu(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function Yu(e){return new this(e).sin()}function Zu(e){return new this(e).sinh()}function Xu(e){return new this(e).sqrt()}function ec(e,t){return new this(e).sub(t)}function tc(){var e=0,t=arguments,r=new this(t[e]);for(F=!1;r.s&&++e<t.length;)r=r.plus(t[e]);return F=!0,M(r,this.precision,this.rounding)}function rc(e){return new this(e).tan()}function nc(e){return new this(e).tanh()}function ic(e){return M(e=new this(e),e.e+1,1)}w[Symbol.for("nodejs.util.inspect.custom")]=w.toString;w[Symbol.toStringTag]="Decimal";var Ze=w.constructor=Go(Yn);kr=new Ze(kr);$r=new Ze($r);var le=Ze;var ii=K(Dr()),zo=K(jr());var xe=class{constructor(t,r,n,i,o){this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function ft(e){return e instanceof xe}var Ho=["JsonNullValueInput","NullableJsonNullValueInput","JsonNullValueFilter"],Br=Symbol(),ri=new WeakMap,W=class{constructor(t){t===Br?ri.set(this,`Prisma.${this._getName()}`):ri.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return ri.get(this)}},qt=class extends W{_getNamespace(){return"NullTypes"}},jt=class extends qt{};ni(jt,"DbNull");var Bt=class extends qt{};ni(Bt,"JsonNull");var Vt=class extends qt{};ni(Vt,"AnyNull");var gt={classes:{DbNull:jt,JsonNull:Bt,AnyNull:Vt},instances:{DbNull:new jt(Br),JsonNull:new Bt(Br),AnyNull:new Vt(Br)}};function ni(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}function ue(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function De(e){return e.toString()!=="Invalid Date"}function Oe(e){return Ze.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}var oe=(e,t)=>{let r={};for(let n of e){let i=n[t];r[i]=n}return r},yt={String:!0,Int:!0,Float:!0,Boolean:!0,Long:!0,DateTime:!0,ID:!0,UUID:!0,Json:!0,Bytes:!0,Decimal:!0,BigInt:!0};var oc={string:"String",boolean:"Boolean",object:"Json",symbol:"Symbol"};function ht(e){return typeof e=="string"?e:e.name}function Kt(e,t){return t?`List<${e}>`:e}var sc=/^(\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60))(\.\d{1,})?(([Z])|([+|-]([01][0-9]|2[0-3]):[0-5][0-9]))$/,ac=/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;function xt(e,t){let r=t?.type;if(e===null)return"null";if(Object.prototype.toString.call(e)==="[object BigInt]")return"BigInt";if(le.isDecimal(e)||r==="Decimal"&&Oe(e))return"Decimal";if(Buffer.isBuffer(e))return"Bytes";if(lc(e,t))return r.name;if(e instanceof W)return e._getName();if(e instanceof xe)return e._toGraphQLInputType();if(Array.isArray(e)){let i=e.reduce((o,s)=>{let a=xt(s,t);return o.includes(a)||o.push(a),o},[]);return i.includes("Float")&&i.includes("Int")&&(i=["Float"]),`List<${i.join(" | ")}>`}let n=typeof e;if(n==="number")return Math.trunc(e)===e?"Int":"Float";if(ue(e))return"DateTime";if(n==="string"){if(ac.test(e))return"UUID";if(new Date(e).toString()==="Invalid Date")return"String";if(sc.test(e))return"DateTime"}return oc[n]}function lc(e,t){let r=t?.type;if(!cc(r))return!1;if(t?.namespace==="prisma"&&Ho.includes(r.name)){let n=e?.constructor?.name;return typeof n=="string"&&gt.instances[n]===e&&r.values.includes(n)}return typeof e=="string"&&r.values.includes(e)}function Vr(e,t){return t.reduce((n,i)=>{let o=(0,zo.default)(e,i);return o<n.distance?{distance:o,str:i}:n},{distance:Math.min(Math.floor(e.length)*1.1,...t.map(n=>n.length*3)),str:null}).str}function Et(e,t=!1){if(typeof e=="string")return e;if(e.values)return`enum ${e.name} {
${(0,ii.default)(e.values.join(", "),2)}
}`;{let r=(0,ii.default)(e.fields.map(n=>{let i=`${n.name}`,o=`${t?O(i):i}${n.isRequired?"":"?"}: ${Dt(n.inputTypes.map(s=>Kt(uc(s.type)?s.type.name:ht(s.type),s.isList)).join(" | "))}`;return n.isRequired?o:$(o)}).join(`
`),2);return`${$("type")} ${R($(e.name))} ${$("{")}
${r}
${$("}")}`}}function uc(e){return typeof e!="string"}function Ut(e){return typeof e=="string"?e==="Null"?"null":e:e.name}function Qt(e){return typeof e=="string"?e:e.name}function oi(e,t,r=!1){if(typeof e=="string")return e==="Null"?"null":e;if(e.values)return e.values.join(" | ");let n=e,i=t&&n.fields.every(o=>o.inputTypes[0].location==="inputObjectTypes"||o.inputTypes[1]?.location==="inputObjectTypes");return r?Ut(e):n.fields.reduce((o,s)=>{let a="";return!i&&!s.isRequired?a=s.inputTypes.map(l=>Ut(l.type)).join(" | "):a=s.inputTypes.map(l=>oi(l.type,s.isRequired,!0)).join(" | "),o[s.name+(s.isRequired?"":"?")]=a,o},{})}function Yo(e,t,r){let n={};for(let i of e)n[r(i)]=i;for(let i of t){let o=r(i);n[o]||(n[o]=i)}return Object.values(n)}function bt(e){return e.substring(0,1).toLowerCase()+e.substring(1)}function Zo(e){return e.endsWith("GroupByOutputType")}function cc(e){return typeof e=="object"&&e!==null&&typeof e.name=="string"&&Array.isArray(e.values)}var Ur=class{constructor({datamodel:t}){this.datamodel=t,this.datamodelEnumMap=this.getDatamodelEnumMap(),this.modelMap=this.getModelMap(),this.typeMap=this.getTypeMap(),this.typeAndModelMap=this.getTypeModelMap()}getDatamodelEnumMap(){return oe(this.datamodel.enums,"name")}getModelMap(){return{...oe(this.datamodel.models,"name")}}getTypeMap(){return{...oe(this.datamodel.types,"name")}}getTypeModelMap(){return{...this.getTypeMap(),...this.getModelMap()}}},Kr=class{constructor({mappings:t}){this.mappings=t,this.mappingsMap=this.getMappingsMap()}getMappingsMap(){return oe(this.mappings.modelOperations,"model")}getOtherOperationNames(){return[Object.values(this.mappings.otherOperations.write),Object.values(this.mappings.otherOperations.read)].flat()}},Qr=class{constructor({schema:t}){this.outputTypeToMergedOutputType=t=>({...t,fields:t.fields});this.schema=t,this.enumMap=this.getEnumMap(),this.queryType=this.getQueryType(),this.mutationType=this.getMutationType(),this.outputTypes=this.getOutputTypes(),this.outputTypeMap=this.getMergedOutputTypeMap(),this.resolveOutputTypes(),this.inputObjectTypes=this.schema.inputObjectTypes,this.inputTypeMap=this.getInputTypeMap(),this.resolveInputTypes(),this.resolveFieldArgumentTypes(),this.queryType=this.outputTypeMap.Query,this.mutationType=this.outputTypeMap.Mutation,this.rootFieldMap=this.getRootFieldMap()}get[Symbol.toStringTag](){return"DMMFClass"}resolveOutputTypes(){for(let t of this.outputTypes.model){for(let r of t.fields)typeof r.outputType.type=="string"&&!yt[r.outputType.type]&&(r.outputType.type=this.outputTypeMap[r.outputType.type]||this.outputTypeMap[r.outputType.type]||this.enumMap[r.outputType.type]||r.outputType.type);t.fieldMap=oe(t.fields,"name")}for(let t of this.outputTypes.prisma){for(let r of t.fields)typeof r.outputType.type=="string"&&!yt[r.outputType.type]&&(r.outputType.type=this.outputTypeMap[r.outputType.type]||this.outputTypeMap[r.outputType.type]||this.enumMap[r.outputType.type]||r.outputType.type);t.fieldMap=oe(t.fields,"name")}}resolveInputTypes(){let t=this.inputObjectTypes.prisma;this.inputObjectTypes.model&&t.push(...this.inputObjectTypes.model);for(let r of t){for(let n of r.fields)for(let i of n.inputTypes){let o=i.type;typeof o=="string"&&!yt[o]&&(this.inputTypeMap[o]||this.enumMap[o])&&(i.type=this.inputTypeMap[o]||this.enumMap[o]||o)}r.fieldMap=oe(r.fields,"name")}}resolveFieldArgumentTypes(){for(let t of this.outputTypes.prisma)for(let r of t.fields)for(let n of r.args)for(let i of n.inputTypes){let o=i.type;typeof o=="string"&&!yt[o]&&(i.type=this.inputTypeMap[o]||this.enumMap[o]||o)}for(let t of this.outputTypes.model)for(let r of t.fields)for(let n of r.args)for(let i of n.inputTypes){let o=i.type;typeof o=="string"&&!yt[o]&&(i.type=this.inputTypeMap[o]||this.enumMap[o]||i.type)}}getQueryType(){return this.schema.outputObjectTypes.prisma.find(t=>t.name==="Query")}getMutationType(){return this.schema.outputObjectTypes.prisma.find(t=>t.name==="Mutation")}getOutputTypes(){return{model:this.schema.outputObjectTypes.model.map(this.outputTypeToMergedOutputType),prisma:this.schema.outputObjectTypes.prisma.map(this.outputTypeToMergedOutputType)}}getEnumMap(){return{...oe(this.schema.enumTypes.prisma,"name"),...this.schema.enumTypes.model?oe(this.schema.enumTypes.model,"name"):void 0}}hasEnumInNamespace(t,r){return this.schema.enumTypes[r]?.find(n=>n.name===t)!==void 0}getMergedOutputTypeMap(){return{...oe(this.outputTypes.model,"name"),...oe(this.outputTypes.prisma,"name")}}getInputTypeMap(){return{...this.schema.inputObjectTypes.model?oe(this.schema.inputObjectTypes.model,"name"):void 0,...oe(this.schema.inputObjectTypes.prisma,"name")}}getRootFieldMap(){return{...oe(this.queryType.fields,"name"),...oe(this.mutationType.fields,"name")}}},Ge=class{constructor(t){return Object.assign(this,new Ur(t),new Kr(t),new Qr(t))}};Io(Ge,[Ur,Kr,Qr]);var il=require("async_hooks"),ol=require("events"),sl=K(require("fs")),hr=K(require("path"));var Z=class{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof Z?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=t[i];if(s instanceof Z){this.strings[o]+=s.strings[0];let l=0;for(;l<s.values.length;)this.values[o++]=s.values[l++],this.strings[o]=s.strings[l];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get text(){let t=1,r=this.strings[0];for(;t<this.strings.length;)r+=`$${t}${this.strings[t++]}`;return r}get sql(){let t=1,r=this.strings[0];for(;t<this.strings.length;)r+=`?${this.strings[t++]}`;return r}inspect(){return{text:this.text,sql:this.sql,values:this.values}}};function Xo(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new Z([r,...Array(e.length-1).fill(t),n],e)}function si(e){return new Z([e],[])}var es=si("");function ai(e,...t){return new Z(e,t)}var rs=K(ts());function ns(e){return{...e,mappings:pc(e.mappings,e.datamodel)}}function pc(e,t){return{modelOperations:e.modelOperations.filter(n=>{let i=t.models.find(o=>o.name===n.model);if(!i)throw new Error(`Mapping without model ${n.model}`);return i.fields.some(o=>o.kind!=="object")}).map(n=>({model:n.model,plural:(0,rs.default)(bt(n.model)),findUnique:n.findUnique||n.findSingle,findUniqueOrThrow:n.findUniqueOrThrow,findFirst:n.findFirst,findFirstOrThrow:n.findFirstOrThrow,findMany:n.findMany,create:n.createOne||n.createSingle||n.create,createMany:n.createMany,delete:n.deleteOne||n.deleteSingle||n.delete,update:n.updateOne||n.updateSingle||n.update,deleteMany:n.deleteMany,updateMany:n.updateMany,upsert:n.upsertOne||n.upsertSingle||n.upsert,aggregate:n.aggregate,groupBy:n.groupBy,findRaw:n.findRaw,aggregateRaw:n.aggregateRaw})),otherOperations:e.otherOperations}}function is(e){return ns(e)}function Jt(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}function Ae(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}var Se=class{constructor(){this._map=new Map}get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};function Xe(e){let t=new Se;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}var as=require("util");var Jr={enumerable:!0,configurable:!0,writable:!0};function Gr(e){let t=new Set(e);return{getOwnPropertyDescriptor:()=>Jr,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var os=Symbol.for("nodejs.util.inspect.custom");function Ie(e,t){let r=dc(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=ss(Reflect.ownKeys(o),r),a=ss(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let l=r.get(s);return l?l.getPropertyDescriptor?{...Jr,...l?.getPropertyDescriptor(s)}:Jr:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)}});return i[os]=function(o,s,a=as.inspect){let l={...this};return delete l[os],a(l,s)},i}function dc(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function ss(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}function Gt(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}function ci({error:e,user_facing_error:t},r){return t.error_code?new ne(t.message,{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new he(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}var Wr=class{};function ls(e,t){return mc(e)?!t||t.kind==="itx"?{batch:e,transaction:!1}:{batch:e,transaction:!0,isolationLevel:t.options.isolationLevel}:{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}function mc(e){return typeof e[0].query=="string"}var Hr=class extends Error{constructor(r,n){super(r);this.clientVersion=n.clientVersion,this.cause=n.cause}get[Symbol.toStringTag](){return this.name}};var me=class extends Hr{constructor(r,n){super(r,n);this.isRetryable=n.isRetryable??!0}};function I(e,t){return{...e,isRetryable:t}}var wt=class extends me{constructor(r){super("This request must be retried",I(r,!0));this.name="ForcedRetryError";this.code="P5001"}};C(wt,"ForcedRetryError");var Le=class extends me{constructor(r,n){super(r,I(n,!1));this.name="InvalidDatasourceError";this.code="P5002"}};C(Le,"InvalidDatasourceError");var qe=class extends me{constructor(r,n){super(r,I(n,!1));this.name="NotImplementedYetError";this.code="P5004"}};C(qe,"NotImplementedYetError");var q=class extends me{constructor(r,n){super(r,n);this.response=n.response;let i=this.response.headers?.["prisma-request-id"];if(i){let o=`(The request id was: ${i})`;this.message=this.message+" "+o}}};var et=class extends q{constructor(r){super("Schema needs to be uploaded",I(r,!0));this.name="SchemaMissingError";this.code="P5005"}};C(et,"SchemaMissingError");var pi="This request could not be understood by the server",Wt=class extends q{constructor(r,n,i){super(n||pi,I(r,!1));this.name="BadRequestError";this.code="P5000";i&&(this.code=i)}};C(Wt,"BadRequestError");var Ht=class extends q{constructor(r,n){super("Engine not started: healthcheck timeout",I(r,!0));this.name="HealthcheckTimeoutError";this.code="P5013";this.logs=n}};C(Ht,"HealthcheckTimeoutError");var zt=class extends q{constructor(r,n,i){super(n,I(r,!0));this.name="EngineStartupError";this.code="P5014";this.logs=i}};C(zt,"EngineStartupError");var Yt=class extends q{constructor(r){super("Engine version is not supported",I(r,!1));this.name="EngineVersionNotSupportedError";this.code="P5012"}};C(Yt,"EngineVersionNotSupportedError");var di="Request timed out",Zt=class extends q{constructor(r,n=di){super(n,I(r,!1));this.name="GatewayTimeoutError";this.code="P5009"}};C(Zt,"GatewayTimeoutError");var fc="Interactive transaction error",Xt=class extends q{constructor(r,n=fc){super(n,I(r,!1));this.name="InteractiveTransactionError";this.code="P5015"}};C(Xt,"InteractiveTransactionError");var gc="Request parameters are invalid",er=class extends q{constructor(r,n=gc){super(n,I(r,!1));this.name="InvalidRequestError";this.code="P5011"}};C(er,"InvalidRequestError");var mi="Requested resource does not exist",tr=class extends q{constructor(r,n=mi){super(n,I(r,!1));this.name="NotFoundError";this.code="P5003"}};C(tr,"NotFoundError");var fi="Unknown server error",Tt=class extends q{constructor(r,n,i){super(n||fi,I(r,!0));this.name="ServerError";this.code="P5006";this.logs=i}};C(Tt,"ServerError");var gi="Unauthorized, check your connection string",rr=class extends q{constructor(r,n=gi){super(n,I(r,!1));this.name="UnauthorizedError";this.code="P5007"}};C(rr,"UnauthorizedError");var yi="Usage exceeded, retry again later",nr=class extends q{constructor(r,n=yi){super(n,I(r,!0));this.name="UsageExceededError";this.code="P5008"}};C(nr,"UsageExceededError");async function yc(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function ir(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await yc(e);if(n.type==="QueryEngineError")throw new ne(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new Tt(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new et(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new Yt(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new zt(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new pe(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new Ht(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new Xt(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new er(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new rr(r,Pt(gi,n));if(e.status===404)return new tr(r,Pt(mi,n));if(e.status===429)throw new nr(r,Pt(yi,n));if(e.status===504)throw new Zt(r,Pt(di,n));if(e.status>=500)throw new Tt(r,Pt(fi,n));if(e.status>=400)throw new Wt(r,Pt(pi,n))}function Pt(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}function us(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}var cs={"@prisma/debug":"workspace:*","@prisma/engines-version":"4.16.1-1.4bc8b6e1b66cb932731fb1bdbbc550d1e010de81","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*","@swc/core":"1.3.64","@swc/jest":"0.2.26","@types/jest":"29.5.2","@types/node":"18.16.16",execa:"5.1.1",jest:"29.5.0",typescript:"4.9.5"};var or=class extends me{constructor(r,n){super(`Cannot fetch data from service:
${r}`,I(n,!0));this.name="RequestError";this.code="P5010"}};C(or,"RequestError");function ps(){return typeof self>"u"?"node":"browser"}async function tt(e,t,r=n=>n){let n=t.clientVersion,i=ps();try{return i==="browser"?await r(fetch)(e,t):await r(hi)(e,t)}catch(o){let s=o.message??"Unknown error";throw new or(s,{clientVersion:n})}}function xc(e){return{...e.headers,"Content-Type":"application/json"}}function Ec(e){return{method:e.method,headers:xc(e)}}function bc(e,t){return{text:()=>Promise.resolve(Buffer.concat(e).toString()),json:()=>Promise.resolve(JSON.parse(Buffer.concat(e).toString())),ok:t.statusCode>=200&&t.statusCode<=299,status:t.statusCode,url:t.url,headers:t.headers}}async function hi(e,t={}){let r=wc("https"),n=Ec(t),i=[],{origin:o}=new URL(e);return new Promise((s,a)=>{let l=r.request(e,n,u=>{let{statusCode:c,headers:{location:p}}=u;c>=301&&c<=399&&p&&(p.startsWith("http")===!1?s(hi(`${o}${p}`,t)):s(hi(p,t))),u.on("data",d=>i.push(d)),u.on("end",()=>s(bc(i,u))),u.on("error",a)});l.on("error",a),l.end(t.body??"")})}var wc=typeof require<"u"?require:()=>{};var Tc=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,ds=re("prisma:client:dataproxyEngine");async function Pc(e){let t=cs["@prisma/engines-version"],r=e.clientVersion??"unknown";if(process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;let[n,i]=r?.split("-")??[];if(i===void 0&&Tc.test(n))return n;if(i!==void 0||r==="0.0.0"){if(process.env.TEST_DATA_PROXY!==void 0)return"0.0.0";let[o]=t.split("-")??[],[s,a,l]=o.split("."),u=Mc(`<=${s}.${a}.${l}`),c=await tt(u,{clientVersion:r});if(!c.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${c.status} ${c.statusText}, response body: ${await c.text()||"<empty body>"}`);let p=await c.text();ds("length of body fetched from unpkg.com",p.length);let d;try{d=JSON.parse(p)}catch(m){throw console.error("JSON.parse error: body fetched from unpkg.com: ",p),m}return d.version}throw new qe("Only `major.minor.patch` versions are supported by Prisma Data Proxy.",{clientVersion:r})}async function ms(e){let t=await Pc(e);return ds("version",t),t}function Mc(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var fs=3,Ac=Promise.resolve(),xi=re("prisma:client:dataproxyEngine"),Ei=class{constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i}){this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i}build({traceparent:t,interactiveTransaction:r}={}){let n={Authorization:`Bearer ${this.apiKey}`};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-transaction-id"]=r.id);let i=this.buildCaptureSettings();return i.length>0&&(n["X-capture-telemetry"]=i.join(", ")),n}buildCaptureSettings(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}},sr=class extends Wr{constructor(r){super();this.config=r,this.env={...this.config.env,...process.env},this.inlineSchema=r.inlineSchema??"",this.inlineDatasources=r.inlineDatasources??{},this.inlineSchemaHash=r.inlineSchemaHash??"",this.clientVersion=r.clientVersion??"unknown",this.logEmitter=r.logEmitter,this.tracingHelper=this.config.tracingHelper;let[n,i]=this.extractHostAndApiKey();this.host=n,this.headerBuilder=new Ei({apiKey:i,tracingHelper:this.tracingHelper,logLevel:r.logLevel,logQueries:r.logQueries}),this.remoteClientVersion=Ac.then(()=>ms(this.config)),xi("host",this.host)}apiKey(){return this.headerBuilder.apiKey}version(){return"unknown"}async start(){}async stop(){}propagateResponseExtensions(r){r?.logs?.length&&r.logs.forEach(n=>{switch(n.level){case"debug":case"error":case"trace":case"warn":case"info":break;case"query":{let i=typeof n.attributes.query=="string"?n.attributes.query:"";if(!this.tracingHelper.isEnabled()){let[o]=i.split("/* traceparent");i=o}this.logEmitter.emit("query",{query:i,timestamp:n.timestamp,duration:n.attributes.duration_ms,params:n.attributes.params,target:n.attributes.target})}}}),r?.traces?.length&&this.tracingHelper.createEngineSpan({span:!0,spans:r.traces})}on(r,n){if(r==="beforeExit")throw new qe("beforeExit event is not yet supported",{clientVersion:this.clientVersion});this.logEmitter.on(r,n)}async url(r){return`https://${this.host}/${await this.remoteClientVersion}/${this.inlineSchemaHash}/${r}`}getDmmf(){throw new qe("getDmmf is not yet supported",{clientVersion:this.clientVersion})}async uploadSchema(){let r={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(r,async()=>{let n=await tt(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});n.ok||xi("schema response status",n.status);let i=await ir(n,this.clientVersion);if(i)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${i.message}`}),i;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`})})}request(r,{traceparent:n,interactiveTransaction:i,customDataProxyFetch:o}){return this.requestInternal({body:r,traceparent:n,interactiveTransaction:i,customDataProxyFetch:o})}async requestBatch(r,{traceparent:n,transaction:i,customDataProxyFetch:o}){let s=i?.kind==="itx"?i.options:void 0,a=ls(r,i),{batchResult:l,elapsed:u}=await this.requestInternal({body:a,customDataProxyFetch:o,interactiveTransaction:s,traceparent:n});return l.map(c=>"errors"in c&&c.errors.length>0?ci(c.errors[0],this.clientVersion):{data:c,elapsed:u})}requestInternal({body:r,traceparent:n,customDataProxyFetch:i,interactiveTransaction:o}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:s})=>{let a=o?`${o.payload.endpoint}/graphql`:await this.url("graphql");s(a);let l=await tt(a,{method:"POST",headers:this.headerBuilder.build({traceparent:n,interactiveTransaction:o}),body:JSON.stringify(r),clientVersion:this.clientVersion},i);l.ok||xi("graphql response status",l.status);let u=await ir(l,this.clientVersion);await this.handleError(u);let c=await l.json(),p=c.extensions;if(p&&this.propagateResponseExtensions(p),c.errors)throw c.errors.length===1?ci(c.errors[0],this.config.clientVersion):new he(c.errors,{clientVersion:this.config.clientVersion});return c}})}async transaction(r,n,i){let o={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${o[r]} transaction`,callback:async({logHttpCall:s})=>{if(r==="start"){let a=JSON.stringify({max_wait:i?.maxWait??2e3,timeout:i?.timeout??5e3,isolation_level:i?.isolationLevel}),l=await this.url("transaction/start");s(l);let u=await tt(l,{method:"POST",headers:this.headerBuilder.build({traceparent:n.traceparent}),body:a,clientVersion:this.clientVersion}),c=await ir(u,this.clientVersion);await this.handleError(c);let p=await u.json(),d=p.extensions;d&&this.propagateResponseExtensions(d);let m=p.id,g=p["data-proxy"].endpoint;return{id:m,payload:{endpoint:g}}}else{let a=`${i.payload.endpoint}/${r}`;s(a);let l=await tt(a,{method:"POST",headers:this.headerBuilder.build({traceparent:n.traceparent}),clientVersion:this.clientVersion}),c=(await l.json()).extensions;c&&this.propagateResponseExtensions(c);let p=await ir(l,this.clientVersion);await this.handleError(p);return}}})}extractHostAndApiKey(){let r=this.mergeOverriddenDatasources(),n=Object.keys(r)[0],i=r[n],o=this.resolveDatasourceURL(n,i),s;try{s=new URL(o)}catch{throw new Le("Could not parse URL of the datasource",{clientVersion:this.clientVersion})}let{protocol:a,host:l,searchParams:u}=s;if(a!=="prisma:")throw new Le("Datasource URL must use prisma:// protocol when --data-proxy is used",{clientVersion:this.clientVersion});let c=u.get("api_key");if(c===null||c.length<1)throw new Le("No valid API key found in the datasource URL",{clientVersion:this.clientVersion});return[l,c]}mergeOverriddenDatasources(){if(this.config.datasources===void 0)return this.inlineDatasources;let r={...this.inlineDatasources};for(let n of this.config.datasources){if(!this.inlineDatasources[n.name])throw new Error(`Unknown datasource: ${n.name}`);r[n.name]={url:{fromEnvVar:null,value:n.url}}}return r}resolveDatasourceURL(r,n){if(n.url.value)return n.url.value;if(n.url.fromEnvVar){let i=n.url.fromEnvVar,o=this.env[i];if(o===void 0)throw new Le(`Datasource "${r}" references an environment variable "${i}" that is not set`,{clientVersion:this.clientVersion});return o}throw new Le(`Datasource "${r}" specification is invalid: both value and fromEnvVar are null`,{clientVersion:this.clientVersion})}metrics(){throw new qe("Metric are not yet supported for Data Proxy",{clientVersion:this.clientVersion})}async withRetry(r){for(let n=0;;n++){let i=o=>{this.logEmitter.emit("info",{message:`Calling ${o} (n=${n})`})};try{return await r.callback({logHttpCall:i})}catch(o){if(!(o instanceof me)||!o.isRetryable)throw o;if(n>=fs)throw o instanceof wt?o.cause:o;this.logEmitter.emit("warn",{message:`Attempt ${n+1}/${fs} failed for ${r.actionGerund}: ${o.message??"(unknown)"}`});let s=await us(n);this.logEmitter.emit("warn",{message:`Retrying after ${s}ms`})}}}async handleError(r){if(r instanceof et)throw await this.uploadSchema(),new wt({clientVersion:this.clientVersion,cause:r});if(r)throw r}};var ar="<unknown>";function gs(e){var t=e.split(`
`);return t.reduce(function(r,n){var i=Fc(n)||Dc(n)||Ic(n)||_c(n)||$c(n);return i&&r.push(i),r},[])}var vc=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Cc=/\((\S*)(?::(\d+))(?::(\d+))\)/;function Fc(e){var t=vc.exec(e);if(!t)return null;var r=t[2]&&t[2].indexOf("native")===0,n=t[2]&&t[2].indexOf("eval")===0,i=Cc.exec(t[2]);return n&&i!=null&&(t[2]=i[1],t[3]=i[2],t[4]=i[3]),{file:r?null:t[2],methodName:t[1]||ar,arguments:r?[t[2]]:[],lineNumber:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}var Rc=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;function Dc(e){var t=Rc.exec(e);return t?{file:t[2],methodName:t[1]||ar,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var Oc=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,Sc=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i;function Ic(e){var t=Oc.exec(e);if(!t)return null;var r=t[3]&&t[3].indexOf(" > eval")>-1,n=Sc.exec(t[3]);return r&&n!=null&&(t[3]=n[1],t[4]=n[2],t[5]=null),{file:t[3],methodName:t[1]||ar,arguments:t[2]?t[2].split(","):[],lineNumber:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}var kc=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i;function $c(e){var t=kc.exec(e);return t?{file:t[3],methodName:t[1]||ar,arguments:[],lineNumber:+t[4],column:t[5]?+t[5]:null}:null}var Nc=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i;function _c(e){var t=Nc.exec(e);return t?{file:t[2],methodName:t[1]||ar,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var bi=class{getLocation(){return null}},wi=class{constructor(){this._error=new Error}getLocation(){let t=this._error.stack;if(!t)return null;let n=gs(t).find(i=>{if(!i.file)return!1;let o=Kn(i.file);return o!=="<anonymous>"&&!o.includes("@prisma")&&!o.includes("/packages/client/src/runtime/")&&!o.endsWith("/runtime/binary.js")&&!o.endsWith("/runtime/library.js")&&!o.endsWith("/runtime/data-proxy.js")&&!o.endsWith("/runtime/edge.js")&&!o.endsWith("/runtime/edge-esm.js")&&!o.startsWith("internal/")&&!i.methodName.includes("new ")&&!i.methodName.includes("getCallSite")&&!i.methodName.includes("Proxy.")&&i.methodName.split(".").length<4});return!n||!n.file?null:{fileName:n.file,lineNumber:n.lineNumber,columnNumber:n.column}}};function We(e){return e==="minimal"?new bi:new wi}var ys={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function Mt(e={}){let t=qc(e);return Object.entries(t).reduce((n,[i,o])=>(ys[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function qc(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function zr(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function hs(e,t){let r=zr(e);return t({action:"aggregate",unpacker:r,argsMapper:Mt})(e)}function jc(e={}){let{select:t,...r}=e;return typeof t=="object"?Mt({...r,_count:t}):Mt({...r,_count:{_all:!0}})}function Bc(e={}){return typeof e.select=="object"?t=>zr(e)(t)._count:t=>zr(e)(t)._count._all}function xs(e,t){return t({action:"count",unpacker:Bc(e),argsMapper:jc})(e)}function Vc(e={}){let t=Mt(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);return t}function Uc(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function Es(e,t){return t({action:"groupBy",unpacker:Uc(e),argsMapper:Vc})(e)}function bs(e,t,r){if(t==="aggregate")return n=>hs(n,r);if(t==="count")return n=>xs(n,r);if(t==="groupBy")return n=>Es(n,r)}function ws(e,t){let r=t.fields.filter(i=>!i.relationName),n=Jn(r,i=>i.name);return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new xe(e,o,s.type,s.isList,s.kind==="enum")},...Gr(Object.keys(n))})}var Ts=e=>Array.isArray(e)?e:e.split("."),lr=(e,t)=>Ts(t).reduce((r,n)=>r&&r[n],e),Yr=(e,t,r)=>Ts(t).reduceRight((n,i,o,s)=>Object.assign({},lr(e,s.slice(0,o)),{[i]:n}),r);function Kc(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function Qc(e,t,r){return t===void 0?e??{}:Yr(t,r,e||!0)}function Ti(e,t,r,n,i,o){let a=e._runtimeDataModel.models[t].fields.reduce((l,u)=>({...l,[u.name]:u}),{});return l=>{let u=We(e._errorFormat),c=Kc(n,i),p=Qc(l,o,c),d=r({dataPath:c,callsite:u})(p),m=Jc(e,t);return new Proxy(d,{get(g,f){if(!m.includes(f))return g[f];let x=[a[f].type,r,f],b=[c,p];return Ti(e,...x,...b)},...Gr([...m,...Object.getOwnPropertyNames(d)])})}}function Jc(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var rt=K(Dr());var Fi=K(Or());function Ee(e){return e.replace(/^./,t=>t.toLowerCase())}function Ms(e,t,r){let n=Ee(r);return!t.result||!(t.result.$allModels||t.result[n])?e:Gc({...e,...Ps(t.name,e,t.result.$allModels),...Ps(t.name,e,t.result[n])})}function Gc(e){let t=new Se,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return ct(e,n=>({...n,needs:r(n.name,new Set)}))}function Ps(e,t,r){return r?ct(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:Wc(t,o,i)})):{}}function Wc(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function Zr(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!!e[n.name])for(let i of n.needs)r[i]=!0;return r}var Ds=K(Dr());var Rs=K(require("fs"));var As={keyword:Be,entity:Be,value:e=>R(ot(e)),punctuation:ot,directive:Be,function:Be,variable:e=>R(ot(e)),string:e=>R(O(e)),boolean:it,number:Be,comment:br};var Hc=e=>e,Xr={},zc=0,D={manual:Xr.Prism&&Xr.Prism.manual,disableWorkerMessageHandler:Xr.Prism&&Xr.Prism.disableWorkerMessageHandler,util:{encode:function(e){if(e instanceof ve){let t=e;return new ve(t.type,D.util.encode(t.content),t.alias)}else return Array.isArray(e)?e.map(D.util.encode):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++zc}),e.__id},clone:function e(t,r){let n,i,o=D.util.type(t);switch(r=r||{},o){case"Object":if(i=D.util.objId(t),r[i])return r[i];n={},r[i]=n;for(let s in t)t.hasOwnProperty(s)&&(n[s]=e(t[s],r));return n;case"Array":return i=D.util.objId(t),r[i]?r[i]:(n=[],r[i]=n,t.forEach(function(s,a){n[a]=e(s,r)}),n);default:return t}}},languages:{extend:function(e,t){let r=D.util.clone(D.languages[e]);for(let n in t)r[n]=t[n];return r},insertBefore:function(e,t,r,n){n=n||D.languages;let i=n[e],o={};for(let a in i)if(i.hasOwnProperty(a)){if(a==t)for(let l in r)r.hasOwnProperty(l)&&(o[l]=r[l]);r.hasOwnProperty(a)||(o[a]=i[a])}let s=n[e];return n[e]=o,D.languages.DFS(D.languages,function(a,l){l===s&&a!=e&&(this[a]=o)}),o},DFS:function e(t,r,n,i){i=i||{};let o=D.util.objId;for(let s in t)if(t.hasOwnProperty(s)){r.call(t,s,t[s],n||s);let a=t[s],l=D.util.type(a);l==="Object"&&!i[o(a)]?(i[o(a)]=!0,e(a,r,null,i)):l==="Array"&&!i[o(a)]&&(i[o(a)]=!0,e(a,r,s,i))}}},plugins:{},highlight:function(e,t,r){let n={code:e,grammar:t,language:r};return D.hooks.run("before-tokenize",n),n.tokens=D.tokenize(n.code,n.grammar),D.hooks.run("after-tokenize",n),ve.stringify(D.util.encode(n.tokens),n.language)},matchGrammar:function(e,t,r,n,i,o,s){for(let f in r){if(!r.hasOwnProperty(f)||!r[f])continue;if(f==s)return;let E=r[f];E=D.util.type(E)==="Array"?E:[E];for(let x=0;x<E.length;++x){let b=E[x],h=b.inside,y=!!b.lookbehind,T=!!b.greedy,A=0,P=b.alias;if(T&&!b.pattern.global){let k=b.pattern.toString().match(/[imuy]*$/)[0];b.pattern=RegExp(b.pattern.source,k+"g")}b=b.pattern||b;for(let k=n,B=i;k<t.length;B+=t[k].length,++k){let V=t[k];if(t.length>e.length)return;if(V instanceof ve)continue;if(T&&k!=t.length-1){b.lastIndex=B;var p=b.exec(e);if(!p)break;var c=p.index+(y?p[1].length:0),d=p.index+p[0].length,a=k,l=B;for(let N=t.length;a<N&&(l<d||!t[a].type&&!t[a-1].greedy);++a)l+=t[a].length,c>=l&&(++k,B=l);if(t[k]instanceof ve)continue;u=a-k,V=e.slice(B,l),p.index-=B}else{b.lastIndex=0;var p=b.exec(V),u=1}if(!p){if(o)break;continue}y&&(A=p[1]?p[1].length:0);var c=p.index+A,p=p[0].slice(A),d=c+p.length,m=V.slice(0,c),g=V.slice(d);let ee=[k,u];m&&(++k,B+=m.length,ee.push(m));let nt=new ve(f,h?D.tokenize(p,h):p,P,p,T);if(ee.push(nt),g&&ee.push(g),Array.prototype.splice.apply(t,ee),u!=1&&D.matchGrammar(e,t,r,k,B,!0,f),o)break}}}},tokenize:function(e,t){let r=[e],n=t.rest;if(n){for(let i in n)t[i]=n[i];delete t.rest}return D.matchGrammar(e,r,t,0,0,!1),r},hooks:{all:{},add:function(e,t){let r=D.hooks.all;r[e]=r[e]||[],r[e].push(t)},run:function(e,t){let r=D.hooks.all[e];if(!(!r||!r.length))for(var n=0,i;i=r[n++];)i(t)}},Token:ve};D.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/,punctuation:/[{}[\];(),.:]/};D.languages.javascript=D.languages.extend("clike",{"class-name":[D.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/});D.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/;D.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/,lookbehind:!0,greedy:!0},"function-variable":{pattern:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:D.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:D.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:D.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:D.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/});D.languages.markup&&D.languages.markup.tag.addInlined("script","javascript");D.languages.js=D.languages.javascript;D.languages.typescript=D.languages.extend("javascript",{keyword:/\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/,builtin:/\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/});D.languages.ts=D.languages.typescript;function ve(e,t,r,n,i){this.type=e,this.content=t,this.alias=r,this.length=(n||"").length|0,this.greedy=!!i}ve.stringify=function(e,t){return typeof e=="string"?e:Array.isArray(e)?e.map(function(r){return ve.stringify(r,t)}).join(""):Yc(e.type)(e.content)};function Yc(e){return As[e]||Hc}function vs(e){return Zc(e,D.languages.javascript)}function Zc(e,t){return D.tokenize(e,t).map(n=>ve.stringify(n)).join("")}var Cs=K(Bn());function Fs(e){return(0,Cs.default)(e)}var Ce=class{static read(t){let r;try{r=Rs.default.readFileSync(t,"utf-8")}catch{return null}return Ce.fromContent(r)}static fromContent(t){let r=t.split(/\r?\n/);return new Ce(1,r)}constructor(t,r){this.firstLineNumber=t,this.lines=r}get lastLineNumber(){return this.firstLineNumber+this.lines.length-1}mapLineAt(t,r){if(t<this.firstLineNumber||t>this.lines.length+this.firstLineNumber)return this;let n=t-this.firstLineNumber,i=[...this.lines];return i[n]=r(i[n]),new Ce(this.firstLineNumber,i)}mapLines(t){return new Ce(this.firstLineNumber,this.lines.map((r,n)=>t(r,this.firstLineNumber+n)))}lineAt(t){return this.lines[t-this.firstLineNumber]}prependSymbolAt(t,r){return this.mapLines((n,i)=>i===t?`${r} ${n}`:`  ${n}`)}slice(t,r){let n=this.lines.slice(t-1,r).join(`
`);return new Ce(t,Fs(n).split(`
`))}highlight(){let t=vs(this.toString());return new Ce(this.firstLineNumber,t.split(`
`))}toString(){return this.lines.join(`
`)}};var Xc={red:S,gray:br,dim:$,bold:R,underline:we,highlightSource:e=>e.highlight()},ep={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function tp({callsite:e,message:t,originalMethod:r,isPanic:n,callArguments:i},o){let s={functionName:`prisma.${r}()`,message:t,isPanic:n??!1,callArguments:i};if(!e||typeof window<"u"||process.env.NODE_ENV==="production")return s;let a=e.getLocation();if(!a||!a.lineNumber||!a.columnNumber)return s;let l=Math.max(1,a.lineNumber-3),u=Ce.read(a.fileName)?.slice(l,a.lineNumber),c=u?.lineAt(a.lineNumber);if(u&&c){let p=np(c),d=rp(c);if(!d)return s;s.functionName=`${d.code})`,s.location=a,n||(u=u.mapLineAt(a.lineNumber,g=>g.slice(0,d.openingBraceIndex))),u=o.highlightSource(u);let m=String(u.lastLineNumber).length;if(s.contextLines=u.mapLines((g,f)=>o.gray(String(f).padStart(m))+" "+g).mapLines(g=>o.dim(g)).prependSymbolAt(a.lineNumber,o.bold(o.red("\u2192"))),i){let g=p+m+1;g+=2,s.callArguments=(0,Ds.default)(i,g).slice(g)}}return s}function rp(e){let t=Object.keys(ye.ModelAction).join("|"),n=new RegExp(String.raw`\.(${t})\(`).exec(e);if(n){let i=n.index+n[0].length,o=e.lastIndexOf(" ",n.index)+1;return{code:e.slice(o,i),openingBraceIndex:i}}return null}function np(e){let t=0;for(let r=0;r<e.length;r++){if(e.charAt(r)!==" ")return t;t++}return t}function ip({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],l=t?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${l}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${l}`)),t&&a.push(s.underline(op(t))),i){a.push("");let u=[i.toString()];o&&(u.push(o),u.push(s.dim(")"))),a.push(u.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function op(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function ke(e){let t=e.showColors?Xc:ep,r=tp(e,t);return ip(r,t)}function Ss(e){return e instanceof Buffer||e instanceof Date||e instanceof RegExp}function Is(e){if(e instanceof Buffer){let t=Buffer.alloc?Buffer.alloc(e.length):new Buffer(e.length);return e.copy(t),t}else{if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp)return new RegExp(e);throw new Error("Unexpected situation")}}function ks(e){let t=[];return e.forEach(function(r,n){typeof r=="object"&&r!==null?Array.isArray(r)?t[n]=ks(r):Ss(r)?t[n]=Is(r):t[n]=ur({},r):t[n]=r}),t}function Os(e,t){return t==="__proto__"?void 0:e[t]}var ur=function(e,...t){if(!e||typeof e!="object")return!1;if(t.length===0)return e;let r,n;for(let i of t)if(!(typeof i!="object"||i===null||Array.isArray(i))){for(let o of Object.keys(i))if(n=Os(e,o),r=Os(i,o),r!==e)if(typeof r!="object"||r===null){e[o]=r;continue}else if(Array.isArray(r)){e[o]=ks(r);continue}else if(Ss(r)){e[o]=Is(r);continue}else if(typeof n!="object"||n===null||Array.isArray(n)){e[o]=ur({},r);continue}else{e[o]=ur(n,r);continue}}return e};function $s(e,t){if(!e||typeof e!="object"||typeof e.hasOwnProperty!="function")return e;let r={};for(let n in e){let i=e[n];Object.hasOwnProperty.call(e,n)&&t(n,i)&&(r[n]=i)}return r}var sp={"[object Date]":!0,"[object Uint8Array]":!0,"[object Decimal]":!0};function Ns(e){return e?typeof e=="object"&&!sp[Object.prototype.toString.call(e)]:!1}function _s(e,t){let r={},n=Array.isArray(t)?t:[t];for(let i in e)Object.hasOwnProperty.call(e,i)&&!n.includes(i)&&(r[i]=e[i]);return r}var Ai=K(Or());var ap=qs(),lp=Bs(),up=Vs().default,cp=(e,t,r)=>{let n=[];return function i(o,s={},a="",l=[]){s.indent=s.indent||"	";let u;s.inlineCharacterLimit===void 0?u={newLine:`
`,newLineOrSpace:`
`,pad:a,indent:a+s.indent}:u={newLine:"@@__STRINGIFY_OBJECT_NEW_LINE__@@",newLineOrSpace:"@@__STRINGIFY_OBJECT_NEW_LINE_OR_SPACE__@@",pad:"@@__STRINGIFY_OBJECT_PAD__@@",indent:"@@__STRINGIFY_OBJECT_INDENT__@@"};let c=p=>{if(s.inlineCharacterLimit===void 0)return p;let d=p.replace(new RegExp(u.newLine,"g"),"").replace(new RegExp(u.newLineOrSpace,"g")," ").replace(new RegExp(u.pad+"|"+u.indent,"g"),"");return d.length<=s.inlineCharacterLimit?d:p.replace(new RegExp(u.newLine+"|"+u.newLineOrSpace,"g"),`
`).replace(new RegExp(u.pad,"g"),a).replace(new RegExp(u.indent,"g"),a+s.indent)};if(n.indexOf(o)!==-1)return'"[Circular]"';if(Buffer.isBuffer(o))return`Buffer(${Buffer.length})`;if(o==null||typeof o=="number"||typeof o=="boolean"||typeof o=="function"||typeof o=="symbol"||o instanceof W||ap(o))return String(o);if(ue(o))return`new Date('${De(o)?o.toISOString():"Invalid Date"}')`;if(o instanceof xe)return`prisma.${bt(o.modelName)}.fields.${o.name}`;if(Array.isArray(o)){if(o.length===0)return"[]";n.push(o);let p="["+u.newLine+o.map((d,m)=>{let g=o.length-1===m?u.newLine:","+u.newLineOrSpace,f=i(d,s,a+s.indent,[...l,m]);s.transformValue&&(f=s.transformValue(o,m,f));let E=u.indent+f+g;return s.transformLine&&(E=s.transformLine({obj:o,indent:u.indent,key:m,stringifiedValue:f,value:o[m],eol:g,originalLine:E,path:l.concat(m)})),E}).join("")+u.pad+"]";return n.pop(),c(p)}if(lp(o)){let p=Object.keys(o).concat(up(o));if(s.filter&&(p=p.filter(m=>s.filter(o,m))),p.length===0)return"{}";n.push(o);let d="{"+u.newLine+p.map((m,g)=>{let f=p.length-1===g?u.newLine:","+u.newLineOrSpace,E=typeof m=="symbol",x=!E&&/^[a-z$_][a-z$_0-9]*$/i.test(m),b=E||x?m:i(m,s,void 0,[...l,m]),h=i(o[m],s,a+s.indent,[...l,m]);s.transformValue&&(h=s.transformValue(o,m,h));let y=u.indent+String(b)+": "+h+f;return s.transformLine&&(y=s.transformLine({obj:o,indent:u.indent,key:b,stringifiedValue:h,value:o[m],eol:f,originalLine:y,path:l.concat(b)})),y}).join("")+u.pad+"}";return n.pop(),c(d)}return o=String(o).replace(/[\r\n]/g,p=>p===`
`?"\\n":"\\r"),s.singleQuotes===!1?(o=o.replace(/"/g,'\\"'),`"${o}"`):(o=o.replace(/\\?'/g,"\\'"),`'${o}'`)}(e,t,r)},cr=cp;var Mi="@@__DIM_POINTER__@@";function en({ast:e,keyPaths:t,valuePaths:r,missingItems:n}){let i=e;for(let{path:o,type:s}of n)i=Yr(i,o,s);return cr(i,{indent:"  ",transformLine:({indent:o,key:s,value:a,stringifiedValue:l,eol:u,path:c})=>{let p=c.join("."),d=t.includes(p),m=r.includes(p),g=n.find(E=>E.path===p),f=l;if(g){typeof a=="string"&&(f=f.slice(1,f.length-1));let E=g.isRequired?"":"?",x=g.isRequired?"+":"?",h=(g.isRequired?y=>R(O(y)):O)(mp(s+E+": "+f+u,o,x));return g.isRequired||(h=$(h)),h}else{let E=n.some(y=>p.startsWith(y.path)),x=s[s.length-2]==="?";x&&(s=s.slice(1,s.length-1)),x&&typeof a=="object"&&a!==null&&(f=f.split(`
`).map((y,T,A)=>T===A.length-1?y+Mi:y).join(`
`)),E&&typeof a=="string"&&(f=f.slice(1,f.length-1),x||(f=R(f))),(typeof a!="object"||a===null)&&!m&&!E&&(f=$(f));let b="";typeof s=="string"&&(b=(d?S(s):s)+": "),f=m?S(f):f;let h=o+b+f+(E?u:$(u));if(d||m){let y=h.split(`
`),T=String(s).length,A=d?S("~".repeat(T)):" ".repeat(T),P=m?pp(o,s,a,l):0,k=m&&Us(a),B=m?"  "+S("~".repeat(P)):"";A&&A.length>0&&!k&&y.splice(1,0,o+A+B),A&&A.length>0&&k&&y.splice(y.length-1,0,o.slice(0,o.length-2)+B),h=y.join(`
`)}return h}}})}function pp(e,t,r,n){return r===null?4:typeof r=="string"?r.length+2:Array.isArray(r)&&r.length==0?2:Us(r)?Math.abs(dp(`${t}: ${(0,Ai.default)(n)}`)-e.length):ue(r)?De(r)?`new Date('${r.toISOString()}')`.length:24:String(r).length}function Us(e){return typeof e=="object"&&e!==null&&!(e instanceof W)&&!ue(e)}function dp(e){return e.split(`
`).reduce((t,r)=>r.length>t?r.length:t,0)}function mp(e,t,r){return e.split(`
`).map((n,i,o)=>i===0?r+t.slice(1)+n:i<o.length-1?r+n.slice(1):n).map(n=>(0,Ai.default)(n).includes(Mi)?$(n.replace(Mi,"")):n.includes("?")?$(n):n).join(`
`)}var pr=2,Ri=class{constructor(t,r){this.type=t;this.children=r;this.printFieldError=({error:t},r,n)=>{if(t.type==="emptySelect"){let i=n?"":` Available options are listed in ${$(O("green"))}.`;return`The ${S("`select`")} statement for type ${R(Qt(t.field.outputType.type))} must not be empty.${i}`}if(t.type==="emptyInclude"){if(r.length===0)return`${R(Qt(t.field.outputType.type))} does not have any relation and therefore can't have an ${S("`include`")} statement.`;let i=n?"":` Available options are listed in ${$(O("green"))}.`;return`The ${S("`include`")} statement for type ${S(Qt(t.field.outputType.type))} must not be empty.${i}`}if(t.type==="noTrueSelect")return`The ${S("`select`")} statement for type ${S(Qt(t.field.outputType.type))} needs ${S("at least one truthy value")}.`;if(t.type==="includeAndSelect")return`Please ${R("either")} use ${O("`include`")} or ${O("`select`")}, but ${S("not both")} at the same time.`;if(t.type==="invalidFieldName"){let i=t.isInclude?"include":"select",o=t.isIncludeScalar?"Invalid scalar":"Unknown",s=n?"":t.isInclude&&r.length===0?`
This model has no relations, so you can't use ${S("include")} with it.`:` Available options are listed in ${$(O("green"))}.`,a=`${o} field ${S(`\`${t.providedName}\``)} for ${S(i)} statement on model ${R(Dt(t.modelName))}.${s}`;return t.didYouMean&&(a+=` Did you mean ${O(`\`${t.didYouMean}\``)}?`),t.isIncludeScalar&&(a+=`
Note, that ${R("include")} statements only accept relation fields.`),a}if(t.type==="invalidFieldType")return`Invalid value ${S(`${cr(t.providedValue)}`)} of type ${S(xt(t.providedValue,void 0))} for field ${R(`${t.fieldName}`)} on model ${R(Dt(t.modelName))}. Expected either ${O("true")} or ${O("false")}.`};this.printArgError=({error:t,path:r},n,i)=>{if(t.type==="invalidName"){let o=`Unknown arg ${S(`\`${t.providedName}\``)} in ${R(r.join("."))} for type ${R(t.outputType?t.outputType.name:Ut(t.originalType))}.`;return t.didYouMeanField?o+=`
\u2192 Did you forget to wrap it with \`${O("select")}\`? ${$("e.g. "+O(`{ select: { ${t.providedName}: ${t.providedValue} } }`))}`:t.didYouMeanArg?(o+=` Did you mean \`${O(t.didYouMeanArg)}\`?`,!n&&!i&&(o+=` ${$("Available args:")}
`+Et(t.originalType,!0))):t.originalType.fields.length===0?o+=` The field ${R(t.originalType.name)} has no arguments.`:!n&&!i&&(o+=` Available args:

`+Et(t.originalType,!0)),o}if(t.type==="invalidType"){let o=cr(t.providedValue,{indent:"  "}),s=o.split(`
`).length>1;if(s&&(o=`
${o}
`),t.requiredType.bestFittingType.location==="enumTypes")return`Argument ${R(t.argName)}: Provided value ${S(o)}${s?"":" "}of type ${S(xt(t.providedValue))} on ${R(`prisma.${this.children[0].name}`)} is not a ${O(Kt(ht(t.requiredType.bestFittingType.type),t.requiredType.bestFittingType.isList))}.
\u2192 Possible values: ${t.requiredType.bestFittingType.type.values.map(c=>O(`${ht(t.requiredType.bestFittingType.type)}.${c}`)).join(", ")}`;let a=".";At(t.requiredType.bestFittingType.type)&&(a=`:
`+Et(t.requiredType.bestFittingType.type));let l=`${t.requiredType.inputType.map(c=>O(Kt(ht(c.type),t.requiredType.bestFittingType.isList))).join(" or ")}${a}`,u=t.requiredType.inputType.length===2&&t.requiredType.inputType.find(c=>At(c.type))||null;return u&&(l+=`
`+Et(u.type,!0)),`Argument ${R(t.argName)}: Got invalid value ${S(o)}${s?"":" "}on ${R(`prisma.${this.children[0].name}`)}. Provided ${S(xt(t.providedValue))}, expected ${l}`}if(t.type==="invalidNullArg"){let o=r.length===1&&r[0]===t.name?"":` for ${R(`${r.join(".")}`)}`,s=` Please use ${R(O("undefined"))} instead.`;return`Argument ${O(t.name)}${o} must not be ${R("null")}.${s}`}if(t.type==="invalidDateArg"){let o=r.length===1&&r[0]===t.argName?"":` for ${R(`${r.join(".")}`)}`;return`Argument ${O(t.argName)}${o} is not a valid Date object.`}if(t.type==="missingArg"){let o=r.length===1&&r[0]===t.missingName?"":` for ${R(`${r.join(".")}`)}`;return`Argument ${O(t.missingName)}${o} is missing.`}if(t.type==="atLeastOne"){let o=i?"":` Available args are listed in ${$(O("green"))}.`,s=t.atLeastFields?` and at least one argument for ${t.atLeastFields.map(a=>R(a)).join(", or ")}`:"";return`Argument ${R(r.join("."))} of type ${R(t.inputType.name)} needs ${O("at least one")} argument${R(s)}.${o}`}if(t.type==="atMostOne"){let o=i?"":` Please choose one. ${$("Available args:")} 
${Et(t.inputType,!0)}`;return`Argument ${R(r.join("."))} of type ${R(t.inputType.name)} needs ${O("exactly one")} argument, but you provided ${t.providedKeys.map(s=>S(s)).join(" and ")}.${o}`}};this.type=t,this.children=r}get[Symbol.toStringTag](){return"Document"}toString(){return`${this.type} {
${(0,rt.default)(this.children.map(String).join(`
`),pr)}
}`}validate(t,r=!1,n,i,o){t||(t={});let s=this.children.filter(x=>x.hasInvalidChild||x.hasInvalidArg);if(s.length===0)return;let a=[],l=[],u=t&&t.select?"select":t.include?"include":void 0;for(let x of s){let b=x.collectErrors(u);a.push(...b.fieldErrors.map(h=>({...h,path:r?h.path:h.path.slice(1)}))),l.push(...b.argErrors.map(h=>({...h,path:r?h.path:h.path.slice(1)})))}let c=this.children[0].name,p=r?this.type:c,d=[],m=[],g=[];for(let x of a){let b=this.normalizePath(x.path,t).join(".");if(x.error.type==="invalidFieldName"){d.push(b);let h=x.error.outputType,{isInclude:y}=x.error;h.fields.filter(T=>y?T.outputType.location==="outputObjectTypes":!0).forEach(T=>{let A=b.split(".");g.push({path:`${A.slice(0,A.length-1).join(".")}.${T.name}`,type:"true",isRequired:!1})})}else x.error.type==="includeAndSelect"?(d.push("select"),d.push("include")):m.push(b);if(x.error.type==="emptySelect"||x.error.type==="noTrueSelect"||x.error.type==="emptyInclude"){let h=this.normalizePath(x.path,t),y=h.slice(0,h.length-1).join(".");x.error.field.outputType.type.fields?.filter(A=>x.error.type==="emptyInclude"?A.outputType.location==="outputObjectTypes":!0).forEach(A=>{g.push({path:`${y}.${A.name}`,type:"true",isRequired:!1})})}}for(let x of l){let b=this.normalizePath(x.path,t).join(".");if(x.error.type==="invalidName")d.push(b);else if(x.error.type!=="missingArg"&&x.error.type!=="atLeastOne")m.push(b);else if(x.error.type==="missingArg"){let h=x.error.missingArg.inputTypes.length===1?x.error.missingArg.inputTypes[0].type:x.error.missingArg.inputTypes.map(y=>{let T=Ut(y.type);return T==="Null"?"null":y.isList?T+"[]":T}).join(" | ");g.push({path:b,type:oi(h,!0,b.split("where.").length===2),isRequired:x.error.missingArg.isRequired})}}let f=x=>{let b=l.some(V=>V.error.type==="missingArg"&&V.error.missingArg.isRequired),h=Boolean(l.find(V=>V.error.type==="missingArg"&&!V.error.missingArg.isRequired)),y=h||b,T="";b&&(T+=`
${$("Note: Lines with ")}${O("+")} ${$("are required")}`),h&&(T.length===0&&(T=`
`),b?T+=$(`, lines with ${O("?")} are optional`):T+=$(`Note: Lines with ${O("?")} are optional`),T+=$("."));let P=l.filter(V=>V.error.type!=="missingArg"||V.error.missingArg.isRequired).map(V=>this.printArgError(V,y,i==="minimal")).join(`
`);if(P+=`
${a.map(V=>this.printFieldError(V,g,i==="minimal")).join(`
`)}`,i==="minimal")return(0,Fi.default)(P);let k={ast:r?{[c]:t}:t,keyPaths:d,valuePaths:m,missingItems:g};n?.endsWith("aggregate")&&(k=Mp(k));let B=ke({callsite:x,originalMethod:n||p,showColors:i&&i==="pretty",callArguments:en(k),message:`${P}${T}
`});return process.env.NO_COLOR||i==="colorless"?(0,Fi.default)(B):B},E=new H(f(o));throw process.env.NODE_ENV!=="production"&&Object.defineProperty(E,"render",{get:()=>f,enumerable:!1}),E}normalizePath(t,r){let n=t.slice(),i=[],o,s=r;for(;(o=n.shift())!==void 0;)!Array.isArray(s)&&o===0||(o==="select"?s[o]?s=s[o]:s=s.include:s&&s[o]&&(s=s[o]),i.push(o));return i}},H=class extends Error{get[Symbol.toStringTag](){return"PrismaClientValidationError"}};C(H,"PrismaClientValidationError");var J=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};C(J,"PrismaClientConstructorValidationError");var ce=class{constructor({name:t,args:r,children:n,error:i,schemaField:o}){this.name=t,this.args=r,this.children=n,this.error=i,this.schemaField=o,this.hasInvalidChild=n?n.some(s=>Boolean(s.error||s.hasInvalidArg||s.hasInvalidChild)):!1,this.hasInvalidArg=r?r.hasInvalidArg:!1}get[Symbol.toStringTag](){return"Field"}toString(){let t=this.name;return this.error?t+" # INVALID_FIELD":(this.args&&this.args.args&&this.args.args.length>0&&(this.args.args.length===1?t+=`(${this.args.toString()})`:t+=`(
${(0,rt.default)(this.args.toString(),pr)}
)`),this.children&&(t+=` {
${(0,rt.default)(this.children.map(String).join(`
`),pr)}
}`),t)}collectErrors(t="select"){let r=[],n=[];if(this.error&&r.push({path:[this.name],error:this.error}),this.children)for(let i of this.children){let o=i.collectErrors(t);r.push(...o.fieldErrors.map(s=>({...s,path:[this.name,t,...s.path]}))),n.push(...o.argErrors.map(s=>({...s,path:[this.name,t,...s.path]})))}return this.args&&n.push(...this.args.collectErrors().map(i=>({...i,path:[this.name,...i.path]}))),{fieldErrors:r,argErrors:n}}},se=class{constructor(t=[]){this.args=t,this.hasInvalidArg=t?t.some(r=>Boolean(r.hasError)):!1}get[Symbol.toStringTag](){return"Args"}toString(){return this.args.length===0?"":`${this.args.map(t=>t.toString()).filter(t=>t).join(`
`)}`}collectErrors(){return this.hasInvalidArg?this.args.flatMap(t=>t.collectErrors()):[]}};function vi(e,t){return Buffer.isBuffer(e)?JSON.stringify(e.toString("base64")):e instanceof xe?`{ _ref: ${JSON.stringify(e.name)}, _container: ${JSON.stringify(e.modelName)}}`:Object.prototype.toString.call(e)==="[object BigInt]"?e.toString():typeof t?.type=="string"&&t.type==="Json"?e===null?"null":e&&e.values&&e.__prismaRawParameters__?JSON.stringify(e.values):t?.isList&&Array.isArray(e)?JSON.stringify(e.map(r=>JSON.stringify(r))):JSON.stringify(JSON.stringify(e)):e===void 0?null:e===null?"null":le.isDecimal(e)||t?.type==="Decimal"&&Oe(e)?JSON.stringify(e.toFixed()):t?.location==="enumTypes"&&typeof e=="string"?Array.isArray(e)?`[${e.join(", ")}]`:e:typeof e=="number"&&t?.type==="Float"?e.toExponential():JSON.stringify(e,null,2)}var ae=class{constructor({key:t,value:r,isEnum:n=!1,error:i,schemaArg:o,inputType:s}){this.inputType=s,this.key=t,this.value=r instanceof W?r._getName():r,this.isEnum=n,this.error=i,this.schemaArg=o,this.isNullable=o?.inputTypes.reduce(a=>a&&o.isNullable,!0)||!1,this.hasError=Boolean(i)||(r instanceof se?r.hasInvalidArg:!1)||Array.isArray(r)&&r.some(a=>a instanceof se?a.hasInvalidArg:a instanceof ae?a.hasError:!1)}get[Symbol.toStringTag](){return"Arg"}_toString(t,r){let n=this.stringifyValue(t);if(!(typeof n>"u"))return`${r}: ${n}`}stringifyValue(t){if(!(typeof t>"u")){if(t instanceof se)return`{
${(0,rt.default)(t.toString(),2)}
}`;if(Array.isArray(t)){if(this.inputType?.type==="Json")return vi(t,this.inputType);let r=!t.some(n=>typeof n=="object");return`[${r?"":`
`}${(0,rt.default)(t.map(n=>n instanceof se?`{
${(0,rt.default)(n.toString(),pr)}
}`:n instanceof ae?n.stringifyValue(n.value):vi(n,this.inputType)).join(`,${r?" ":`
`}`),r?0:pr)}${r?"":`
`}]`}return vi(t,this.inputType)}}toString(){return this._toString(this.value,this.key)}collectErrors(){if(!this.hasError)return[];let t=[];if(this.error){let r=typeof this.inputType?.type=="object"?`${this.inputType.type.name}${this.inputType.isList?"[]":""}`:void 0;t.push({error:this.error,path:[this.key],id:r})}return Array.isArray(this.value)?t.concat(this.value.flatMap((r,n)=>r instanceof se?r.collectErrors().map(i=>({...i,path:[this.key,String(n),...i.path]})):r instanceof ae?r.collectErrors().map(i=>({...i,path:[this.key,...i.path]})):[])):this.value instanceof se?t.concat(this.value.collectErrors().map(r=>({...r,path:[this.key,...r.path]}))):t}};function nn({dmmf:e,rootTypeName:t,rootField:r,select:n,modelName:i,extensions:o}){n||(n={});let s=t==="query"?e.queryType:e.mutationType,a={args:[],outputType:{isList:!1,type:s,location:"outputObjectTypes"},name:t},l={modelName:i},u=Js({dmmf:e,selection:{[r]:n},schemaField:a,path:[t],context:l,extensions:o});return new Ri(t,u)}function Qs(e){return e}function Js({dmmf:e,selection:t,schemaField:r,path:n,context:i,extensions:o}){let s=r.outputType.type,a=i.modelName?o.getAllComputedFields(i.modelName):{};return t=Zr(t,a),Object.entries(t).reduce((l,[u,c])=>{let p=s.fieldMap?s.fieldMap[u]:s.fields.find(h=>h.name===u);if(!p)return a?.[u]||l.push(new ce({name:u,children:[],error:{type:"invalidFieldName",modelName:s.name,providedName:u,didYouMean:Vr(u,s.fields.map(h=>h.name).concat(Object.keys(a??{}))),outputType:s}})),l;if(p.outputType.location==="scalar"&&p.args.length===0&&typeof c!="boolean")return l.push(new ce({name:u,children:[],error:{type:"invalidFieldType",modelName:s.name,fieldName:u,providedValue:c}})),l;if(c===!1)return l;let d={name:p.name,fields:p.args,constraints:{minNumFields:null,maxNumFields:null}},m=typeof c=="object"?_s(c,["include","select"]):void 0,g=m?rn(m,d,i,[],typeof p=="string"?void 0:p.outputType.type):void 0,f=p.outputType.location==="outputObjectTypes";if(c){if(c.select&&c.include)l.push(new ce({name:u,children:[new ce({name:"include",args:new se,error:{type:"includeAndSelect",field:p}})]}));else if(c.include){let h=Object.keys(c.include);if(h.length===0)return l.push(new ce({name:u,children:[new ce({name:"include",args:new se,error:{type:"emptyInclude",field:p}})]})),l;if(p.outputType.location==="outputObjectTypes"){let y=p.outputType.type,T=y.fields.filter(P=>P.outputType.location==="outputObjectTypes").map(P=>P.name),A=h.filter(P=>!T.includes(P));if(A.length>0)return l.push(...A.map(P=>new ce({name:P,children:[new ce({name:P,args:new se,error:{type:"invalidFieldName",modelName:y.name,outputType:y,providedName:P,didYouMean:Vr(P,T)||void 0,isInclude:!0,isIncludeScalar:y.fields.some(k=>k.name===P)}})]}))),l}}else if(c.select){let h=Object.values(c.select);if(h.length===0)return l.push(new ce({name:u,children:[new ce({name:"select",args:new se,error:{type:"emptySelect",field:p}})]})),l;if(h.filter(T=>T).length===0)return l.push(new ce({name:u,children:[new ce({name:"select",args:new se,error:{type:"noTrueSelect",field:p}})]})),l}}let E=f?gp(e,p.outputType.type):null,x=E;c&&(c.select?x=c.select:c.include?x=ur(E,c.include):c.by&&Array.isArray(c.by)&&p.outputType.namespace==="prisma"&&p.outputType.location==="outputObjectTypes"&&Zo(p.outputType.type.name)&&(x=fp(c.by)));let b;if(x!==!1&&f){let h=i.modelName;typeof p.outputType.type=="object"&&p.outputType.namespace==="model"&&p.outputType.location==="outputObjectTypes"&&(h=p.outputType.type.name),b=Js({dmmf:e,selection:x,schemaField:p,path:[...n,u],context:{modelName:h},extensions:o})}return l.push(new ce({name:u,args:g,children:b,schemaField:p})),l},[])}function fp(e){let t=Object.create(null);for(let r of e)t[r]=!0;return t}function gp(e,t){let r=Object.create(null);for(let n of t.fields)e.typeMap[n.outputType.type.name]!==void 0&&(r[n.name]=!0),(n.outputType.location==="scalar"||n.outputType.location==="enumTypes")&&(r[n.name]=!0);return r}function Di(e,t,r,n){return new ae({key:e,value:t,isEnum:n.location==="enumTypes",inputType:n,error:{type:"invalidType",providedValue:t,argName:e,requiredType:{inputType:r.inputTypes,bestFittingType:n}}})}function Gs(e,t,r){let{isList:n}=t,i=yp(t,r),o=xt(e,t);return o===i||n&&o==="List<>"||i==="Json"&&o!=="Symbol"&&!(e instanceof W)&&!(e instanceof xe)||o==="Int"&&i==="BigInt"||(o==="Int"||o==="Float")&&i==="Decimal"||o==="DateTime"&&i==="String"||o==="UUID"&&i==="String"||o==="String"&&i==="ID"||o==="Int"&&i==="Float"||o==="Int"&&i==="Long"||o==="String"&&i==="Decimal"&&hp(e)||e===null?!0:t.isList&&Array.isArray(e)?e.every(s=>Gs(s,{...t,isList:!1},r)):!1}function yp(e,t,r=e.isList){let n=ht(e.type);return e.location==="fieldRefTypes"&&t.modelName&&(n+=`<${t.modelName}>`),Kt(n,r)}var tn=e=>$s(e,(t,r)=>r!==void 0);function hp(e){return/^\-?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i.test(e)}function xp(e,t,r,n){let i=null,o=[];for(let s of r.inputTypes){if(i=bp(e,t,r,s,n),i?.collectErrors().length===0)return i;if(i&&i?.collectErrors()){let a=i?.collectErrors();a&&a.length>0&&o.push({arg:i,errors:a})}}if(i?.hasError&&o.length>0){let s=o.map(({arg:a,errors:l})=>{let u=l.map(c=>{let p=1;return c.error.type==="invalidType"&&(p=2*Math.exp(Ws(c.error.providedValue))+1),p+=Math.log(c.path.length),c.error.type==="missingArg"&&a.inputType&&At(a.inputType.type)&&a.inputType.type.name.includes("Unchecked")&&(p*=2),c.error.type==="invalidName"&&At(c.error.originalType)&&c.error.originalType.name.includes("Unchecked")&&(p*=2),p});return{score:l.length+Ep(u),arg:a,errors:l}});return s.sort((a,l)=>a.score<l.score?-1:1),s[0].arg}return i}function Ws(e){let t=1;if(!e||typeof e!="object")return t;for(let r in e)if(!!Object.prototype.hasOwnProperty.call(e,r)&&typeof e[r]=="object"){let n=Ws(e[r])+1;t=Math.max(n,t)}return t}function Ep(e){return e.reduce((t,r)=>t+r,0)}function bp(e,t,r,n,i){if(typeof t>"u")return r.isRequired?new ae({key:e,value:t,isEnum:n.location==="enumTypes",inputType:n,error:{type:"missingArg",missingName:e,missingArg:r,atLeastOne:!1,atMostOne:!1}}):null;let{isNullable:o,isRequired:s}=r;if(t===null&&!o&&!s&&!(At(n.type)?n.type.constraints.minNumFields!==null&&n.type.constraints.minNumFields>0:!1))return new ae({key:e,value:t,isEnum:n.location==="enumTypes",inputType:n,error:{type:"invalidNullArg",name:e,invalidType:r.inputTypes,atLeastOne:!1,atMostOne:!1}});if(!n.isList)if(At(n.type)){if(typeof t!="object"||Array.isArray(t)||n.location==="inputObjectTypes"&&!Ns(t))return Di(e,t,r,n);{let c=tn(t),p,d=Object.keys(c||{}),m=d.length;return m===0&&typeof n.type.constraints.minNumFields=="number"&&n.type.constraints.minNumFields>0||n.type.constraints.fields?.some(g=>d.includes(g))===!1?p={type:"atLeastOne",key:e,inputType:n.type,atLeastFields:n.type.constraints.fields}:m>1&&typeof n.type.constraints.maxNumFields=="number"&&n.type.constraints.maxNumFields<2&&(p={type:"atMostOne",key:e,inputType:n.type,providedKeys:d}),new ae({key:e,value:c===null?null:rn(c,n.type,i,r.inputTypes),isEnum:n.location==="enumTypes",error:p,inputType:n,schemaArg:r})}}else return Ks(e,t,r,n,i);if(!Array.isArray(t)&&n.isList&&e!=="updateMany"&&(t=[t]),n.location==="enumTypes"||n.location==="scalar")return Ks(e,t,r,n,i);let a=n.type,u=(typeof a.constraints?.minNumFields=="number"&&a.constraints?.minNumFields>0?Array.isArray(t)&&t.some(c=>!c||Object.keys(tn(c)).length===0):!1)?{inputType:a,key:e,type:"atLeastOne"}:void 0;if(!u){let c=typeof a.constraints?.maxNumFields=="number"&&a.constraints?.maxNumFields<2?Array.isArray(t)&&t.find(p=>!p||Object.keys(tn(p)).length!==1):!1;c&&(u={inputType:a,key:e,type:"atMostOne",providedKeys:Object.keys(c)})}if(!Array.isArray(t))for(let c of r.inputTypes){let p=rn(t,c.type,i);if(p.collectErrors().length===0)return new ae({key:e,value:p,isEnum:!1,schemaArg:r,inputType:c})}return new ae({key:e,value:t.map((c,p)=>n.isList&&typeof c!="object"?c:typeof c!="object"||!t||Array.isArray(c)?Di(String(p),c,Tp(r),wp(n)):rn(c,a,i)),isEnum:!1,inputType:n,schemaArg:r,error:u})}function wp(e){return{...e,isList:!1}}function Tp(e){return{...e,inputTypes:e.inputTypes.filter(t=>!t.isList)}}function At(e){return!(typeof e=="string"||Object.hasOwnProperty.call(e,"values"))}function Ks(e,t,r,n,i){return ue(t)&&!De(t)?new ae({key:e,value:t,schemaArg:r,inputType:n,error:{type:"invalidDateArg",argName:e}}):Gs(t,n,i)?new ae({key:e,value:t,isEnum:n.location==="enumTypes",schemaArg:r,inputType:n}):Di(e,t,r,n)}function rn(e,t,r,n,i){t.meta?.source&&(r={modelName:t.meta.source});let o=tn(e),{fields:s,fieldMap:a}=t,l=s.map(d=>[d.name,void 0]),u=Object.entries(o||{}),p=Yo(u,l,d=>d[0]).reduce((d,[m,g])=>{let f=a?a[m]:s.find(x=>x.name===m);if(!f){let x=typeof g=="boolean"&&i&&i.fields.some(b=>b.name===m)?m:null;return d.push(new ae({key:m,value:g,error:{type:"invalidName",providedName:m,providedValue:g,didYouMeanField:x,didYouMeanArg:!x&&Vr(m,[...s.map(b=>b.name),"select"])||void 0,originalType:t,possibilities:n,outputType:i}})),d}let E=xp(m,g,f,r);return E&&d.push(E),d},[]);if(typeof t.constraints.minNumFields=="number"&&u.length<t.constraints.minNumFields||p.find(d=>d.error?.type==="missingArg"||d.error?.type==="atLeastOne")){let d=t.fields.filter(m=>!m.isRequired&&o&&(typeof o[m.name]>"u"||o[m.name]===null));p.push(...d.map(m=>{let g=m.inputTypes[0];return new ae({key:m.name,value:void 0,isEnum:g.location==="enumTypes",error:{type:"missingArg",missingName:m.name,missingArg:m,atLeastOne:Boolean(t.constraints.minNumFields)||!1,atMostOne:t.constraints.maxNumFields===1||!1},inputType:g})}))}return new se(p)}function on({document:e,path:t,data:r}){let n=lr(r,t);if(n==="undefined")return null;if(typeof n!="object")return n;let i=Pp(e,t);return Oi({field:i,data:n})}function Oi({field:e,data:t}){if(!t||typeof t!="object"||!e.children||!e.schemaField)return t;let r={DateTime:n=>new Date(n),Json:n=>JSON.parse(n),Bytes:n=>Buffer.from(n,"base64"),Decimal:n=>new le(n),BigInt:n=>BigInt(n)};for(let n of e.children){let i=n.schemaField?.outputType.type;if(i&&typeof i=="string"){let o=r[i];if(o)if(Array.isArray(t))for(let s of t)typeof s[n.name]<"u"&&s[n.name]!==null&&(Array.isArray(s[n.name])?s[n.name]=s[n.name].map(o):s[n.name]=o(s[n.name]));else typeof t[n.name]<"u"&&t[n.name]!==null&&(Array.isArray(t[n.name])?t[n.name]=t[n.name].map(o):t[n.name]=o(t[n.name]))}if(n.schemaField&&n.schemaField.outputType.location==="outputObjectTypes")if(Array.isArray(t))for(let o of t)Oi({field:n,data:o[n.name]});else Oi({field:n,data:t[n.name]})}return t}function Pp(e,t){let r=t.slice(),n=r.shift(),i=e.children.find(o=>o.name===n);if(!i)throw new Error(`Could not find field ${n} in document ${e}`);for(;r.length>0;){let o=r.shift();if(!i.children)throw new Error(`Can't get children for field ${i} with child ${o}`);let s=i.children.find(a=>a.name===o);if(!s)throw new Error(`Can't find child ${o} of field ${i}`);i=s}return i}function Ci(e){return e.split(".").filter(t=>t!=="select").join(".")}function Si(e){if(Object.prototype.toString.call(e)==="[object Object]"){let r={};for(let n in e)if(n==="select")for(let i in e.select)r[i]=Si(e.select[i]);else r[n]=Si(e[n]);return r}return e}function Mp({ast:e,keyPaths:t,missingItems:r,valuePaths:n}){let i=t.map(Ci),o=n.map(Ci),s=r.map(l=>({path:Ci(l.path),isRequired:l.isRequired,type:l.type}));return{ast:Si(e),keyPaths:i,missingItems:s,valuePaths:o}}var sn=Hs().version;var be=class extends ne{constructor(t){super(t,{code:"P2025",clientVersion:sn}),this.name="NotFoundError"}};C(be,"NotFoundError");function Ii(e,t,r,n){let i;if(r&&typeof r=="object"&&"rejectOnNotFound"in r&&r.rejectOnNotFound!==void 0)i=r.rejectOnNotFound,delete r.rejectOnNotFound;else if(typeof n=="boolean")i=n;else if(n&&typeof n=="object"&&e in n){let o=n[e];if(o&&typeof o=="object")return t in o?o[t]:void 0;i=Ii(e,t,r,o)}else typeof n=="function"?i=n:i=!1;return i}var vp=/(findUnique|findFirst)/;function zs(e,t,r,n){if(r??(r="record"),n&&!e&&vp.exec(t))throw typeof n=="boolean"&&n?new be(`No ${r} found`):typeof n=="function"?n(new be(`No ${r} found`)):St(n)?n:new be(`No ${r} found`)}function Ys(e,t,r){return e===ye.ModelAction.findFirstOrThrow||e===ye.ModelAction.findUniqueOrThrow?Cp(t,r):r}function Cp(e,t){return async r=>{if("rejectOnNotFound"in r.args){let i=ke({originalMethod:r.clientMethod,callsite:r.callsite,message:"'rejectOnNotFound' option is not supported"});throw new H(i)}return await t(r).catch(i=>{throw i instanceof ne&&i.code==="P2025"?new be(`No ${e} found`):i})}}var Fp=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],Rp=["aggregate","count","groupBy"];function ki(e,t){let r=[Op(e,t),Dp(t)];e._engineConfig.previewFeatures?.includes("fieldReference")&&r.push(Ip(e,t));let n=e._extensions.getAllModelExtensions(t);return n&&r.push(Jt(n)),Ie({},r)}function Dp(e){return Ae("name",()=>e)}function Op(e,t){let r=Ee(t),n=Object.keys(ye.ModelAction).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=l=>e._request(l);s=Ys(o,t,s);let a=l=>u=>{let c=We(e._errorFormat);return e._createPrismaPromise(p=>{let d={args:u,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:p,callsite:c};return s({...d,...l})})};return Fp.includes(o)?Ti(e,t,a):Sp(i)?bs(e,i,a):a({})}}}function Sp(e){return Rp.includes(e)}function Ip(e,t){return Xe(Ae("fields",()=>{let r=e._runtimeDataModel.models[t];return ws(t,r)}))}function Zs(e){return e.replace(/^./,t=>t.toUpperCase())}var $i=Symbol();function dr(e){let t=[kp(e),Ae($i,()=>e)],r=e._extensions.getAllClientExtensions();return r&&t.push(Jt(r)),Ie(e,t)}function kp(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(Ee),n=[...new Set(t.concat(r))];return Xe({getKeys(){return n},getPropertyValue(i){let o=Zs(i);if(e._runtimeDataModel.models[o]!==void 0)return ki(e,o);if(e._runtimeDataModel.models[i]!==void 0)return ki(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function an(e){return e[$i]?e[$i]:e}function Xs(e){if(typeof e=="function")return e(this);let t=an(this),r=Object.create(t,{_extensions:{value:this._extensions.append(e)},$use:{value:void 0},$on:{value:void 0}});return dr(r)}function ea(e){if(e instanceof Z)return $p(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=mr(e[n]);return r}let t={};for(let r in e)t[r]=mr(e[r]);return t}function $p(e){return new Z(e.strings,e.values)}function mr(e){if(typeof e!="object"||e==null||e instanceof W||ft(e))return e;if(Oe(e))return new le(e.toFixed());if(ue(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=mr(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:mr(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=mr(e[r]);return t}Te(e,"Unknown value")}function ra(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:ea(t.args??{}),__internalParams:t,query:(s,a=t)=>{let l=a.customDataProxyFetch;return a.customDataProxyFetch=sa(o,l),a.args=s,ra(e,a,r,n+1)}})})}function na(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return ra(e,t,s)}function ia(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?oa(r,n,0,e):e(r)}}function oa(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let l=a.customDataProxyFetch;return a.customDataProxyFetch=sa(i,l),oa(a,t,r+1,n)}})}var ta=e=>e;function sa(e=ta,t=ta){return r=>e(t(r))}var ln=class{constructor(t,r){this.extension=t;this.previous=r;this.computedFieldsCache=new Se;this.modelExtensionsCache=new Se;this.queryCallbacksCache=new Se;this.clientExtensions=_t(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());this.batchCallbacks=_t(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t})}getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>Ms(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=Ee(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},He=class{constructor(t){this.head=t}static empty(){return new He}static single(t){return new He(new ln(t))}isEmpty(){return this.head===void 0}append(t){return new He(new ln(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};var aa=re("prisma:client"),la={Vercel:"vercel","Netlify CI":"netlify"};function ua({postinstall:e,ciName:t,clientVersion:r}){if(aa("checkPlatformCaching:postinstall",e),aa("checkPlatformCaching:ciName",t),e===!0&&t&&t in la){let n=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${la[t]}-build`;throw console.error(n),new pe(n,r)}}var Np={findUnique:"query",findUniqueOrThrow:"query",findFirst:"query",findFirstOrThrow:"query",findMany:"query",count:"query",create:"mutation",createMany:"mutation",update:"mutation",updateMany:"mutation",upsert:"mutation",delete:"mutation",deleteMany:"mutation",executeRaw:"mutation",queryRaw:"mutation",aggregate:"query",groupBy:"query",runCommandRaw:"mutation",findRaw:"query",aggregateRaw:"query"},un=class{constructor(t,r){this.dmmf=t;this.errorFormat=r}createMessage({action:t,modelName:r,args:n,extensions:i,clientMethod:o,callsite:s}){let a,l=Np[t];(t==="executeRaw"||t==="queryRaw"||t==="runCommandRaw")&&(a=t);let u;if(r!==void 0){if(u=this.dmmf?.mappingsMap[r],u===void 0)throw new Error(`Could not find mapping for model ${r}`);if(a=u[t==="count"?"aggregate":t],!a){let d=ke({message:`Model \`${r}\` does not support \`${t}\` action.`,originalMethod:o,callsite:s});throw new H(d)}}if(l!=="query"&&l!=="mutation")throw new Error(`Invalid operation ${l} for action ${t}`);if(this.dmmf?.rootFieldMap[a]===void 0)throw new Error(`Could not find rootField ${a} for action ${t} for model ${r} on rootType ${l}`);let p=nn({dmmf:this.dmmf,rootField:a,rootTypeName:l,select:n,modelName:r,extensions:i});return p.validate(n,!1,o,this.errorFormat,s),new Ni(p)}createBatch(t){return t.map(r=>r.toEngineQuery())}},Ni=class{constructor(t){this.document=t}isWrite(){return this.document.type==="mutation"}getBatchId(){if(!this.getRootField().startsWith("findUnique"))return;let t=this.document.children[0].args?.args.map(n=>n.value instanceof se?`${n.key}-${n.value.args.map(i=>i.key).join(",")}`:n.key).join(","),r=this.document.children[0].children.join(",");return`${this.document.children[0].name}|${t}|${r}`}toDebugString(){return String(this.document)}toEngineQuery(){return{query:String(this.document),variables:{}}}deserializeResponse(t,r){let n=this.getRootField(),i=[];return n&&i.push(n),i.push(...r.filter(o=>o!=="select"&&o!=="include")),on({document:this.document,path:i,data:t})}getRootField(){return this.document.children[0].name}};function cn(e){return e===null?e:Array.isArray(e)?e.map(cn):typeof e=="object"?_p(e)?Lp(e):ct(e,cn):e}function _p(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function Lp({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":return Buffer.from(t,"base64");case"DateTime":return new Date(t);case"Decimal":return new le(t);case"Json":return JSON.parse(t);default:Te(t,"Unknown tagged value")}}var pn=class{constructor(t=0,r){this.context=r;this.lines=[];this.currentLine="";this.currentIndent=0;this.currentIndent=t}write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r){let n=r.length-1;for(let i=0;i<r.length;i++)this.write(r[i]),i!==n&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};var ya=K(jr());function da(e,t){let r=ma(e),n=qp(r),i=jp(n);i?dn(i,t):t.addErrorMessage(()=>"Unknown error")}function ma(e){return e.errors.flatMap(t=>t.kind==="Union"?ma(t):[t])}function qp(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:o.argument.typeNames.concat(n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function jp(e){return Gn(e,(t,r)=>{let n=ca(t),i=ca(r);return n!==i?n-i:pa(t)-pa(r)})}function ca(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function pa(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;default:return 0}}var je=class{constructor(t,r){this.name=t;this.value=r;this.isRequired=!1}makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};var mn=e=>e,fa={bold:mn,red:mn,green:mn,dim:mn},ga={bold:R,red:S,green:O,dim:$},vt={write(e){e.writeLine(",")}};var $e=class{constructor(t){this.contents=t;this.isUnderlined=!1;this.color=t=>t}underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};var ze=class{constructor(){this.hasError=!1}markAsError(){return this.hasError=!0,this}};var j=class extends ze{constructor(){super(...arguments);this.fields={};this.suggestions=[]}addField(r){this.fields[r.name]=r}addSuggestion(r){this.suggestions.push(r)}getField(r){return this.fields[r]}getDeepField(r){let[n,...i]=r,o=this.getField(n);if(!o)return;let s=o;for(let a of i){if(!(s.value instanceof j))return;let l=s.value.getField(a);if(!l)return;s=l}return s}getDeepFieldValue(r){return r.length===0?this:this.getDeepField(r)?.value}hasField(r){return Boolean(this.getField(r))}removeAllFields(){this.fields={}}removeField(r){delete this.fields[r]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(r){return this.getField(r)?.value}getDeepSubSelectionValue(r){let n=this;for(let i of r){if(!(n instanceof j))return;let o=n.getSubSelectionValue(i);if(!o)return;n=o}return n}getDeepSelectionParent(r){let n=this.getSelectionParent();if(!n)return;let i=n;for(let o of r){let s=i.value.getFieldValue(o);if(!s||!(s instanceof j))return;let a=s.getSelectionParent();if(!a)return;i=a}return i}getSelectionParent(){let r=this.getField("select");if(r?.value instanceof j)return{kind:"select",value:r.value};let n=this.getField("include");if(n?.value instanceof j)return{kind:"include",value:n.value}}getSubSelectionValue(r){return this.getSelectionParent()?.value.fields[r].value}getPrintWidth(){let r=Object.values(this.fields);return r.length==0?2:Math.max(...r.map(i=>i.getPrintWidth()))+2}write(r){let n=Object.values(this.fields);if(n.length===0&&this.suggestions.length===0){this.writeEmpty(r);return}this.writeWithContents(r,n)}writeEmpty(r){let n=new $e("{}");this.hasError&&n.setColor(r.context.colors.red).underline(),r.write(n)}writeWithContents(r,n){r.writeLine("{").withIndent(()=>{r.writeJoined(vt,[...n,...this.suggestions]).newLine()}),r.write("}"),this.hasError&&r.afterNextNewline(()=>{r.writeLine(r.context.colors.red("~".repeat(this.getPrintWidth())))})}};var X=class extends ze{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new $e(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}};var fn=class{constructor(){this.fields=[]}addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(vt,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function dn(e,t){switch(e.kind){case"IncludeAndSelect":Vp(e,t);break;case"IncludeOnScalar":Up(e,t);break;case"EmptySelection":Kp(e,t);break;case"UnknownSelectionField":Qp(e,t);break;case"UnknownArgument":Jp(e,t);break;case"UnknownInputField":Gp(e,t);break;case"RequiredArgumentMissing":Wp(e,t);break;case"InvalidArgumentType":Hp(e,t);break;case"InvalidArgumentValue":zp(e,t);break;case"ValueTooLarge":Yp(e,t);break;case"SomeFieldsMissing":Zp(e,t);break;case"TooManyFieldsGiven":Xp(e,t);break;case"Union":da(e,t);break;default:throw new Error("not implemented: "+e.kind)}}function Vp(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath);r&&r instanceof j&&(r.getField("include")?.markAsError(),r.getField("select")?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green("`include`")} or ${n.green("`select`")}, but ${n.red("not both")} at the same time.`)}function Up(e,t){let[r,n]=gn(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new je(s.name,"true"));t.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${fr(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function Kp(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),Ea(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${fr(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function Qp(e,t){let[r,n]=gn(e.selectionPath),i=t.arguments.getDeepSelectionParent(r);i&&(i.value.getField(n)?.markAsError(),Ea(i.value,e.outputType)),t.addErrorMessage(o=>{let s=[`Unknown field ${o.red(`\`${n}\``)}`];return i&&s.push(`for ${o.bold(i.kind)} statement`),s.push(`on model ${o.bold(`\`${e.outputType.name}\``)}.`),s.push(fr(o)),s.join(" ")})}function Jp(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath);n instanceof j&&(n.getField(r)?.markAsError(),ed(n,e.arguments)),t.addErrorMessage(i=>ha(i,r,e.arguments.map(o=>o.name)))}function Gp(e,t){let[r,n]=gn(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath);if(i instanceof j){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r);o instanceof j&&ba(o,e.inputType)}t.addErrorMessage(o=>ha(o,n,e.inputType.fields.map(s=>s.name)))}function ha(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=rd(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(fr(e)),n.join(" ")}function Wp(e,t){let r;t.addErrorMessage(l=>r?.value instanceof X&&r.value.text==="null"?`Argument \`${l.green(o)}\` must not be ${l.red("null")}.`:`Argument \`${l.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath);if(!(n instanceof j))return;let[i,o]=gn(e.argumentPath),s=new fn,a=n.getDeepFieldValue(i);if(a instanceof j)if(r=a.getField(o),r&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let l of e.inputTypes[0].fields)s.addField(l.name,l.typeNames.join(" | "));a.addSuggestion(new je(o,s).makeRequired())}else{let l=e.inputTypes.map(xa).join(" | ");a.addSuggestion(new je(o,l).makeRequired())}}function xa(e){return e.kind==="list"?`${xa(e.elementType)}[]`:e.name}function Hp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath);n instanceof j&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=yn("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function zp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath);n instanceof j&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=yn("or",e.argument.typeNames.map(a=>i.green(a))),s=[`Invalid value for argument \`${i.bold(r)}\``];return e.underlyingError&&s.push(`: ${e.underlyingError}`),s.push(`. Expected ${o}.`),s.join("")})}function Yp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath),i;if(n instanceof j){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof X&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function Zp(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath);if(n instanceof j){let i=n.getDeepFieldValue(e.argumentPath);i instanceof j&&ba(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${yn("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(fr(i)),o.join(" ")})}function Xp(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath),i=[];if(n instanceof j){let o=n.getDeepFieldValue(e.argumentPath);o instanceof j&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${yn("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function Ea(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new je(r.name,"true"))}function ed(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new je(r.name,r.typeNames.join(" | ")))}function ba(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new je(r.name,r.typeNames.join(" | ")))}function gn(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function fr({green:e}){return`Available options are listed in ${e("green")}.`}function yn(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var td=3;function rd(e,t){let r=1/0,n;for(let i of t){let o=(0,ya.default)(e,i);o>td||o<r&&(r=o,n=i)}return n}var hn=class extends ze{constructor(){super(...arguments);this.items=[]}addItem(r){return this.items.push(r),this}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(n=>n.getPrintWidth()))+2}write(r){if(this.items.length===0){this.writeEmpty(r);return}this.writeWithItems(r)}writeEmpty(r){let n=new $e("[]");this.hasError&&n.setColor(r.context.colors.red).underline(),r.write(n)}writeWithItems(r){let{colors:n}=r.context;r.writeLine("[").withIndent(()=>r.writeJoined(vt,this.items).newLine()).write("]"),this.hasError&&r.afterNextNewline(()=>{r.writeLine(n.red("~".repeat(this.getPrintWidth())))})}};var wa=": ",xn=class{constructor(t,r){this.name=t;this.value=r;this.hasError=!1}markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+wa.length}write(t){let r=new $e(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(wa).write(this.value)}};var _i=class{constructor(t){this.errorMessages=[];this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function Ta(e){return new _i(Pa(e))}function Pa(e){let t=new j;for(let[r,n]of Object.entries(e)){let i=new xn(r,Ma(n));t.addField(i)}return t}function Ma(e){if(typeof e=="string")return new X(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new X(String(e));if(typeof e=="bigint")return new X(`${e}n`);if(e===null)return new X("null");if(e===void 0)return new X("undefined");if(Oe(e))return new X(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return Buffer.isBuffer(e)?new X(`Buffer.alloc(${e.byteLength})`):new X(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=De(e)?e.toISOString():"Invalid Date";return new X(`new Date("${t}")`)}if(e instanceof W)return new X(`Prisma.${e._getName()}`);if(ft(e))return new X(`prisma.${bt(e.modelName)}.$fields.${e.name}`);if(Array.isArray(e))return nd(e);if(typeof e=="object")return Pa(e);Te(e,"Unknown value type")}function nd(e){let t=new hn;for(let r of e)t.addItem(Ma(r));return t}function En({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i}){let o=Ta(e);for(let c of t)dn(c,o);let s=r==="pretty"?ga:fa,a=o.renderAllMessages(s),l=new pn(0,{colors:s}).write(o).toString(),u=ke({message:a,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:l});throw new H(u)}var id={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",update:"updateOne",updateMany:"updateMany",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"};function Aa({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i,callsite:o,clientMethod:s,errorFormat:a}){let l=new Ct({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a});return{modelName:e,action:id[t],query:Li(r,l)}}function Li({select:e,include:t,...r}={},n){return{arguments:Ca(r,n),selection:od(e,t,n)}}function od(e,t,r){return e&&t&&r.throwValidationError({kind:"IncludeAndSelect",selectionPath:r.getSelectionPath()}),e?ld(e,r):sd(r,t)}function sd(e,t){let r={};return e.model&&!e.isRawAction()&&(r.$composites=!0,r.$scalars=!0),t&&ad(r,t,e),r}function ad(e,t,r){for(let[n,i]of Object.entries(t)){let o=r.findField(n);o&&o?.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),i===!0?e[n]=!0:typeof i=="object"&&(e[n]=Li(i,r.nestSelection(n)))}}function ld(e,t){let r={},n=t.getComputedFields(),i=Zr(e,n);for(let[o,s]of Object.entries(i)){let a=t.findField(o);n?.[o]&&!a||(s===!0?r[o]=!0:typeof s=="object"&&(r[o]=Li(s,t.nestSelection(o))))}return r}function va(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(ue(e)){if(De(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(ft(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return ud(e,t);if(ArrayBuffer.isView(e))return{$type:"Bytes",value:Buffer.from(e).toString("base64")};if(cd(e))return e.values;if(Oe(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof W){if(e!==gt.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(typeof e=="object")return Ca(e,t);Te(e,"Unknown value type")}function Ca(e,t){if(e.$type)return{$type:"Json",value:JSON.stringify(e)};let r={};for(let n in e){let i=e[n];i!==void 0&&(r[n]=va(i,t.nestArgument(n)))}return r}function ud(e,t){let r=[];for(let n=0;n<e.length;n++){let i=e[n];i!==void 0&&r.push(va(i,t.nestArgument(String(n))))}return r}function cd(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}var Ct=class{constructor(t){this.params=t;this.params.modelName&&(this.model=this.params.runtimeDataModel.models[this.params.modelName])}throwValidationError(t){En({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.model))return{name:this.params.modelName,fields:this.model.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}getComputedFields(){if(!!this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.model?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new Ct({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}nestArgument(t){return new Ct({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};var gr=class{constructor(t,r){this.runtimeDataModel=t;this.errorFormat=r}createMessage(t){let r=Aa({...t,runtimeDataModel:this.runtimeDataModel,errorFormat:this.errorFormat});return new bn(r)}createBatch(t){return t.map(r=>r.toEngineQuery())}},pd={aggregate:!1,aggregateRaw:!1,createMany:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateOne:!0,upsertOne:!0},bn=class{constructor(t){this.query=t}isWrite(){return pd[this.query.action]}getBatchId(){if(this.query.action!=="findUnique"&&this.query.action!=="findUniqueOrThrow")return;let t=[];return this.query.modelName&&t.push(this.query.modelName),this.query.query.arguments&&t.push(qi(this.query.query.arguments)),t.push(qi(this.query.query.selection)),t.join("")}toDebugString(){return JSON.stringify(this.query,null,2)}toEngineQuery(){return this.query}deserializeResponse(t,r){if(!t)return t;let n=Object.values(t)[0],i=r.filter(o=>o!=="select"&&o!=="include");return cn(lr(n,i))}};function qi(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${qi(n)})`:r}).join(" ")})`}var Fa=e=>({command:e});var Ra=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);function yr(e){try{return Da(e,"fast")}catch{return Da(e,"slow")}}function Da(e,t){return JSON.stringify(e.map(r=>dd(r,t)))}function dd(e,t){return typeof e=="bigint"?{prisma__type:"bigint",prisma__value:e.toString()}:ue(e)?{prisma__type:"date",prisma__value:e.toJSON()}:le.isDecimal(e)?{prisma__type:"decimal",prisma__value:e.toJSON()}:Buffer.isBuffer(e)?{prisma__type:"bytes",prisma__value:e.toString("base64")}:md(e)||ArrayBuffer.isView(e)?{prisma__type:"bytes",prisma__value:Buffer.from(e).toString("base64")}:typeof e=="object"&&t==="slow"?Sa(e):e}function md(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function Sa(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(Oa);let t={};for(let r of Object.keys(e))t[r]=Oa(e[r]);return t}function Oa(e){return typeof e=="bigint"?e.toString():Sa(e)}var fd=/^(\s*alter\s)/i,Ia=re("prisma:client");function ji(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&fd.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var Bi=(e,t)=>r=>{let n="",i;if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:yr(s||[]),__prismaRawParameters__:!0}}else switch(e){case"sqlite":case"mysql":{n=r.sql,i={values:yr(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":{n=r.text,i={values:yr(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=Ra(r),i={values:yr(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${e} provider does not support ${t}`)}return i?.values?Ia(`prisma.${t}(${n}, ${i.values})`):Ia(`prisma.${t}(${n})`),{query:n,parameters:i}},ka={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new Z(t,r)}},$a={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};function Vi(e){return function(r){let n,i=(o=e)=>{try{return o===void 0||o?.kind==="itx"?n??(n=Na(r(o))):Na(r(o))}catch(s){return Promise.reject(s)}};return{then(o,s){return i().then(o,s)},catch(o){return i().catch(o)},finally(o){return i().finally(o)},requestTransaction(o){let s=i(o);return s.requestTransaction?s.requestTransaction(o):s},[Symbol.toStringTag]:"PrismaPromise"}}}function Na(e){return typeof e.then=="function"?e:Promise.resolve(e)}var _a={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},async createEngineSpan(){},getActiveContext(){},runInChildSpan(e,t){return t()}},Ui=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}createEngineSpan(t){return this.getGlobalTracingHelper().createEngineSpan(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){return globalThis.PRISMA_INSTRUMENTATION?.helper??_a}};function La(e){return e.includes("tracing")?new Ui:_a}function qa(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}function ja(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}var gd=["$connect","$disconnect","$on","$transaction","$use","$extends"],Ba=gd;function Ua(e,t,r){let n=Va(e,r),i=Va(t,r),o=Object.values(i).map(a=>a[a.length-1]),s=Object.keys(i);return Object.entries(n).forEach(([a,l])=>{s.includes(a)||o.push(l[l.length-1])}),o}var Va=(e,t)=>e.reduce((r,n)=>{let i=t(n);return r[i]||(r[i]=[]),r[i].push(n),r},{});var wn=class{constructor(){this._middlewares=[]}use(t){this._middlewares.push(t)}get(t){return this._middlewares[t]}has(t){return!!this._middlewares[t]}length(){return this._middlewares.length}};var Ja=K(Or());function Tn(e){return typeof e.batchRequestIdx=="number"}function Ka({result:e,modelName:t,select:r,extensions:n}){let i=n.getAllComputedFields(t);if(!i)return e;let o=[],s=[];for(let a of Object.values(i)){if(r){if(!r[a.name])continue;let l=a.needs.filter(u=>!r[u]);l.length>0&&s.push(Gt(l))}yd(e,a.needs)&&o.push(hd(a,Ie(e,o)))}return o.length>0||s.length>0?Ie(e,[...o,...s]):e}function yd(e,t){return t.every(r=>Qn(e,r))}function hd(e,t){return Xe(Ae(e.name,()=>e.compute(t)))}function Pn({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=Pn({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&Qa({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&Qa({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function Qa({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null)continue;let l=n.models[r].fields.find(c=>c.name===o);if(!l||l.kind!=="object"||!l.relationName)continue;let u=typeof s=="object"?s:{};t[o]=Pn({visitor:i,result:t[o],args:u,modelName:l.type,runtimeDataModel:n})}}var Mn=class{constructor(t){this.options=t;this.tickActive=!1;this.batches={}}request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,process.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};var xd=re("prisma:client:request_handler"),An=class{constructor(t,r){this.logEmitter=r,this.client=t,this.dataloader=new Mn({batchLoader:ia(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,protocolEncoder:s,otelParentCtx:a}=n[0],l=s.createBatch(n.map(d=>d.protocolMessage)),u=this.client._tracingHelper.getTraceParent(a),c=n.some(d=>d.protocolMessage.isWrite());return(await this.client._engine.requestBatch(l,{traceparent:u,transaction:Ed(o),containsWrite:c,customDataProxyFetch:i})).map((d,m)=>{if(d instanceof Error)return d;try{return this.mapQueryEngineResult(n[m],d)}catch(g){return g}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?Ga(n.transaction):void 0,o=await this.client._engine.request(n.protocolMessage.toEngineQuery(),{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:n.protocolMessage.isWrite(),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:n.protocolMessage.getBatchId(),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{let r=await this.dataloader.request(t);return zs(r,t.clientMethod,t.modelName,t.rejectOnNotFound),r}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s})}}mapQueryEngineResult({protocolMessage:t,dataPath:r,unpacker:n,modelName:i,args:o,extensions:s},a){let l=a?.data,u=a?.elapsed,c=this.unpack(t,l,r,n);return i&&(c=this.applyResultExtensions({result:c,modelName:i,args:o,extensions:s})),process.env.PRISMA_CLIENT_GET_TIME?{data:c,elapsed:u}:c}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o}){if(xd(t),bd(t,i)||t instanceof be)throw t;if(t instanceof ne&&wd(t)){let a=Wa(t.meta);En({args:o,errors:[a],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r})}let s=t.message;throw n&&(s=ke({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:s})),s=this.sanitizeMessage(s),t.code?new ne(s,{code:t.code,clientVersion:this.client._clientVersion,meta:t.meta,batchRequestIdx:t.batchRequestIdx}):t.isPanic?new Ne(s,this.client._clientVersion):t instanceof he?new he(s,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx}):t instanceof pe?new pe(s,this.client._clientVersion):t instanceof Ne?new Ne(s,this.client._clientVersion):(t.clientVersion=this.client._clientVersion,t)}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,Ja.default)(t):t}unpack(t,r,n,i){if(!r)return r;r.data&&(r=r.data);let o=t.deserializeResponse(r,n);return i?i(o):o}applyResultExtensions({result:t,modelName:r,args:n,extensions:i}){return i.isEmpty()||t==null||!this.client._runtimeDataModel.models[r]?t:Pn({result:t,args:n??{},modelName:r,runtimeDataModel:this.client._runtimeDataModel,visitor(s,a,l){let u=Ee(a);return Ka({result:s,modelName:u,select:l.select,extensions:i})}})}get[Symbol.toStringTag](){return"RequestHandler"}};function Ed(e){if(!!e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:Ga(e)};Te(e,"Unknown transaction kind")}}function Ga(e){return{id:e.id,payload:e.payload}}function bd(e,t){return Tn(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function wd(e){return e.code==="P2009"||e.code==="P2012"}function Wa(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(Wa)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}function Ha(e){return e.map(t=>{let r={};for(let n of Object.keys(t))r[n]=za(t[n]);return r})}function za({prisma__type:e,prisma__value:t}){switch(e){case"bigint":return BigInt(t);case"bytes":return Buffer.from(t,"base64");case"decimal":return new le(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"array":return t.map(za);default:return t}}var el=K(jr());var Ya=["datasources","errorFormat","log","__internal","rejectOnNotFound"],Za=["pretty","colorless","minimal"],Xa=["info","query","warn","error"],Td={datasources:(e,t)=>{if(!!e){if(typeof e!="object"||Array.isArray(e))throw new J(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=Ft(r,t)||`Available datasources: ${t.join(", ")}`;throw new J(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new J(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new J(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new J(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},errorFormat:e=>{if(!!e){if(typeof e!="string")throw new J(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!Za.includes(e)){let t=Ft(e,Za);throw new J(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new J(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!Xa.includes(r)){let n=Ft(r,Xa);throw new J(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=Ft(i,o);throw new J(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new J(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},__internal:e=>{if(!e)return;let t=["debug","hooks","engine","measurePerformance"];if(typeof e!="object")throw new J(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=Ft(r,t);throw new J(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}},rejectOnNotFound:e=>{if(!!e){if(St(e)||typeof e=="boolean"||typeof e=="object"||typeof e=="function")return e;throw new J(`Invalid rejectOnNotFound expected a boolean/Error/{[modelName: Error | boolean]} but received ${JSON.stringify(e)}`)}}};function tl(e,t){for(let[r,n]of Object.entries(e)){if(!Ya.includes(r)){let i=Ft(r,Ya);throw new J(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}Td[r](n,t)}}function Ft(e,t){if(t.length===0||typeof e!="string")return"";let r=Pd(e,t);return r?` Did you mean "${r}"?`:""}function Pd(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,el.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function rl(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},l=u=>{o||(o=!0,r(u))};for(let u=0;u<e.length;u++)e[u].then(c=>{n[u]=c,a()},c=>{if(!Tn(c)){l(c);return}c.batchRequestIdx===u?l(c):(i||(i=c),a())})})}var Fe=re("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var Md={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},Ad=Symbol.for("prisma.client.transaction.id"),vd={id:0,nextId(){return++this.id}};function al(e){class t{constructor(n){this._middlewares=new wn;this._createPrismaPromise=Vi();this._getDmmf=Sr(async n=>{try{let i=await this._tracingHelper.runInChildSpan({name:"getDmmf",internal:!0},()=>this._engine.getDmmf());return this._tracingHelper.runInChildSpan({name:"processDmmf",internal:!0},()=>new Ge(is(i)))}catch(i){this._fetcher.handleAndLogRequestError({...n,args:{},error:i})}});this._getProtocolEncoder=Sr(async n=>this._engineConfig.engineProtocol==="json"?new gr(this._runtimeDataModel,this._errorFormat):(this._dmmf===void 0&&(this._dmmf=await this._getDmmf(n)),new un(this._dmmf,this._errorFormat)));this.$extends=Xs;ua(e),n&&tl(n,e.datasourceNames);let i=new ol.EventEmitter().on("error",()=>{});this._extensions=He.empty(),this._previewFeatures=e.generator?.previewFeatures??[],this._rejectOnNotFound=n?.rejectOnNotFound,this._clientVersion=e.clientVersion??sn,this._activeProvider=e.activeProvider,this._dataProxy=e.dataProxy,this._tracingHelper=La(this._previewFeatures),this._clientEngineType=jn(e.generator);let o={rootEnvPath:e.relativeEnvPaths.rootEnvPath&&hr.default.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&hr.default.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s=Ot(o,{conflictCheck:"none"});try{let a=n??{},l=a.__internal??{},u=l.debug===!0;u&&re.enable("prisma:client");let c=hr.default.resolve(e.dirname,e.relativePath);sl.default.existsSync(c)||(c=e.dirname),Fe("dirname",e.dirname),Fe("relativePath",e.relativePath),Fe("cwd",c);let p=a.datasources||{},d=Object.entries(p).filter(([E,x])=>x&&x.url).map(([E,{url:x}])=>({name:E,url:x})),m=Ua([],d,E=>E.name),g=l.engine||{};a.errorFormat?this._errorFormat=a.errorFormat:process.env.NODE_ENV==="production"?this._errorFormat="minimal":process.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",e.runtimeDataModel?this._runtimeDataModel=e.runtimeDataModel:this._runtimeDataModel=Oo(e.document.datamodel);let f=Vn(e.generator);if(Fe("protocol",f),e.document&&(this._dmmf=new Ge(e.document)),this._engineConfig={cwd:c,dirname:e.dirname,enableDebugLogs:u,allowTriggerPanic:g.allowTriggerPanic,datamodelPath:hr.default.join(e.dirname,e.filename??"schema.prisma"),prismaPath:g.binaryPath??void 0,engineEndpoint:g.endpoint,datasources:m,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:a.log&&ja(a.log),logQueries:a.log&&Boolean(typeof a.log=="string"?a.log==="query":a.log.find(E=>typeof E=="string"?E==="query":E.level==="query")),env:s?.parsed??e.injectableEdgeEnv?.parsed??{},flags:[],clientVersion:e.clientVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,logEmitter:i,engineProtocol:f,isBundled:e.isBundled},Fe("clientVersion",e.clientVersion),Fe("clientEngineType",this._dataProxy?"dataproxy":this._clientEngineType),this._dataProxy&&Fe("using Data Proxy with Node.js runtime"),this._engine=this.getEngine(),this._fetcher=new An(this,i),a.log)for(let E of a.log){let x=typeof E=="string"?E:E.emit==="stdout"?E.level:null;x&&this.$on(x,b=>{kt.log(`${kt.tags[x]??""}`,b.message||b.query)})}this._metrics=new pt(this._engine)}catch(a){throw a.clientVersion=this._clientVersion,a}return dr(this)}get[Symbol.toStringTag](){return"PrismaClient"}getEngine(){if(this._dataProxy===!0)return new sr(this._engineConfig);throw this._clientEngineType,"library",this._clientEngineType,"binary",new H("Invalid client engine type, please use `library` or `binary`")}$use(n){this._middlewares.use(n)}$on(n,i){n==="beforeExit"?this._engine.on("beforeExit",i):this._engine.on(n,o=>{let s=o.fields;return i(n==="query"?{timestamp:o.timestamp,query:s?.query??o.query,params:s?.params??o.params,duration:s?.duration_ms??o.duration,target:o.target}:{timestamp:o.timestamp,message:s?.message??o.message,target:o.target})})}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async _runDisconnect(){await this._engine.stop(),delete this._connectionPromise,this._engine=this.getEngine(),delete this._disconnectionPromise}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{fo(),this._dataProxy||(this._dmmf=void 0)}}$executeRawInternal(n,i,o,s){return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:Bi(this._activeProvider,i),callsite:We(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=nl(n,i);return ji(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new H("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n")})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(ji(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new H(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`);return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:Fa,callsite:We(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:Bi(this._activeProvider,i),callsite:We(this._errorFormat),dataPath:[],middlewareArgsMapper:s}).then(Ha)}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...nl(n,i));throw new H("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n")})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=vd.nextId(),s=qa(n.length),a=n.map((l,u)=>{if(l?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let c=i?.isolationLevel,p={kind:"batch",id:o,index:u,isolationLevel:c,lock:s};return l.requestTransaction?.(p)??l});return rl(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s=await this._engine.transaction("start",o,i),a;try{let l={kind:"itx",...s};a=await n(this._createItxClient(l)),await this._engine.transaction("commit",o,s)}catch(l){throw await this._engine.transaction("rollback",o,s).catch(()=>{}),l}return a}_createItxClient(n){let i=an(this);return dr(Ie(i,[Ae("_createPrismaPromise",()=>Vi(n)),Ae(Ad,()=>n.id),Gt(Ba)]))}$transaction(n,i){let o;typeof n=="function"?o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??Md,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:Boolean(n.transaction),action:n.action,model:n.model},s={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:o.action,model:o.model,name:`${o.model}.${o.action}`}}},a=-1,l=u=>{let c=this._middlewares.get(++a);if(c)return this._tracingHelper.runInChildSpan(s.middleware,f=>c(u,E=>(f?.end(),l(E))));let{runInTransaction:p,args:d,...m}=u,g={...n,...m};return d&&(g.args=i.middlewareArgsToRequestArgs(d)),n.transaction!==void 0&&p===!1&&delete g.transaction,na(this,g)};return this._tracingHelper.runInChildSpan(s.operation,()=>new il.AsyncResource("prisma-client-request").runInAsyncScope(()=>l(o)))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:l,argsMapper:u,transaction:c,unpacker:p,otelParentCtx:d,customDataProxyFetch:m}){try{let g=await this._getProtocolEncoder({clientMethod:i,callsite:s});n=u?u(n):n;let f={name:"serialize"},E;l&&(E=Ii(a,l,n,this._rejectOnNotFound),Fd(E,l,a));let x=this._tracingHelper.runInChildSpan(f,()=>g.createMessage({modelName:l,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions}));return re.enabled("prisma:client")&&(Fe("Prisma Client call:"),Fe(`prisma.${i}(${en({ast:n,keyPaths:[],valuePaths:[],missingItems:[]})})`),Fe("Generated request:"),Fe(x.toDebugString()+`
`)),c?.kind==="batch"&&await c.lock,this._fetcher.request({protocolMessage:x,protocolEncoder:g,modelName:l,action:a,clientMethod:i,dataPath:o,rejectOnNotFound:E,callsite:s,args:n,extensions:this._extensions,transaction:c,unpacker:p,otelParentCtx:d,otelChildCtx:this._tracingHelper.getActiveContext(),customDataProxyFetch:m})}catch(g){throw g.clientVersion=this._clientVersion,g}}get $metrics(){if(!this._hasPreviewFlag("metrics"))throw new H("`metrics` preview feature must be enabled in order to access metrics API");return this._metrics}_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}}return t}var Cd={findUnique:"findUniqueOrThrow",findFirst:"findFirstOrThrow"};function Fd(e,t,r){if(e){let n=Cd[r],i=t?`prisma.${Ee(t)}.${n}`:`prisma.${n}`,o=`rejectOnNotFound.${t??""}.${r}`;Nt(o,`\`rejectOnNotFound\` option is deprecated and will be removed in Prisma 5. Please use \`${i}\` method instead`)}}function nl(e,t){return Rd(e)?[new Z(e,t),ka]:[e,$a]}function Rd(e){return Array.isArray(e)&&Array.isArray(e.raw)}var Dd=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function ll(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!Dd.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}var pl=K(cl()),dl=e=>pl.decompressFromBase64(e);function ml(e){Ot(e,{conflictCheck:"warn"})}0&&(module.exports={DMMF,DMMFClass,Debug,Decimal,Extensions,MetricsClient,NotFoundError,PrismaClientInitializationError,PrismaClientKnownRequestError,PrismaClientRustPanicError,PrismaClientUnknownRequestError,PrismaClientValidationError,Public,Sql,Types,decompressFromBase64,defineDmmfProperty,empty,getPrismaClient,join,makeDocument,makeStrictEnum,objectEnumValues,raw,sqltag,transformDocument,unpack,warnEnvConflicts,warnOnce});
/*!
 *  decimal.js v10.4.3
 *  An arbitrary-precision Decimal type for JavaScript.
 *  https://github.com/MikeMcl/decimal.js
 *  Copyright (c) 2022 Michael Mclaughlin <<EMAIL>>
 *  MIT Licence
 */
/*!
 * @description Recursive object extending
 * <AUTHOR> Lotsmanov <<EMAIL>>
 * @license MIT
 *
 * The MIT License (MIT)
 *
 * Copyright (c) 2013-2018 Viacheslav Lotsmanov
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
//# sourceMappingURL=data-proxy.js.map
