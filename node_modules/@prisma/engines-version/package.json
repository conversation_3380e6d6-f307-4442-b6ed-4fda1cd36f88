{"name": "@prisma/engines-version", "version": "4.16.1-1.4bc8b6e1b66cb932731fb1bdbbc550d1e010de81", "main": "index.js", "types": "index.d.ts", "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "prisma": {"enginesVersion": "4bc8b6e1b66cb932731fb1bdbbc550d1e010de81"}, "repository": {"type": "git", "url": "https://github.com/prisma/engines-wrapper.git", "directory": "packages/engines-version"}, "devDependencies": {"@types/node": "18.16.18", "typescript": "4.9.5"}, "files": ["index.js", "index.d.ts"], "scripts": {"build": "tsc -d"}}