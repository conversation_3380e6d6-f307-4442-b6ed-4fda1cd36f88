{"name": "@prisma/engines", "version": "4.16.2", "description": "This package is intended for Prisma's internal use", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "devDependencies": {"@prisma/engines-version": "4.16.1-1.4bc8b6e1b66cb932731fb1bdbbc550d1e010de81", "@swc/core": "1.3.64", "@swc/jest": "0.2.26", "@types/jest": "29.5.2", "@types/node": "18.16.16", "execa": "5.1.1", "jest": "29.5.0", "typescript": "4.9.5", "@prisma/debug": "4.16.2", "@prisma/fetch-engine": "4.16.2", "@prisma/get-platform": "4.16.2"}, "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/engines"}, "files": ["dist", "download", "scripts"], "sideEffects": false, "scripts": {"dev": "DEV=true node -r esbuild-register helpers/build.ts", "build": "node -r esbuild-register helpers/build.ts", "test": "jest --passWithNoTests", "postinstall": "node scripts/postinstall.js"}}