{"name": "@radix-ui/react-icons", "private": false, "version": "1.3.2", "license": "MIT", "description": "Radix UI React Icon Set", "authors": ["<PERSON> <<EMAIL>>", "<PERSON>", "Colm Tuite <<EMAIL>>"], "module": "dist/react-icons.esm.js", "main": "index.js", "types": "dist/index.d.ts", "files": ["outline/", "solid/", "dist/", "index.js", "manifest.json"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"generate-src": "generate-icon-lib --file=9Df5CaFUEomVzn20gRpaX3 && mv ./icons/15/* ./icons && rmdir ./icons/15 && cd ./icons && zip -r ../../../radix-icons.zip ./", "build": "rimraf dist && tsdx build --format es,cjs --entry src/index.tsx", "watch": "rimraf dist && tsdx watch --format es,cjs --entry src/index.tsx", "prepublishOnly": "npm run build"}, "peerDependencies": {"react": "^16.x || ^17.x || ^18.x || ^19.0.0 || ^19.0.0-rc"}, "devDependencies": {"@modulz/generate-icon-lib": "^0.2.1", "tsdx": "0.14.0"}, "gitHead": "bde33b13aa5848555f5512ac12155930fb4beb7d"}