import{rectToClientRect as t,arrow as e,autoPlacement as n,detectOverflow as o,flip as i,hide as r,inline as c,limitShift as l,offset as s,shift as f,size as u,computePosition as a}from"@floating-ui/core";const h=Math.min,d=Math.max,p=Math.round,g=Math.floor,m=t=>({x:t,y:t});function w(){return"undefined"!=typeof window}function y(t){return b(t)?(t.nodeName||"").toLowerCase():"#document"}function x(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function v(t){var e;return null==(e=(b(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function b(t){return!!w()&&(t instanceof Node||t instanceof x(t).Node)}function L(t){return!!w()&&(t instanceof Element||t instanceof x(t).Element)}function T(t){return!!w()&&(t instanceof HTMLElement||t instanceof x(t).HTMLElement)}function R(t){return!(!w()||"undefined"==typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof x(t).ShadowRoot)}const S=new Set(["inline","contents"]);function E(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=A(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!S.has(i)}const C=new Set(["table","td","th"]);function F(t){return C.has(y(t))}const D=[":popover-open",":modal"];function O(t){return D.some((e=>{try{return t.matches(e)}catch(t){return!1}}))}const H=["transform","translate","scale","rotate","perspective"],W=["transform","translate","scale","rotate","perspective","filter"],M=["paint","layout","strict","content"];function P(t){const e=z(),n=L(t)?A(t):t;return H.some((t=>!!n[t]&&"none"!==n[t]))||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||W.some((t=>(n.willChange||"").includes(t)))||M.some((t=>(n.contain||"").includes(t)))}function z(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}const B=new Set(["html","body","#document"]);function V(t){return B.has(y(t))}function A(t){return x(t).getComputedStyle(t)}function N(t){return L(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function k(t){if("html"===y(t))return t;const e=t.assignedSlot||t.parentNode||R(t)&&t.host||v(t);return R(e)?e.host:e}function I(t){const e=k(t);return V(e)?t.ownerDocument?t.ownerDocument.body:t.body:T(e)&&E(e)?e:I(e)}function q(t,e,n){var o;void 0===e&&(e=[]),void 0===n&&(n=!0);const i=I(t),r=i===(null==(o=t.ownerDocument)?void 0:o.body),c=x(i);if(r){const t=X(c);return e.concat(c,c.visualViewport||[],E(i)?i:[],t&&n?q(t):[])}return e.concat(i,q(i,[],n))}function X(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Y(t){const e=A(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const i=T(t),r=i?t.offsetWidth:n,c=i?t.offsetHeight:o,l=p(n)!==r||p(o)!==c;return l&&(n=r,o=c),{width:n,height:o,$:l}}function $(t){return L(t)?t:t.contextElement}function _(t){const e=$(t);if(!T(e))return m(1);const n=e.getBoundingClientRect(),{width:o,height:i,$:r}=Y(e);let c=(r?p(n.width):n.width)/o,l=(r?p(n.height):n.height)/i;return c&&Number.isFinite(c)||(c=1),l&&Number.isFinite(l)||(l=1),{x:c,y:l}}const j=m(0);function G(t){const e=x(t);return z()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:j}function J(e,n,o,i){void 0===n&&(n=!1),void 0===o&&(o=!1);const r=e.getBoundingClientRect(),c=$(e);let l=m(1);n&&(i?L(i)&&(l=_(i)):l=_(e));const s=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==x(t))&&e}(c,o,i)?G(c):m(0);let f=(r.left+s.x)/l.x,u=(r.top+s.y)/l.y,a=r.width/l.x,h=r.height/l.y;if(c){const t=x(c),e=i&&L(i)?x(i):i;let n=t,o=X(n);for(;o&&i&&e!==n;){const t=_(o),e=o.getBoundingClientRect(),i=A(o),r=e.left+(o.clientLeft+parseFloat(i.paddingLeft))*t.x,c=e.top+(o.clientTop+parseFloat(i.paddingTop))*t.y;f*=t.x,u*=t.y,a*=t.x,h*=t.y,f+=r,u+=c,n=x(o),o=X(n)}}return t({width:a,height:h,x:f,y:u})}function K(t,e){const n=N(t).scrollLeft;return e?e.left+n:J(v(t)).left+n}function Q(t,e,n){void 0===n&&(n=!1);const o=t.getBoundingClientRect();return{x:o.left+e.scrollLeft-(n?0:K(t,o)),y:o.top+e.scrollTop}}const U=new Set(["absolute","fixed"]);function Z(e,n,o){let i;if("viewport"===n)i=function(t,e){const n=x(t),o=v(t),i=n.visualViewport;let r=o.clientWidth,c=o.clientHeight,l=0,s=0;if(i){r=i.width,c=i.height;const t=z();(!t||t&&"fixed"===e)&&(l=i.offsetLeft,s=i.offsetTop)}return{width:r,height:c,x:l,y:s}}(e,o);else if("document"===n)i=function(t){const e=v(t),n=N(t),o=t.ownerDocument.body,i=d(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),r=d(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let c=-n.scrollLeft+K(t);const l=-n.scrollTop;return"rtl"===A(o).direction&&(c+=d(e.clientWidth,o.clientWidth)-i),{width:i,height:r,x:c,y:l}}(v(e));else if(L(n))i=function(t,e){const n=J(t,!0,"fixed"===e),o=n.top+t.clientTop,i=n.left+t.clientLeft,r=T(t)?_(t):m(1);return{width:t.clientWidth*r.x,height:t.clientHeight*r.y,x:i*r.x,y:o*r.y}}(n,o);else{const t=G(e);i={x:n.x-t.x,y:n.y-t.y,width:n.width,height:n.height}}return t(i)}function tt(t,e){const n=k(t);return!(n===e||!L(n)||V(n))&&("fixed"===A(n).position||tt(n,e))}function et(t,e,n){const o=T(e),i=v(e),r="fixed"===n,c=J(t,!0,r,e);let l={scrollLeft:0,scrollTop:0};const s=m(0);function f(){s.x=K(i)}if(o||!o&&!r)if(("body"!==y(e)||E(i))&&(l=N(e)),o){const t=J(e,!0,r,e);s.x=t.x+e.clientLeft,s.y=t.y+e.clientTop}else i&&f();r&&!o&&i&&f();const u=!i||o||r?m(0):Q(i,l);return{x:c.left+l.scrollLeft-s.x-u.x,y:c.top+l.scrollTop-s.y-u.y,width:c.width,height:c.height}}function nt(t){return"static"===A(t).position}function ot(t,e){if(!T(t)||"fixed"===A(t).position)return null;if(e)return e(t);let n=t.offsetParent;return v(t)===n&&(n=n.ownerDocument.body),n}function it(t,e){const n=x(t);if(O(t))return n;if(!T(t)){let e=k(t);for(;e&&!V(e);){if(L(e)&&!nt(e))return e;e=k(e)}return n}let o=ot(t,e);for(;o&&F(o)&&nt(o);)o=ot(o,e);return o&&V(o)&&nt(o)&&!P(o)?n:o||function(t){let e=k(t);for(;T(e)&&!V(e);){if(P(e))return e;if(O(e))return null;e=k(e)}return null}(t)||n}const rt={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:o,strategy:i}=t;const r="fixed"===i,c=v(o),l=!!e&&O(e.floating);if(o===c||l&&r)return n;let s={scrollLeft:0,scrollTop:0},f=m(1);const u=m(0),a=T(o);if((a||!a&&!r)&&(("body"!==y(o)||E(c))&&(s=N(o)),T(o))){const t=J(o);f=_(o),u.x=t.x+o.clientLeft,u.y=t.y+o.clientTop}const h=!c||a||r?m(0):Q(c,s,!0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-s.scrollLeft*f.x+u.x+h.x,y:n.y*f.y-s.scrollTop*f.y+u.y+h.y}},getDocumentElement:v,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:o,strategy:i}=t;const r=[..."clippingAncestors"===n?O(e)?[]:function(t,e){const n=e.get(t);if(n)return n;let o=q(t,[],!1).filter((t=>L(t)&&"body"!==y(t))),i=null;const r="fixed"===A(t).position;let c=r?k(t):t;for(;L(c)&&!V(c);){const e=A(c),n=P(c);n||"fixed"!==e.position||(i=null),(r?!n&&!i:!n&&"static"===e.position&&i&&U.has(i.position)||E(c)&&!n&&tt(t,c))?o=o.filter((t=>t!==c)):i=e,c=k(c)}return e.set(t,o),o}(e,this._c):[].concat(n),o],c=r[0],l=r.reduce(((t,n)=>{const o=Z(e,n,i);return t.top=d(o.top,t.top),t.right=h(o.right,t.right),t.bottom=h(o.bottom,t.bottom),t.left=d(o.left,t.left),t}),Z(e,c,i));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:it,getElementRects:async function(t){const e=this.getOffsetParent||it,n=this.getDimensions,o=await n(t.floating);return{reference:et(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=Y(t);return{width:e,height:n}},getScale:_,isElement:L,isRTL:function(t){return"rtl"===A(t).direction}};function ct(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function lt(t,e,n,o){void 0===o&&(o={});const{ancestorScroll:i=!0,ancestorResize:r=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:s=!1}=o,f=$(t),u=i||r?[...f?q(f):[],...q(e)]:[];u.forEach((t=>{i&&t.addEventListener("scroll",n,{passive:!0}),r&&t.addEventListener("resize",n)}));const a=f&&l?function(t,e){let n,o=null;const i=v(t);function r(){var t;clearTimeout(n),null==(t=o)||t.disconnect(),o=null}return function c(l,s){void 0===l&&(l=!1),void 0===s&&(s=1),r();const f=t.getBoundingClientRect(),{left:u,top:a,width:p,height:m}=f;if(l||e(),!p||!m)return;const w={rootMargin:-g(a)+"px "+-g(i.clientWidth-(u+p))+"px "+-g(i.clientHeight-(a+m))+"px "+-g(u)+"px",threshold:d(0,h(1,s))||1};let y=!0;function x(e){const o=e[0].intersectionRatio;if(o!==s){if(!y)return c();o?c(!1,o):n=setTimeout((()=>{c(!1,1e-7)}),1e3)}1!==o||ct(f,t.getBoundingClientRect())||c(),y=!1}try{o=new IntersectionObserver(x,{...w,root:i.ownerDocument})}catch(t){o=new IntersectionObserver(x,w)}o.observe(t)}(!0),r}(f,n):null;let p,m=-1,w=null;c&&(w=new ResizeObserver((t=>{let[o]=t;o&&o.target===f&&w&&(w.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame((()=>{var t;null==(t=w)||t.observe(e)}))),n()})),f&&!s&&w.observe(f),w.observe(e));let y=s?J(t):null;return s&&function e(){const o=J(t);y&&!ct(y,o)&&n();y=o,p=requestAnimationFrame(e)}(),n(),()=>{var t;u.forEach((t=>{i&&t.removeEventListener("scroll",n),r&&t.removeEventListener("resize",n)})),null==a||a(),null==(t=w)||t.disconnect(),w=null,s&&cancelAnimationFrame(p)}}const st=o,ft=s,ut=n,at=f,ht=i,dt=u,pt=r,gt=e,mt=c,wt=l,yt=(t,e,n)=>{const o=new Map,i={platform:rt,...n},r={...i.platform,_c:o};return a(t,e,{...i,platform:r})};export{gt as arrow,ut as autoPlacement,lt as autoUpdate,yt as computePosition,st as detectOverflow,ht as flip,q as getOverflowAncestors,pt as hide,mt as inline,wt as limitShift,ft as offset,rt as platform,at as shift,dt as size};
