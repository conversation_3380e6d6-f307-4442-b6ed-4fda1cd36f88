var p=Object.defineProperty;var a=Object.getOwnPropertyDescriptor;var H=Object.getOwnPropertyNames;var J=Object.prototype.hasOwnProperty;var k=(_,E)=>{for(var f in E)p(_,f,{get:E[f],enumerable:!0})},m=(_,E,f,C)=>{if(E&&typeof E=="object"||typeof E=="function")for(let c of H(E))!J.call(_,c)&&c!==f&&p(_,c,{get:()=>E[c],enumerable:!(C=a(E,c))||C.enumerable});return _};var B=_=>m(p({},"__esModule",{value:!0}),_);var Z={};k(Z,{commandScore:()=>V});module.exports=B(Z);var D=1,K=.9,W=.8,$=.17,u=.1,G=.999,y=.9999;var F=.99,j=/[\\\/_+.#"@\[\(\{&]/,q=/[\\\/_+.#"@\[\(\{&]/g,Q=/[\s-]/,Y=/[\s-]/g;function L(_,E,f,C,c,P,O){if(P===E.length)return c===_.length?D:F;var T=`${c},${P}`;if(O[T]!==void 0)return O[T];for(var U=C.charAt(P),A=f.indexOf(U,c),S=0,h,N,R,M;A>=0;)h=L(_,E,f,C,A+1,P+1,O),h>S&&(A===c?h*=D:j.test(_.charAt(A-1))?(h*=W,R=_.slice(c,A-1).match(q),R&&c>0&&(h*=Math.pow(G,R.length))):Q.test(_.charAt(A-1))?(h*=K,M=_.slice(c,A-1).match(Y),M&&c>0&&(h*=Math.pow(G,M.length))):(h*=$,c>0&&(h*=Math.pow(G,A-c))),_.charAt(A)!==E.charAt(P)&&(h*=y)),(h<u&&f.charAt(A-1)===C.charAt(P+1)||C.charAt(P+1)===C.charAt(P)&&f.charAt(A-1)!==C.charAt(P))&&(N=L(_,E,f,C,A+1,P+2,O),N*u>h&&(h=N*u)),h>S&&(S=h),A=f.indexOf(U,A+1);return O[T]=S,S}function X(_){return _.toLowerCase().replace(Y," ")}function V(_,E){return L(_,E,X(_),X(E),0,0,{})}0&&(module.exports={commandScore});
