function $e42e1063c40fb3ef$export$b9ecd428b558ff10(originalE<PERSON><PERSON><PERSON><PERSON>, ourEventHandler, { checkForDefaultPrevented: checkForDefaultPrevented = true  } = {}) {
    return function handleEvent(event) {
        originalEventHandler === null || originalEventHandler === void 0 || originalEventHandler(event);
        if (checkForDefaultPrevented === false || !event.defaultPrevented) return ourEventHandler === null || ourEventHandler === void 0 ? void 0 : ourEventHandler(event);
    };
}




export {$e42e1063c40fb3ef$export$b9ecd428b558ff10 as composeEventHandlers};
//# sourceMappingURL=index.module.js.map
