{"mappings": ";;ACAA;AAEA,SAASA,yCAAT,CACEG,iBADF,EAEEC,cAFF,EAGE;IACA,MAAMC,OAAO,GAAA,aAAGH,CAAAA,oBAAA,CAAkDE,cAAlD,CAAhB,AAAA;IAEA,SAASE,QAAT,CAAkBC,KAAlB,EAA2E;QACzE,MAAM,EATV,UASYC,QAAF,CAAA,EAAY,GAAGC,OAAH,EAAZ,GAA2BF,KAAjC,AADyE,EAEzE,0CADM;QAEN,uDAAA;QACA,MAAMG,KAAK,GAAGR,cAAA,CAAc,IAAMO,OAApB;QAAA,EAA6BG,MAAM,CAACC,MAAP,CAAcJ,OAAd,CAA7B,CAAd,AAAA;QACA,OAAA,aAAO,CAAA,oBAAA,CAAC,OAAD,CAAS,QAAT,EAAP;YAAyB,KAAK,EAAEC,KAAP;SAAlB,EAAiCF,QAAjC,CAAP,CAAO;KACR;IAED,SAASM,UAAT,CAAoBC,YAApB,EAA0C;QACxC,MAAMN,OAAO,GAAGP,iBAAA,CAAiBG,OAAjB,CAAhB,AAAA;QACA,IAAII,OAAJ,EAAa,OAAOA,OAAP,CAAb;QACA,IAAIL,cAAc,KAAKY,SAAvB,EAAkC,OAAOZ,cAAP,CAHM,CAIxC,iEADA;QAEA,MAAM,IAAIa,KAAJ,CAAW,CAAA,EAAA,EAAIF,YAAa,CAAA,yBAAA,EAA2BZ,iBAAkB,CAAA,EAAA,CAAzE,CAAN,CAAA;KACD;IAEDG,QAAQ,CAACY,WAAT,GAAuBf,iBAAiB,GAAG,UAA3C,CAAAG;IACA,OAAO;QAACA,QAAD;QAAWQ,UAAX;KAAP,CAAA;CACD;AAED;;oGAEA,CASA,SAASb,wCAAT,CAA4BkB,SAA5B,EAA+CC,sBAAqC,GAAG,EAAvF,EAA2F;IACzF,IAAIC,eAAsB,GAAG,EAA7B,AAAA;IAEA;;oGAEF,CAEE,SAASrB,yCAAT,CACEG,iBADF,EAEEC,cAFF,EAGE;QACA,MAAMkB,WAAW,GAAA,aAAGpB,CAAAA,oBAAA,CAAkDE,cAAlD,CAApB,AAAA;QACA,MAAMmB,KAAK,GAAGF,eAAe,CAACG,MAA9B,AAAA;QACAH,eAAe,GAAG;eAAIA,eAAJ;YAAqBjB,cAArB;SAAlB,CAAAiB;QAEA,SAASf,QAAT,CACEC,KADF,EAEE;YACA,MAAM,EAzDZ,OAyDckB,KAAF,CAAA,EAzDZ,UAyDqBjB,QAAT,CAAA,EAAmB,GAAGC,OAAH,EAAnB,GAAkCF,KAAxC,AAAM;YACN,MAAMF,OAAO,GAAG,AAAAoB,CAAAA,KAAK,KAAA,IAAL,IAAAA,KAAK,KAAA,KAAA,CAAL,GAAA,KAAA,CAAA,GAAAA,KAAK,CAAGN,SAAH,CAAL,CAAmBI,KAAnB,CAAA,CAAA,IAA6BD,WAA7C,AAFA,EAGA,0CADA;YAEA,uDAAA;YACA,MAAMZ,KAAK,GAAGR,cAAA,CAAc,IAAMO,OAApB;YAAA,EAA6BG,MAAM,CAACC,MAAP,CAAcJ,OAAd,CAA7B,CAAd,AAAA;YACA,OAAA,aAAO,CAAA,oBAAA,CAAC,OAAD,CAAS,QAAT,EAAP;gBAAyB,KAAK,EAAEC,KAAP;aAAlB,EAAiCF,QAAjC,CAAP,CAAO;SACR;QAED,SAASM,UAAT,CAAoBC,YAApB,EAA0CU,KAA1C,EAAsF;YACpF,MAAMpB,OAAO,GAAG,AAAAoB,CAAAA,KAAK,KAAA,IAAL,IAAAA,KAAK,KAAA,KAAA,CAAL,GAAA,KAAA,CAAA,GAAAA,KAAK,CAAGN,SAAH,CAAL,CAAmBI,KAAnB,CAAA,CAAA,IAA6BD,WAA7C,AAAA;YACA,MAAMb,OAAO,GAAGP,iBAAA,CAAiBG,OAAjB,CAAhB,AAAA;YACA,IAAII,OAAJ,EAAa,OAAOA,OAAP,CAAb;YACA,IAAIL,cAAc,KAAKY,SAAvB,EAAkC,OAAOZ,cAAP,CAJkD,CAKpF,iEADA;YAEA,MAAM,IAAIa,KAAJ,CAAW,CAAA,EAAA,EAAIF,YAAa,CAAA,yBAAA,EAA2BZ,iBAAkB,CAAA,EAAA,CAAzE,CAAN,CAAA;SACD;QAEDG,QAAQ,CAACY,WAAT,GAAuBf,iBAAiB,GAAG,UAA3C,CAAAG;QACA,OAAO;YAACA,QAAD;YAAWQ,UAAX;SAAP,CAAA;KACD;IAED;;oGAEF,CAEE,MAAMY,WAAwB,GAAG,IAAM;QACrC,MAAMC,aAAa,GAAGN,eAAe,CAACO,GAAhB,CAAqBxB,CAAAA,cAAD,GAAoB;YAC5D,OAAA,aAAOF,CAAAA,oBAAA,CAAoBE,cAApB,CAAP,CAAA;SADoB,CAAtB,AAEC;QACD,OAAO,SAASyB,QAAT,CAAkBJ,KAAlB,EAAgC;YACrC,MAAMK,QAAQ,GAAG,AAAAL,CAAAA,KAAK,KAAA,IAAL,IAAAA,KAAK,KAAA,KAAA,CAAL,GAAA,KAAA,CAAA,GAAAA,KAAK,CAAGN,SAAH,CAAL,CAAA,IAAsBQ,aAAvC,AAAA;YACA,OAAOzB,cAAA,CACL,IAAO,CAAA;oBAAE,CAAE,CAAA,OAAA,EAASiB,SAAU,CAAA,CAArB,CAAA,EAAyB;wBAAE,GAAGM,KAAL;wBAAY,CAACN,SAAD,CAAA,EAAaW,QAAb;qBAAZ;iBAAlC,CAAA;YADK,EAEL;gBAACL,KAAD;gBAAQK,QAAR;aAFK,CAAP,CACS;SAHX,CAMC;KAVH,AAWC;IAEDJ,WAAW,CAACP,SAAZ,GAAwBA,SAAxB,CAAAO;IACA,OAAO;QAAC1B,yCAAD;QAAgB+B,0CAAoB,CAACL,WAAD,KAAiBN,sBAAjB,CAApC;KAAP,CAAA;CACD;AAED;;oGAEA,CAEA,SAASW,0CAAT,CAA8B,GAAGC,MAAjC,EAAwD;IACtD,MAAMC,SAAS,GAAGD,MAAM,CAAC,CAAD,CAAxB,AAAA;IACA,IAAIA,MAAM,CAACR,MAAP,KAAkB,CAAtB,EAAyB,OAAOS,SAAP,CAAzB;IAEA,MAAMP,YAAwB,GAAG,IAAM;QACrC,MAAMQ,UAAU,GAAGF,MAAM,CAACJ,GAAP,CAAYF,CAAAA,WAAD,GAAkB,CAAA;gBAC9CG,QAAQ,EAAEH,WAAW,EADyB;gBAE9CP,SAAS,EAAEO,WAAW,CAACP,SAAvBA;aAF4B,CAAA;QAAX,CAAnB,AAAgD;QAKhD,OAAO,SAASgB,iBAAT,CAA2BC,cAA3B,EAA2C;YAChD,MAAMC,WAAU,GAAGH,UAAU,CAACI,MAAX,CAAkB,CAACD,UAAD,EAAa,EAlHxD,UAkH0DR,QAAF,CAAA,EAlHxD,WAkHoEV,SAAAA,CAAAA,EAAzB,GAAyC;gBAC5E,0FAAA;gBACA,oFAAA;gBACA,sDAAA;gBACA,MAAMoB,UAAU,GAAGV,QAAQ,CAACO,cAAD,CAA3B,AAAA;gBACA,MAAMI,YAAY,GAAGD,UAAU,CAAE,CAAA,OAAA,EAASpB,SAAU,CAAA,CAArB,CAA/B,AAAA;gBACA,OAAO;oBAAE,GAAGkB,UAAL;oBAAiB,GAAGG,YAAH;iBAAxB,CAAO;aANU,EAOhB,EAPgB,CAAnB,AAOC;YAED,OAAOtC,cAAA,CAAc,IAAO,CAAA;oBAAE,CAAE,CAAA,OAAA,EAAS+B,SAAS,CAACd,SAAU,CAAA,CAA/B,CAAA,EAAmCkB,WAAnC;iBAAT,CAAA;YAAd,EAAyE;gBAACA,WAAD;aAAzE,CAAP,CAA4B;SAV9B,CAWC;KAjBH,AAkBC;IAEDX,YAAW,CAACP,SAAZ,GAAwBc,SAAS,CAACd,SAAlC,CAAAO;IACA,OAAOA,YAAP,CAAA;CACD;;ADjID", "sources": ["packages/react/context/src/index.ts", "packages/react/context/src/createContext.tsx"], "sourcesContent": ["export { createContext, createContextScope } from './createContext';\nexport type { CreateScope, Scope } from './createContext';\n", "import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  function Provider(props: ContextValueType & { children: React.ReactNode }) {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  }\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  Provider.displayName = rootComponentName + 'Provider';\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    function Provider(\n      props: ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    ) {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName][index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    }\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    Provider.displayName = rootComponentName + 'Provider';\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n"], "names": ["createContext", "createContextScope", "React", "rootComponentName", "defaultContext", "Context", "Provider", "props", "children", "context", "value", "useMemo", "Object", "values", "useContext", "consumerName", "undefined", "Error", "displayName", "scopeName", "createContextScopeDeps", "defaultContexts", "BaseContext", "index", "length", "scope", "createScope", "scopeContexts", "map", "useScope", "contexts", "composeContextScopes", "scopes", "baseScope", "scopeHooks", "useComposedScopes", "overrideScopes", "nextScopes", "reduce", "scopeProps", "currentScope"], "version": 3, "file": "index.module.js.map"}