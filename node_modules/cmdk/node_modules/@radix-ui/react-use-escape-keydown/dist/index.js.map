{"mappings": ";;;;;;;;ACAA;;AAGA;;GAEA,CACA,SAASA,yCAAT,CAA0BG,mBAA1B,EAAgF;IAC9E,MAAMC,eAAe,GAAGF,gDAAc,CAACC,mBAAD,CAAtC,AAAA;IAEAF,sBAAA,CAAgB,IAAM;QACpB,MAAMK,aAAa,GAAIC,CAAAA,KAAD,GAA0B;YAC9C,IAAIA,KAAK,CAACC,GAAN,KAAc,QAAlB,EACEJ,eAAe,CAACG,KAAD,CAAf,CAAAH;SAFJ,AAIC;QACDK,QAAQ,CAACC,gBAAT,CAA0B,SAA1B,EAAqCJ,aAArC,CAAAG,CAAAA;QACA,OAAO,IAAMA,QAAQ,CAACE,mBAAT,CAA6B,SAA7B,EAAwCL,aAAxC,CAAb;QAAA,CAAA;KAPF,EAQG;QAACF,eAAD;KARH,CAQC,CAAA;CACF;;ADlBD", "sources": ["packages/react/use-escape-keydown/src/index.ts", "packages/react/use-escape-keydown/src/useEscapeKeydown.tsx"], "sourcesContent": ["export { useEscapeKeydown } from './useEscapeKeydown';\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\n/**\n * Listens for when the escape key is down\n */\nfunction useEscapeKeydown(onEscapeKeyDownProp?: (event: KeyboardEvent) => void) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onEscapeKeyDown(event);\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [onEscapeKeyDown]);\n}\n\nexport { useEscapeKeydown };\n"], "names": ["useEscapeKeydown", "React", "useCallbackRef", "onEscapeKeyDownProp", "onEscapeKeyDown", "useEffect", "handleKeyDown", "event", "key", "document", "addEventListener", "removeEventListener"], "version": 3, "file": "index.js.map"}