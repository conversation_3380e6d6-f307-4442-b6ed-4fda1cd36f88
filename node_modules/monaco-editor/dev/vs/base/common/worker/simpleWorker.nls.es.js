/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/base/common/worker/simpleWorker.nls.es", {
	"vs/base/common/platform": [
		"_",
	],
	"vs/editor/common/languages": [
		"matriz",
		"booleano",
		"clase",
		"constante",
		"constructor",
		"enumeración",
		"miembro de la enumeración",
		"evento",
		"campo",
		"archivo",
		"función",
		"interfaz",
		"clave",
		"método",
		"módulo",
		"espacio de nombres",
		"NULL",
		"número",
		"objeto",
		"operador",
		"paquete",
		"propiedad",
		"cadena",
		"estructura",
		"parámetro de tipo",
		"variable",
		"{0} ({1})",
	]
});