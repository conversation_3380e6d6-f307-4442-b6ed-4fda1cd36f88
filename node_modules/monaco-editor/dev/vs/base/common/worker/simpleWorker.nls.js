/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

/*---------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/
define("vs/base/common/worker/simpleWorker.nls", {
	"vs/base/common/platform": [
		"_"
	],
	"vs/editor/common/languages": [
		"array",
		"boolean",
		"class",
		"constant",
		"constructor",
		"enumeration",
		"enumeration member",
		"event",
		"field",
		"file",
		"function",
		"interface",
		"key",
		"method",
		"module",
		"namespace",
		"null",
		"number",
		"object",
		"operator",
		"package",
		"property",
		"string",
		"struct",
		"type parameter",
		"variable",
		"{0} ({1})"
	]
});