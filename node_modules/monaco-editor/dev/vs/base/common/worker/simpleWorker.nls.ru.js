/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/base/common/worker/simpleWorker.nls.ru", {
	"vs/base/common/platform": [
		"_",
	],
	"vs/editor/common/languages": [
		"массив",
		"логическое значение",
		"класс",
		"константа",
		"конструктор",
		"перечисление",
		"элемент перечисления",
		"событие",
		"поле",
		"файл",
		"функция",
		"интерфейс",
		"ключ",
		"метод",
		"модуль",
		"пространство имен",
		"NULL",
		"число",
		"объект",
		"оператор",
		"пакет",
		"свойство",
		"строка",
		"структура",
		"параметр типа",
		"Переменная",
		"{0} ({1})",
	]
});