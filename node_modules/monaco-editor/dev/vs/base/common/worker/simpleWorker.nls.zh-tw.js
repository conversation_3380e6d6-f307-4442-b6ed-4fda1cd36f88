/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/base/common/worker/simpleWorker.nls.zh-tw", {
	"vs/base/common/platform": [
		"_",
	],
	"vs/editor/common/languages": [
		"陣列",
		"布林值",
		"類別",
		"常數",
		"建構函式",
		"列舉",
		"列舉成員",
		"事件",
		"欄位",
		"檔案",
		"函式",
		"介面",
		"索引鍵",
		"方法",
		"模組",
		"命名空間",
		"null",
		"數字",
		"物件",
		"運算子",
		"套件",
		"屬性",
		"字串",
		"結構",
		"型別參數",
		"變數",
		"{0} ({1})",
	]
});