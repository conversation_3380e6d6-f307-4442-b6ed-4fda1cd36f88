/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/editor/editor.main.nls.de", {
	"vs/base/browser/ui/actionbar/actionViewItems": [
		"{0} ({1})",
	],
	"vs/base/browser/ui/findinput/findInput": [
		"Eingabe",
	],
	"vs/base/browser/ui/findinput/findInputToggles": [
		"Groß-/Kleinschreibung beachten",
		"Nur ganzes Wort suchen",
		"Regulären Ausdruck verwenden",
	],
	"vs/base/browser/ui/findinput/replaceInput": [
		"Eingabe",
		"Groß-/Kleinschreibung beibehalten",
	],
	"vs/base/browser/ui/hover/hoverWidget": [
		"Überprüfen Sie dies in der barrierefreien Ansicht mit \"{0}\".",
		"Überprüfen Sie dies in der barrierefreien Ansicht über den Befehl \"Barrierefreie Ansicht öffnen\", der zurzeit nicht über eine Tastenzuordnung ausgelöst werden kann.",
	],
	"vs/base/browser/ui/iconLabel/iconLabelHover": [
		"Wird geladen...",
	],
	"vs/base/browser/ui/inputbox/inputBox": [
		"Fehler: {0}",
		"Warnung: {0}",
		"Info: {0}",
		" oder {0} für Verlauf",
		" ({0} für Verlauf)",
		"Gelöschte Eingabe",
	],
	"vs/base/browser/ui/keybindingLabel/keybindingLabel": [
		"Ungebunden",
	],
	"vs/base/browser/ui/selectBox/selectBoxCustom": [
		"Auswahlfeld",
	],
	"vs/base/browser/ui/toolbar/toolbar": [
		"Weitere Aktionen...",
	],
	"vs/base/browser/ui/tree/abstractTree": [
		"Filter",
		"Fuzzyübereinstimmung",
		"Zum Filtern Text eingeben",
		"Zum Suchen eingeben",
		"Zum Suchen eingeben",
		"Schließen",
		"Kein Element gefunden.",
	],
	"vs/base/common/actions": [
		"(leer)",
	],
	"vs/base/common/errorMessage": [
		"{0}: {1}",
		"Ein Systemfehler ist aufgetreten ({0}).",
		"Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.",
		"Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.",
		"{0} ({1} Fehler gesamt)",
		"Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.",
	],
	"vs/base/common/keybindingLabels": [
		"STRG",
		"UMSCHALTTASTE",
		"ALT",
		"Windows",
		"STRG",
		"UMSCHALTTASTE",
		"ALT",
		"Super",
		"Steuern",
		"UMSCHALTTASTE",
		"Option",
		"Befehl",
		"Steuern",
		"UMSCHALTTASTE",
		"ALT",
		"Windows",
		"Steuern",
		"UMSCHALTTASTE",
		"ALT",
		"Super",
	],
	"vs/base/common/platform": [
		"_",
	],
	"vs/editor/browser/controller/textAreaHandler": [
		"Editor",
		"Auf den Editor kann zurzeit nicht zugegriffen werden.",
		"{0} Um den für die Sprachausgabe optimierten Modus zu aktivieren, verwenden Sie {1}",
		"{0} Um den für die Sprachausgabe optimierten Modus zu aktivieren, öffnen Sie die Schnellauswahl mit {1}, und führen Sie den Befehl \"Barrierefreiheitsmodus der Bildschirmsprachausgabe umschalten\" aus, der derzeit nicht über die Tastatur ausgelöst werden kann.",
		"{0} Weisen Sie eine Tastenzuordnung für den Befehl \"Barrierefreiheitsmodus der Sprachausgabe umschalten\" zu, indem Sie mit auf den Editor für Tastenzuordnungen zugreifen {1} und ihn ausführen.",
	],
	"vs/editor/browser/coreCommands": [
		"Auch bei längeren Zeilen am Ende bleiben",
		"Auch bei längeren Zeilen am Ende bleiben",
		"Sekundäre Cursor entfernt",
	],
	"vs/editor/browser/editorExtensions": [
		"&&Rückgängig",
		"Rückgängig",
		"&&Wiederholen",
		"Wiederholen",
		"&&Alles auswählen",
		"Alle auswählen",
	],
	"vs/editor/browser/widget/codeEditorWidget": [
		"Die Anzahl der Cursor wurde auf {0} beschränkt. Erwägen Sie die Verwendung von [Suchen und Ersetzen](https://code.visualstudio.com/docs/editor/codebasics#_find-und-ersetzen) für größere Änderungen, oder erhöhen Sie die Multicursorbegrenzungseinstellung des Editors.",
		"Erhöhen des Grenzwerts für mehrere Cursor",
	],
	"vs/editor/browser/widget/diffEditor/accessibleDiffViewer": [
		"Symbol für \"Einfügen\" im barrierefreien Diff-Viewer.",
		"Symbol für \"Entfernen\" im barrierefreien Diff-Viewer.",
		"Symbol für \"Schließen\" im barrierefreien Diff-Viewer.",
		"Schließen",
		"Barrierefreier Diff-Viewer. Verwenden Sie den Pfeil nach oben und unten, um zu navigieren.",
		"keine geänderten Zeilen",
		"1 Zeile geändert",
		"{0} Zeilen geändert",
		"Unterschied {0} von {1}: ursprüngliche Zeile {2}, {3}, geänderte Zeile {4}, {5}",
		"leer",
		"{0}: unveränderte Zeile {1}",
		"{0} ursprüngliche Zeile {1} geänderte Zeile {2}",
		"+ {0} geänderte Zeile(n) {1}",
		"– {0} Originalzeile {1}",
	],
	"vs/editor/browser/widget/diffEditor/colors": [
		"Die Rahmenfarbe für Text, der im Diff-Editor verschoben wurde.",
		"Die aktive Rahmenfarbe für Text, der im Diff-Editor verschoben wurde.",
		"Die Farbe des Schattens um unveränderte Regionswidgets.",
	],
	"vs/editor/browser/widget/diffEditor/decorations": [
		"Zeilenformatierung für Einfügungen im Diff-Editor",
		"Zeilenformatierung für Entfernungen im Diff-Editor",
	],
	"vs/editor/browser/widget/diffEditor/diffEditor.contribution": [
		"\"Unveränderte Bereiche reduzieren\" umschalten",
		"\"Verschobene Codeblöcke anzeigen\" umschalten",
		"\"Bei eingeschränktem Speicherplatz Inlineansicht verwenden\" umschalten",
		"Bei eingeschränktem Speicherplatz Inlineansicht verwenden",
		"Verschobene Codeblöcke anzeigen",
		"Diff-Editor",
		"Seite wechseln",
		"Vergleichsmodus beenden",
		"Alle unveränderten Regionen reduzieren",
		"Alle unveränderten Regionen anzeigen",
		"Barrierefreier Diff-Viewer",
		"Zum nächsten Unterschied wechseln",
		"Barrierefreien Diff-Viewer öffnen",
		"Zum vorherigen Unterschied wechseln",
	],
	"vs/editor/browser/widget/diffEditor/diffEditorDecorations": [
		"Ausgewählte Änderungen zurücksetzen",
		"Änderung zurücksetzen",
	],
	"vs/editor/browser/widget/diffEditor/diffEditorEditors": [
		" verwenden Sie {0}, um die Hilfe zur Barrierefreiheit zu öffnen.",
	],
	"vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature": [
		"Unveränderten Bereich falten",
		"Klicken oder ziehen Sie, um oben mehr anzuzeigen.",
		"Unveränderte Regionen anzeigen",
		"Klicken oder ziehen Sie, um unten mehr anzuzeigen.",
		"{0} ausgeblendete Linien",
		"Zum Auffalten doppelklicken",
	],
	"vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin": [
		"Gelöschte Zeilen kopieren",
		"Gelöschte Zeile kopieren",
		"Geänderte Zeilen kopieren",
		"Geänderte Zeile kopieren",
		"Gelöschte Zeile kopieren ({0})",
		"Geänderte Zeile ({0}) kopieren",
		"Diese Änderung rückgängig machen",
	],
	"vs/editor/browser/widget/diffEditor/movedBlocksLines": [
		"Code mit Änderungen in Zeile {0}-{1} verschoben",
		"Code mit Änderungen aus Zeile {0}-{1} verschoben",
		"Code in Zeile {0}-{1} verschoben",
		"Code aus Zeile {0}-{1} verschoben",
	],
	"vs/editor/browser/widget/multiDiffEditorWidget/colors": [
		"Die Hintergrundfarbe des Diff-Editor-Headers",
	],
	"vs/editor/common/config/editorConfigurationSchema": [
		"Editor",
		"Die Anzahl der Leerzeichen, denen ein Tabstopp entspricht. Diese Einstellung wird basierend auf dem Inhalt der Datei überschrieben, wenn {0} aktiviert ist.",
		"Die Anzahl von Leerzeichen, die für den Einzug oder „tabSize“ verwendet werden, um den Wert aus „#editor.tabSize#“ zu verwenden. Diese Einstellung wird basierend auf dem Dateiinhalt überschrieben, wenn „#editor.detectIndentation#“ aktiviert ist.",
		"Fügt beim Drücken der TAB-Taste Leerzeichen ein. Diese Einstellung wird basierend auf dem Inhalt der Datei überschrieben, wenn {0} aktiviert ist.",
		"Steuert, ob {0} und {1} automatisch erkannt werden, wenn eine Datei basierend auf dem Dateiinhalt geöffnet wird.",
		"Nachfolgende automatisch eingefügte Leerzeichen entfernen",
		"Spezielle Behandlung für große Dateien zum Deaktivieren bestimmter speicherintensiver Funktionen.",
		"Deaktivieren Sie Word-basierte Vorschläge.",
		"Nur Wörter aus dem aktiven Dokument vorschlagen",
		"Wörter aus allen geöffneten Dokumenten derselben Sprache vorschlagen",
		"Wörter aus allen geöffneten Dokumenten vorschlagen",
		"Steuert, ob Vervollständigungen auf Grundlage der Wörter im Dokument berechnet werden sollen, und aus welchen Dokumenten sie berechnet werden sollen.",
		"Die semantische Hervorhebung ist für alle Farbdesigns aktiviert.",
		"Die semantische Hervorhebung ist für alle Farbdesigns deaktiviert.",
		"Die semantische Hervorhebung wird durch die Einstellung \"semanticHighlighting\" des aktuellen Farbdesigns konfiguriert.",
		"Steuert, ob die semantische Hervorhebung für die Sprachen angezeigt wird, die sie unterstützen.",
		"Lassen Sie Peek-Editoren geöffnet, auch wenn Sie auf ihren Inhalt doppelklicken oder auf die ESCAPETASTE klicken.",
		"Zeilen, die diese Länge überschreiten, werden aus Leistungsgründen nicht tokenisiert",
		"Steuert, ob die Tokenisierung asynchron auf einem Webworker erfolgen soll.",
		"Steuert, ob die asynchrone Tokenisierung protokolliert werden soll. Nur zum Debuggen.",
		"Steuert, ob die asynchrone Tokenisierung anhand der Legacy-Hintergrundtokenisierung überprüft werden soll. Die Tokenisierung kann verlangsamt werden. Nur zum Debuggen.",
		"Definiert die Klammersymbole, die den Einzug vergrößern oder verkleinern.",
		"Das öffnende Klammerzeichen oder die Zeichenfolgensequenz.",
		"Das schließende Klammerzeichen oder die Zeichenfolgensequenz.",
		"Definiert die Klammerpaare, die durch ihre Schachtelungsebene farbig formatiert werden, wenn die Farbgebung für das Klammerpaar aktiviert ist.",
		"Das öffnende Klammerzeichen oder die Zeichenfolgensequenz.",
		"Das schließende Klammerzeichen oder die Zeichenfolgensequenz.",
		"Timeout in Millisekunden, nach dem die Diff-Berechnung abgebrochen wird. Bei 0 wird kein Timeout verwendet.",
		"Maximale Dateigröße in MB, für die Diffs berechnet werden sollen. Verwenden Sie 0, um keinen Grenzwert zu setzen.",
		"Steuert, ob der Diff-Editor die Unterschiede nebeneinander oder im Text anzeigt.",
		"Wenn die Breite des Diff-Editors unter diesem Wert liegt, wird die Inlineansicht verwendet.",
		"Wenn diese Option aktiviert ist und die Breite des Editors nicht ausreicht, wird die Inlineansicht verwendet.",
		"Wenn diese Option aktiviert ist, zeigt der Diff-Editor Pfeile in seinem Glyphenrand an, um Änderungen rückgängig zu machen.",
		"Wenn aktiviert, ignoriert der Diff-Editor Änderungen an voran- oder nachgestellten Leerzeichen.",
		"Steuert, ob der Diff-Editor die Indikatoren \"+\" und \"-\" für hinzugefügte/entfernte Änderungen anzeigt.",
		"Steuert, ob der Editor CodeLens anzeigt.",
		"Zeilenumbrüche erfolgen nie.",
		"Der Zeilenumbruch erfolgt an der Breite des Anzeigebereichs.",
		"Zeilen werden gemäß der Einstellung „{0}“ umbrochen.",
		"Verwendet den Legacyvergleichsalgorithmus.",
		"Verwendet den erweiterten Vergleichsalgorithmus.",
		"Steuert, ob der Diff-Editor unveränderte Regionen anzeigt.",
		"Steuert, wie viele Zeilen für unveränderte Regionen verwendet werden.",
		"Steuert, wie viele Zeilen als Mindestwert für unveränderte Regionen verwendet werden.",
		"Steuert, wie viele Zeilen beim Vergleich unveränderter Regionen als Kontext verwendet werden.",
		"Steuert, ob der Diff-Editor erkannte Codeverschiebevorgänge anzeigen soll.",
		"Steuert, ob der diff-Editor leere Dekorationen anzeigt, um anzuzeigen, wo Zeichen eingefügt oder gelöscht wurden.",
	],
	"vs/editor/common/config/editorOptions": [
		"Verwenden Sie Plattform-APIs, um zu erkennen, wenn eine Sprachausgabe angefügt ist.",
		"Optimieren Sie diese Option für die Verwendung mit einer Sprachausgabe.",
		"Hiermit wird angenommen, dass keine Sprachausgabe angefügt ist.",
		"Steuert, ob die Benutzeroberfläche in einem Modus ausgeführt werden soll, in dem sie für Sprachausgaben optimiert ist.",
		"Steuert, ob beim Kommentieren ein Leerzeichen eingefügt wird.",
		"Steuert, ob leere Zeilen bei Umschalt-, Hinzufügungs- oder Entfernungsaktionen für Zeilenkommentare ignoriert werden sollen.",
		"Steuert, ob ein Kopiervorgang ohne Auswahl die aktuelle Zeile kopiert.",
		"Steuert, ob der Cursor bei der Suche nach Übereinstimmungen während der Eingabe springt.",
		"Suchzeichenfolge niemals aus der Editorauswahl seeden.",
		"Suchzeichenfolge immer aus der Editorauswahl seeden, einschließlich Wort an Cursorposition.",
		"Suchzeichenfolge nur aus der Editorauswahl seeden.",
		"Steuert, ob für die Suchzeichenfolge im Widget \"Suche\" ein Seeding aus der Auswahl des Editors ausgeführt wird.",
		"\"In Auswahl suchen\" niemals automatisch aktivieren (Standard).",
		"\"In Auswahl suchen\" immer automatisch aktivieren.",
		"\"In Auswahl suchen\" automatisch aktivieren, wenn mehrere Inhaltszeilen ausgewählt sind.",
		"Steuert die Bedingung zum automatischen Aktivieren von \"In Auswahl suchen\".",
		"Steuert, ob das Widget \"Suche\" die freigegebene Suchzwischenablage unter macOS lesen oder bearbeiten soll.",
		"Steuert, ob das Suchwidget zusätzliche Zeilen im oberen Bereich des Editors hinzufügen soll. Wenn die Option auf \"true\" festgelegt ist, können Sie über die erste Zeile hinaus scrollen, wenn das Suchwidget angezeigt wird.",
		"Steuert, ob die Suche automatisch am Anfang (oder am Ende) neu gestartet wird, wenn keine weiteren Übereinstimmungen gefunden werden.",
		"Hiermit werden Schriftligaturen (Schriftartfeatures \"calt\" und \"liga\") aktiviert/deaktiviert. Ändern Sie diesen Wert in eine Zeichenfolge, um die CSS-Eigenschaft \"font-feature-settings\" detailliert zu steuern.",
		"Explizite CSS-Eigenschaft \"font-feature-settings\". Stattdessen kann ein boolescher Wert übergeben werden, wenn nur Ligaturen aktiviert/deaktiviert werden müssen.",
		"Hiermit werden Schriftligaturen oder Schriftartfeatures konfiguriert. Hierbei kann es sich entweder um einen booleschen Wert zum Aktivieren oder Deaktivieren von Ligaturen oder um eine Zeichenfolge für den Wert der CSS-Eigenschaft \"font-feature-settings\" handeln.",
		"Aktiviert/deaktiviert die Übersetzung von „font-weight“ in „font-variation-settings“. Ändern Sie dies in eine Zeichenfolge für eine differenzierte Steuerung der CSS-Eigenschaft „font-variation-settings“.",
		"Explizite CSS-Eigenschaft „font-variation-settings“. Stattdessen kann ein boolescher Wert eingeben werden, wenn nur „font-weight“ in „font-variation-settings“ übersetzt werden muss.",
		"Konfiguriert Variationen der Schriftart. Kann entweder ein boolescher Wert zum Aktivieren/Deaktivieren der Übersetzung von „font-weight“ in „font-variation-settings“ oder eine Zeichenfolge für den Wert der CSS-Eigenschaft „font-variation-settings“ sein.",
		"Legt die Schriftgröße in Pixeln fest.",
		"Es sind nur die Schlüsselwörter \"normal\" und \"bold\" sowie Zahlen zwischen 1 und 1000 zulässig.",
		"Steuert die Schriftbreite. Akzeptiert die Schlüsselwörter \"normal\" und \"bold\" sowie Zahlen zwischen 1 und 1000.",
		"Vorschauansicht der Ergebnisse anzeigen (Standardeinstellung)",
		"Zum Hauptergebnis gehen und Vorschauansicht anzeigen",
		"Wechseln Sie zum primären Ergebnis, und aktivieren Sie die Navigation ohne Vorschau zu anderen Ergebnissen.",
		"Diese Einstellung ist veraltet. Verwenden Sie stattdessen separate Einstellungen wie \"editor.editor.gotoLocation.multipleDefinitions\" oder \"editor.editor.gotoLocation.multipleImplementations\".",
		"Legt das Verhalten des Befehls \"Gehe zu Definition\" fest, wenn mehrere Zielpositionen vorhanden sind",
		"Legt das Verhalten des Befehls \"Gehe zur Typdefinition\" fest, wenn mehrere Zielpositionen vorhanden sind.",
		"Legt das Verhalten des Befehls \"Gehe zu Deklaration\" fest, wenn mehrere Zielpositionen vorhanden sind.",
		"Legt das Verhalten des Befehls \"Gehe zu Implementierungen\", wenn mehrere Zielspeicherorte vorhanden sind",
		"Legt das Verhalten des Befehls \"Gehe zu Verweisen\" fest, wenn mehrere Zielpositionen vorhanden sind",
		"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \"Gehe zu Definition\" die aktuelle Position ist.",
		"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \"Gehe zu Typdefinition\" die aktuelle Position ist.",
		"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \"Gehe zu Deklaration\" der aktuelle Speicherort ist.",
		"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \"Gehe zu Implementatierung\" der aktuelle Speicherort ist.",
		"Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von \"Gehe zu Verweis\" die aktuelle Position ist.",
		"Steuert, ob die Hovermarkierung angezeigt wird.",
		"Steuert die Verzögerung in Millisekunden, nach der die Hovermarkierung angezeigt wird.",
		"Steuert, ob die Hovermarkierung sichtbar bleiben soll, wenn der Mauszeiger darüber bewegt wird.",
		"Steuert die Verzögerung in Millisekunden, nach der die Hovermarkierung ausgeblendet wird. Erfordert die Aktivierung von \"editor.hover.sticky\".",
		"Zeigen Sie den Mauszeiger lieber über der Linie an, wenn Platz vorhanden ist.",
		"Es wird angenommen, dass alle Zeichen gleich breit sind. Dies ist ein schneller Algorithmus, der für Festbreitenschriftarten und bestimmte Alphabete (wie dem lateinischen), bei denen die Glyphen gleich breit sind, korrekt funktioniert.",
		"Delegiert die Berechnung von Umbruchpunkten an den Browser. Dies ist ein langsamer Algorithmus, der bei großen Dateien Code Freezes verursachen kann, aber in allen Fällen korrekt funktioniert.",
		"Steuert den Algorithmus, der Umbruchpunkte berechnet. Beachten Sie, dass \"advanced\" im Barrierefreiheitsmodus für eine optimale Benutzererfahrung verwendet wird.",
		"Aktiviert das Glühlampensymbol für Codeaktionen im Editor.",
		"Das KI-Symbol nicht anzeigen.",
		"Zeigen Sie ein KI-Symbol an, wenn das Codeaktionsmenü eine KI-Aktion ausschließlich im Code enthält.",
		"Zeigen Sie ein KI-Symbol an, wenn das Codeaktionsmenü eine KI-Aktion in Code und leeren Zeilen enthält.",
		"Ein KI-Symbol zusammen mit der Glühbirne anzeigen, wenn das Codeaktionsmenü eine KI-Aktion enthält.",
		"Zeigt die geschachtelten aktuellen Bereiche während des Bildlaufs am oberen Rand des Editors an.",
		"Definiert die maximale Anzahl fixierter Zeilen, die angezeigt werden sollen.",
		"Legt das Modell fest, das zur Bestimmung der zu fixierenden Zeilen verwendet wird. Existiert das Gliederungsmodell nicht, wird auf das Modell des Folding Providers zurückgegriffen, der wiederum auf das Einrückungsmodell zurückgreift. Diese Reihenfolge wird in allen drei Fällen beachtet.",
		"Hiermit aktivieren Sie das Scrollen mit fixiertem Bildlauf mit der horizontalen Scrollleiste des Editors.",
		"Aktiviert die Inlay-Hinweise im Editor.",
		"Inlay-Hinweise sind aktiviert",
		"Inlay-Hinweise werden standardmäßig angezeigt und ausgeblendet, wenn Sie {0} gedrückt halten",
		"Inlayhinweise sind standardmäßig ausgeblendet. Sie werden angezeigt, wenn {0} gedrückt gehalten wird.",
		"Inlay-Hinweise sind deaktiviert",
		"Steuert den Schriftgrad von Einlapphinweisen im Editor. Standardmäßig wird die {0} verwendet, wenn der konfigurierte Wert kleiner als {1} oder größer als der Schriftgrad des Editors ist.",
		"Steuert die Schriftartfamilie von Einlapphinweisen im Editor. Bei Festlegung auf \"leer\" wird die {0} verwendet.",
		"Aktiviert den Abstand um die Inlay-Hinweise im Editor.",
		"Steuert die Zeilenhöhe. \r\n – Verwenden Sie 0, um die Zeilenhöhe automatisch anhand des Schriftgrads zu berechnen.\r\n – Werte zwischen 0 und 8 werden als Multiplikator mit dem Schriftgrad verwendet.\r\n – Werte größer oder gleich 8 werden als effektive Werte verwendet.",
		"Steuert, ob die Minimap angezeigt wird.",
		"Steuert, ob die Minimap automatisch ausgeblendet wird.",
		"Die Minimap hat die gleiche Größe wie der Editor-Inhalt (und kann scrollen).",
		"Die Minimap wird bei Bedarf vergrößert oder verkleinert, um die Höhe des Editors zu füllen (kein Scrollen).",
		"Die Minimap wird bei Bedarf verkleinert, damit sie nicht größer als der Editor ist (kein Scrollen).",
		"Legt die Größe der Minimap fest.",
		"Steuert die Seite, wo die Minimap gerendert wird.",
		"Steuert, wann der Schieberegler für die Minimap angezeigt wird.",
		"Maßstab des in der Minimap gezeichneten Inhalts: 1, 2 oder 3.",
		"Die tatsächlichen Zeichen in einer Zeile rendern im Gegensatz zu Farbblöcken.",
		"Begrenzen Sie die Breite der Minimap, um nur eine bestimmte Anzahl von Spalten zu rendern.",
		"Steuert den Abstand zwischen dem oberen Rand des Editors und der ersten Zeile.",
		"Steuert den Abstand zwischen dem unteren Rand des Editors und der letzten Zeile.",
		"Aktiviert ein Pop-up, das Dokumentation und Typ eines Parameters anzeigt während Sie tippen.",
		"Steuert, ob das Menü mit Parameterhinweisen zyklisch ist oder sich am Ende der Liste schließt.",
		"Schnelle Vorschläge werden im Vorschlagswidget angezeigt",
		"Schnelle Vorschläge werden als inaktiver Text angezeigt",
		"Schnelle Vorschläge sind deaktiviert",
		"Schnellvorschläge innerhalb von Zeichenfolgen aktivieren.",
		"Schnellvorschläge innerhalb von Kommentaren aktivieren.",
		"Schnellvorschläge außerhalb von Zeichenfolgen und Kommentaren aktivieren.",
		"Steuert, ob Vorschläge während des Tippens automatisch angezeigt werden sollen. Dies kann bei der Eingabe von Kommentaren, Zeichenketten und anderem Code kontrolliert werden. Schnellvorschläge können so konfiguriert werden, dass sie als Geistertext oder mit dem Vorschlags-Widget angezeigt werden. Beachten Sie auch die \'{0}\'-Einstellung, die steuert, ob Vorschläge durch Sonderzeichen ausgelöst werden.",
		"Zeilennummern werden nicht dargestellt.",
		"Zeilennummern werden als absolute Zahl dargestellt.",
		"Zeilennummern werden als Abstand in Zeilen an Cursorposition dargestellt.",
		"Zeilennummern werden alle 10 Zeilen dargestellt.",
		"Steuert die Anzeige von Zeilennummern.",
		"Anzahl der Zeichen aus Festbreitenschriftarten, ab der dieses Editor-Lineal gerendert wird.",
		"Farbe dieses Editor-Lineals.",
		"Vertikale Linien nach einer bestimmten Anzahl von Monospacezeichen rendern. Verwenden Sie mehrere Werte für mehrere Linien. Wenn das Array leer ist, werden keine Linien gerendert.",
		"Die vertikale Bildlaufleiste wird nur bei Bedarf angezeigt.",
		"Die vertikale Bildlaufleiste ist immer sichtbar.",
		"Die vertikale Bildlaufleiste wird immer ausgeblendet.",
		"Steuert die Sichtbarkeit der vertikalen Bildlaufleiste.",
		"Die horizontale Bildlaufleiste wird nur bei Bedarf angezeigt.",
		"Die horizontale Bildlaufleiste ist immer sichtbar.",
		"Die horizontale Bildlaufleiste wird immer ausgeblendet.",
		"Steuert die Sichtbarkeit der horizontalen Bildlaufleiste.",
		"Die Breite der vertikalen Bildlaufleiste.",
		"Die Höhe der horizontalen Bildlaufleiste.",
		"Steuert, ob Klicks nach Seite scrollen oder zur Klickposition springen.",
		"Wenn diese Option festgelegt ist, wird die Größe des Editorinhalts nicht durch die horizontale Scrollleiste vergrößert.",
		"Legt fest, ob alle nicht einfachen ASCII-Zeichen hervorgehoben werden. Nur Zeichen zwischen U+0020 und U+007E, Tabulator, Zeilenvorschub und Wagenrücklauf gelten als einfache ASCII-Zeichen.",
		"Legt fest, ob Zeichen, die nur als Platzhalter dienen oder überhaupt keine Breite haben, hervorgehoben werden.",
		"Legt fest, ob Zeichen hervorgehoben werden, die mit einfachen ASCII-Zeichen verwechselt werden können, mit Ausnahme derjenigen, die im aktuellen Gebietsschema des Benutzers üblich sind.",
		"Steuert, ob Zeichen in Kommentaren auch mit Unicode-Hervorhebung versehen werden sollen.",
		"Steuert, ob Zeichen in Zeichenfolgen auch mit Unicode-Hervorhebung versehen werden sollen.",
		"Definiert zulässige Zeichen, die nicht hervorgehoben werden.",
		"Unicodezeichen, die in zulässigen Gebietsschemas üblich sind, werden nicht hervorgehoben.",
		"Steuert, ob Inline-Vorschläge automatisch im Editor angezeigt werden.",
		"Die Symbolleiste „Inline-Vorschlag“ anzeigen, wenn ein Inline-Vorschlag angezeigt wird.",
		"Die Symbolleiste „Inline-Vorschlag“ anzeigen, wenn Sie mit dem Mauszeiger auf einen Inline-Vorschlag zeigen.",
		"Die Inlinevorschlagssymbolleiste nie anzeigen.",
		"Steuert, wann die Inlinevorschlagssymbolleiste angezeigt werden soll.",
		"Steuert, wie Inlinevorschläge mit dem Vorschlagswidget interagieren. Wenn diese Option aktiviert ist, wird das Vorschlagswidget nicht automatisch angezeigt, wenn Inlinevorschläge verfügbar sind.",
		"Steuert, ob die Klammerpaar-Farbgebung aktiviert ist oder nicht. Verwenden Sie {0}, um die Hervorhebungsfarben der Klammer zu überschreiben.",
		"Steuert, ob jeder Klammertyp über einen eigenen unabhängigen Farbpool verfügt.",
		"Aktiviert Klammernpaarführungslinien.",
		"Aktiviert Klammernpaarführungslinien nur für das aktive Klammerpaar.",
		"Deaktiviert Klammernpaarführungslinien.",
		"Steuert, ob Führungslinien für Klammerpaare aktiviert sind oder nicht.",
		"Aktiviert horizontale Führungslinien als Ergänzung zu vertikalen Klammernpaarführungslinien.",
		"Aktiviert horizontale Führungslinien nur für das aktive Klammerpaar.",
		"Deaktiviert horizontale Führungslinien für Klammernpaare.",
		"Steuert, ob horizontale Führungslinien für Klammernpaare aktiviert sind oder nicht.",
		"Steuert, ob der Editor das aktive Klammerpaar hervorheben soll.",
		"Steuert, ob der Editor Einzugsführungslinien rendern soll.",
		"Hebt die aktive Einzugsführung hervor.",
		"Hebt die aktive Einzugshilfslinie hervor, selbst wenn Klammerhilfslinien hervorgehoben sind.",
		"Heben Sie die aktive Einzugshilfslinie nicht hervor.",
		"Steuert, ob der Editor die aktive Einzugsführungslinie hevorheben soll.",
		"Vorschlag einfügen, ohne den Text auf der rechten Seite des Cursors zu überschreiben",
		"Vorschlag einfügen und Text auf der rechten Seite des Cursors überschreiben",
		"Legt fest, ob Wörter beim Akzeptieren von Vervollständigungen überschrieben werden. Beachten Sie, dass dies von Erweiterungen abhängt, die für dieses Features aktiviert sind.",
		"Steuert, ob Filter- und Suchvorschläge geringfügige Tippfehler berücksichtigen.",
		"Steuert, ob bei der Sortierung Wörter priorisiert werden, die in der Nähe des Cursors stehen.",
		"Steuert, ob gespeicherte Vorschlagauswahlen in verschiedenen Arbeitsbereichen und Fenstern gemeinsam verwendet werden (dafür ist \"#editor.suggestSelection#\" erforderlich).",
		"Wählen Sie immer einen Vorschlag aus, wenn IntelliSense automatisch ausgelöst wird.",
		"Wählen Sie niemals einen Vorschlag aus, wenn IntelliSense automatisch ausgelöst wird.",
		"Wählen Sie einen Vorschlag nur aus, wenn IntelliSense aus einem Triggerzeichen ausgelöst wird.",
		"Wählen Sie einen Vorschlag nur aus, wenn Sie IntelliSense während der Eingabe auslösen.",
		"Steuert, ob ein Vorschlag ausgewählt wird, wenn das Widget angezeigt wird. Beachten Sie, dass dies nur für automatisch ausgelöste Vorschläge gilt (\"#editor.quickSuggestions#\" und \"#editor.suggestOnTriggerCharacters#\"), und dass ein Vorschlag immer ausgewählt wird, wenn er explizit aufgerufen wird, z. B. über STRG+LEERTASTE.",
		"Steuert, ob ein aktiver Schnipsel verhindert, dass der Bereich \"Schnelle Vorschläge\" angezeigt wird.",
		"Steuert, ob Symbole in Vorschlägen ein- oder ausgeblendet werden.",
		"Steuert die Sichtbarkeit der Statusleiste unten im Vorschlagswidget.",
		"Steuert, ob das Ergebnis des Vorschlags im Editor in der Vorschau angezeigt werden soll.",
		"Steuert, ob Vorschlagsdetails inline mit der Bezeichnung oder nur im Detailwidget angezeigt werden.",
		"Diese Einstellung ist veraltet. Die Größe des Vorschlagswidgets kann jetzt geändert werden.",
		"Diese Einstellung ist veraltet. Verwenden Sie stattdessen separate Einstellungen wie \"editor.suggest.showKeywords\" oder \"editor.suggest.showSnippets\".",
		"Wenn aktiviert, zeigt IntelliSense \"method\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"funktions\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"constructor\"-Vorschläge an.",
		"Wenn IntelliSense aktiviert ist, werden „veraltete“ Vorschläge angezeigt.",
		"Wenn dies aktiviert ist, erfordert die IntelliSense-Filterung, dass das erste Zeichen mit einem Wortanfang übereinstimmt, z. B. „c“ in „Console“ oder „WebContext“, aber _nicht_ bei „description“. Wenn diese Option deaktiviert ist, zeigt IntelliSense mehr Ergebnisse an, sortiert sie aber weiterhin nach der Übereinstimmungsqualität.",
		"Wenn aktiviert, zeigt IntelliSense \"field\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"variable\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"class\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"struct\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"interface\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"module\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"property\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"event\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"operator\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"unit\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"value\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"constant\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"enum\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"enumMember\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"keyword\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"text\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"color\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"file\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"reference\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"customcolor\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"folder\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"typeParameter\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense \"snippet\"-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense user-Vorschläge an.",
		"Wenn aktiviert, zeigt IntelliSense issues-Vorschläge an.",
		"Gibt an, ob führende und nachstehende Leerzeichen immer ausgewählt werden sollen.",
		"Gibt an, ob Unterwörter (z. B. \"foo\" in \"fooBar\" oder \"foo_bar\") ausgewählt werden sollen.",
		"Kein Einzug. Umbrochene Zeilen beginnen bei Spalte 1.",
		"Umbrochene Zeilen erhalten den gleichen Einzug wie das übergeordnete Element.",
		"Umbrochene Zeilen erhalten + 1 Einzug auf das übergeordnete Element.",
		"Umgebrochene Zeilen werden im Vergleich zum übergeordneten Element +2 eingerückt.",
		"Steuert die Einrückung der umbrochenen Zeilen.",
		"Steuert, ob Sie eine Datei in einen Text-Editor ziehen und ablegen können, indem Sie die UMSCHALTTASTE gedrückt halten (anstatt die Datei in einem Editor zu öffnen).",
		"Steuert, ob beim Ablegen von Dateien im Editor ein Widget angezeigt wird. Mit diesem Widget können Sie steuern, wie die Datei ablegt wird.",
		"Zeigt das Widget für die Dropdownauswahl an, nachdem eine Datei im Editor abgelegt wurde.",
		"Das Widget für die Ablageauswahl wird nie angezeigt. Stattdessen wird immer der Standardablageanbieter verwendet.",
		"Steuert, ob Sie Inhalte auf unterschiedliche Weise einfügen können.",
		"Steuert, ob beim Einfügen von Inhalt im Editor ein Widget angezeigt wird. Mit diesem Widget können Sie steuern, wie die Datei eingefügt wird.",
		"Das Widget für die Einfügeauswahl anzeigen, nachdem der Inhalt in den Editor eingefügt wurde.",
		"Das Widget für die Einfügeauswahl wird nie angezeigt. Stattdessen wird immer das Standardeinfügeverhalten verwendet.",
		"Steuert, ob Vorschläge über Commitzeichen angenommen werden sollen. In JavaScript kann ein Semikolon (\";\") beispielsweise ein Commitzeichen sein, das einen Vorschlag annimmt und dieses Zeichen eingibt.",
		"Einen Vorschlag nur mit der EINGABETASTE akzeptieren, wenn dieser eine Änderung am Text vornimmt.",
		"Steuert, ob Vorschläge mit der EINGABETASTE (zusätzlich zur TAB-Taste) akzeptiert werden sollen. Vermeidet Mehrdeutigkeit zwischen dem Einfügen neuer Zeilen oder dem Annehmen von Vorschlägen.",
		"Steuert die Anzahl von Zeilen im Editor, die von einer Sprachausgabe in einem Arbeitsschritt gelesen werden können. Wenn eine Sprachausgabe erkannt wird, wird der Standardwert automatisch auf 500 festgelegt. Warnung: Ein Wert höher als der Standardwert, kann sich auf die Leistung auswirken.",
		"Editor-Inhalt",
		"Steuern Sie, ob Inlinevorschläge von einer Sprachausgabe angekündigt werden.",
		"Verwenden Sie Sprachkonfigurationen, um zu bestimmen, wann Klammern automatisch geschlossen werden sollen.",
		"Schließe Klammern nur automatisch, wenn der Cursor sich links von einem Leerzeichen befindet.",
		"Steuert, ob der Editor automatisch Klammern schließen soll, nachdem der Benutzer eine öffnende Klammer hinzugefügt hat.",
		"Verwenden Sie Sprachkonfigurationen, um festzulegen, wann Kommentare automatisch geschlossen werden sollen.",
		"Kommentare werden nur dann automatisch geschlossen, wenn sich der Cursor links von einem Leerraum befindet.",
		"Steuert, ob der Editor Kommentare automatisch schließen soll, nachdem die Benutzer*innen einen ersten Kommentar hinzugefügt haben.",
		"Angrenzende schließende Anführungszeichen oder Klammern werden nur überschrieben, wenn sie automatisch eingefügt wurden.",
		"Steuert, ob der Editor angrenzende schließende Anführungszeichen oder Klammern beim Löschen entfernen soll.",
		"Schließende Anführungszeichen oder Klammern werden nur überschrieben, wenn sie automatisch eingefügt wurden.",
		"Steuert, ob der Editor schließende Anführungszeichen oder Klammern überschreiben soll.",
		"Verwende die Sprachkonfiguration, um zu ermitteln, wann Anführungsstriche automatisch geschlossen werden.",
		"Schließende Anführungszeichen nur dann automatisch ergänzen, wenn der Cursor sich links von einem Leerzeichen befindet.",
		"Steuert, ob der Editor Anführungszeichen automatisch schließen soll, nachdem der Benutzer ein öffnendes Anführungszeichen hinzugefügt hat.",
		"Der Editor fügt den Einzug nicht automatisch ein.",
		"Der Editor behält den Einzug der aktuellen Zeile bei.",
		"Der Editor behält den in der aktuellen Zeile definierten Einzug bei und beachtet für Sprachen definierte Klammern.",
		"Der Editor behält den Einzug der aktuellen Zeile bei, beachtet von Sprachen definierte Klammern und ruft spezielle onEnterRules-Regeln auf, die von Sprachen definiert wurden.",
		"Der Editor behält den Einzug der aktuellen Zeile bei, beachtet die von Sprachen definierten Klammern, ruft von Sprachen definierte spezielle onEnterRules-Regeln auf und beachtet von Sprachen definierte indentationRules-Regeln.",
		"Legt fest, ob der Editor den Einzug automatisch anpassen soll, wenn Benutzer Zeilen eingeben, einfügen, verschieben oder einrücken",
		"Sprachkonfigurationen verwenden, um zu bestimmen, wann eine Auswahl automatisch umschlossen werden soll.",
		"Mit Anführungszeichen, nicht mit Klammern umschließen.",
		"Mit Klammern, nicht mit Anführungszeichen umschließen.",
		"Steuert, ob der Editor die Auswahl beim Eingeben von Anführungszeichen oder Klammern automatisch umschließt.",
		"Emuliert das Auswahlverhalten von Tabstoppzeichen, wenn Leerzeichen für den Einzug verwendet werden. Die Auswahl wird an Tabstopps ausgerichtet.",
		"Steuert, ob der Editor CodeLens anzeigt.",
		"Steuert die Schriftfamilie für CodeLens.",
		"Steuert den Schriftgrad in Pixeln für CodeLens. Bei Festlegung auf „0, 90 % von „#editor.fontSize#“ verwendet.",
		"Steuert, ob der Editor die Inline-Farbdecorators und die Farbauswahl rendern soll.",
		"Farbauswahl sowohl beim Klicken als auch beim Daraufzeigen des Farbdekorators anzeigen",
		"Farbauswahl beim Draufzeigen auf den Farbdekorator anzeigen",
		"Farbauswahl beim Klicken auf den Farbdekorator anzeigen",
		"Steuert die Bedingung, damit eine Farbauswahl aus einem Farbdekorator angezeigt wird.",
		"Steuert die maximale Anzahl von Farb-Decorators, die in einem Editor gleichzeitig gerendert werden können.",
		"Zulassen, dass die Auswahl per Maus und Tasten die Spaltenauswahl durchführt.",
		"Steuert, ob Syntax-Highlighting in die Zwischenablage kopiert wird.",
		"Steuert den Cursoranimationsstil.",
		"Die Smooth Caret-Animation ist deaktiviert.",
		"Die Smooth Caret-Animation ist nur aktiviert, wenn der Benutzer den Cursor mit einer expliziten Geste bewegt.",
		"Die Smooth Caret-Animation ist immer aktiviert.",
		"Steuert, ob die weiche Cursoranimation aktiviert werden soll.",
		"Steuert den Cursor-Stil.",
		"Steuert die Mindestanzahl sichtbarer führender Zeilen (mindestens 0) und nachfolgender Zeilen (mindestens 1) um den Cursor. Dies wird in einigen anderen Editoren als „scrollOff“ oder „scrollOffset“ bezeichnet.",
		"\"cursorSurroundingLines\" wird nur erzwungen, wenn die Auslösung über die Tastatur oder API erfolgt.",
		"\"cursorSurroundingLines\" wird immer erzwungen.",
		"Steuert, wann \"#cursorSurroundingLines#\" erzwungen werden soll.",
		"Steuert die Breite des Cursors, wenn `#editor.cursorStyle#` auf `line` festgelegt ist.",
		"Steuert, ob der Editor das Verschieben einer Auswahl per Drag and Drop zulässt.",
		"Verwenden Sie eine neue Rendering-Methode mit SVGs.",
		"Verwenden Sie eine neue Rendering-Methode mit Schriftartzeichen.",
		"Verwenden Sie die stabile Rendering-Methode.",
		"Steuert, ob Leerzeichen mit einer neuen experimentellen Methode gerendert werden.",
		"Multiplikator für Scrollgeschwindigkeit bei Drücken von ALT.",
		"Steuert, ob Codefaltung im Editor aktiviert ist.",
		"Verwenden Sie eine sprachspezifische Faltstrategie, falls verfügbar. Andernfalls wird eine einzugsbasierte verwendet.",
		"Einzugsbasierte Faltstrategie verwenden.",
		"Steuert die Strategie für die Berechnung von Faltbereichen.",
		"Steuert, ob der Editor eingefaltete Bereiche hervorheben soll.",
		"Steuert, ob der Editor Importbereiche automatisch reduziert.",
		"Die maximale Anzahl von faltbaren Regionen. Eine Erhöhung dieses Werts kann dazu führen, dass der Editor weniger reaktionsfähig wird, wenn die aktuelle Quelle eine große Anzahl von faltbaren Regionen aufweist.",
		"Steuert, ob eine Zeile aufgefaltet wird, wenn nach einer gefalteten Zeile auf den leeren Inhalt geklickt wird.",
		"Steuert die Schriftfamilie.",
		"Steuert, ob der Editor den eingefügten Inhalt automatisch formatieren soll. Es muss ein Formatierer vorhanden sein, der in der Lage ist, auch Dokumentbereiche zu formatieren.",
		"Steuert, ob der Editor die Zeile nach der Eingabe automatisch formatieren soll.",
		"Steuert, ob der Editor den vertikalen Glyphenrand rendert. Der Glyphenrand wird hauptsächlich zum Debuggen verwendet.",
		"Steuert, ob der Cursor im Übersichtslineal ausgeblendet werden soll.",
		"Legt den Abstand der Buchstaben in Pixeln fest.",
		"Steuert, ob die verknüpfte Bearbeitung im Editor aktiviert ist. Abhängig von der Sprache werden zugehörige Symbole, z. B. HTML-Tags, während der Bearbeitung aktualisiert.",
		"Steuert, ob der Editor Links erkennen und anklickbar machen soll.",
		"Passende Klammern hervorheben",
		"Ein Multiplikator, der für die Mausrad-Bildlaufereignisse \"deltaX\" und \"deltaY\" verwendet werden soll.",
		"Schriftart des Editors vergrößern, wenn das Mausrad verwendet und die STRG-TASTE gedrückt wird.",
		"Mehrere Cursor zusammenführen, wenn sie sich überlappen.",
		"Ist unter Windows und Linux der STRG-Taste und unter macOS der Befehlstaste zugeordnet.",
		"Ist unter Windows und Linux der ALT-Taste und unter macOS der Wahltaste zugeordnet.",
		"Der Modifizierer, der zum Hinzufügen mehrerer Cursor mit der Maus verwendet werden soll. Die Mausgesten \"Gehe zu Definition\" und \"Link öffnen\" werden so angepasst, dass sie nicht mit dem [Multicursormodifizierer](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-Modifizierer) in Konflikt stehen.",
		"Jeder Cursor fügt eine Textzeile ein.",
		"Jeder Cursor fügt den vollständigen Text ein.",
		"Steuert das Einfügen, wenn die Zeilenanzahl des Einfügetexts der Cursor-Anzahl entspricht.",
		"Steuert die maximale Anzahl von Cursorn, die sich gleichzeitig in einem aktiven Editor befindet.",
		"Hebt keine Vorkommen hervor.",
		"Hebt Vorkommen nur in der aktuellen Datei hervor.",
		"Experimentell: Hebt Vorkommen in allen gültigen geöffneten Dateien hervor.",
		"Steuert, ob Vorkommen in geöffneten Dateien hervorgehoben werden sollen.",
		"Steuert, ob um das Übersichtslineal ein Rahmen gezeichnet werden soll.",
		"Struktur beim Öffnen des Peek-Editors fokussieren",
		"Editor fokussieren, wenn Sie den Peek-Editor öffnen",
		"Steuert, ob der Inline-Editor oder die Struktur im Peek-Widget fokussiert werden soll.",
		"Steuert, ob die Mausgeste \"Gehe zu Definition\" immer das Vorschauwidget öffnet.",
		"Steuert die Verzögerung in Millisekunden nach der Schnellvorschläge angezeigt werden.",
		"Steuert, ob der Editor bei Eingabe automatisch eine Umbenennung vornimmt.",
		"Veraltet. Verwenden Sie stattdessen \"editor.linkedEditing\".",
		"Steuert, ob der Editor Steuerzeichen rendern soll.",
		"Letzte Zeilennummer rendern, wenn die Datei mit einem Zeilenumbruch endet.",
		"Hebt den Bundsteg und die aktuelle Zeile hervor.",
		"Steuert, wie der Editor die aktuelle Zeilenhervorhebung rendern soll.",
		"Steuert, ob der Editor die aktuelle Zeilenhervorhebung nur dann rendern soll, wenn der Fokus auf dem Editor liegt.",
		"Leerraumzeichen werden gerendert mit Ausnahme der einzelnen Leerzeichen zwischen Wörtern.",
		"Hiermit werden Leerraumzeichen nur für ausgewählten Text gerendert.",
		"Nur nachstehende Leerzeichen rendern",
		"Steuert, wie der Editor Leerzeichen rendern soll.",
		"Steuert, ob eine Auswahl abgerundete Ecken aufweisen soll.",
		"Steuert die Anzahl der zusätzlichen Zeichen, nach denen der Editor horizontal scrollt.",
		"Steuert, ob der Editor jenseits der letzten Zeile scrollen wird.",
		"Nur entlang der vorherrschenden Achse scrollen, wenn gleichzeitig vertikal und horizontal gescrollt wird. Dadurch wird ein horizontaler Versatz beim vertikalen Scrollen auf einem Trackpad verhindert.",
		"Steuert, ob die primäre Linux-Zwischenablage unterstützt werden soll.",
		"Steuert, ob der Editor Übereinstimmungen hervorheben soll, die der Auswahl ähneln.",
		"Steuerelemente für die Codefaltung immer anzeigen.",
		"Zeigen Sie niemals die Faltungssteuerelemente an, und verringern Sie die Größe des Bundstegs.",
		"Steuerelemente für die Codefaltung nur anzeigen, wenn sich die Maus über dem Bundsteg befindet.",
		"Steuert, wann die Steuerungselemente für die Codefaltung am Bundsteg angezeigt werden.",
		"Steuert das Ausblenden von nicht verwendetem Code.",
		"Steuert durchgestrichene veraltete Variablen.",
		"Zeige Schnipselvorschläge über den anderen Vorschlägen.",
		"Schnipselvorschläge unter anderen Vorschlägen anzeigen.",
		"Zeige Schnipselvorschläge mit anderen Vorschlägen.",
		"Keine Schnipselvorschläge anzeigen.",
		"Steuert, ob Codeschnipsel mit anderen Vorschlägen angezeigt und wie diese sortiert werden.",
		"Legt fest, ob der Editor Bildläufe animiert ausführt.",
		"Steuert, ob für Benutzer*innen, die eine Sprachausgabe nutzen, bei Anzeige einer Inlinevervollständigung ein Hinweis zur Barrierefreiheit angezeigt werden soll.",
		"Schriftgrad für das Vorschlagswidget. Bei Festlegung auf {0} wird der Wert von {1} verwendet.",
		"Zeilenhöhe für das Vorschlagswidget. Bei Festlegung auf {0} wird der Wert von {1} verwendet. Der Mindestwert ist 8.",
		"Steuert, ob Vorschläge automatisch angezeigt werden sollen, wenn Triggerzeichen eingegeben werden.",
		"Immer den ersten Vorschlag auswählen.",
		"Wählen Sie die aktuellsten Vorschläge aus, es sei denn, es wird ein Vorschlag durch eine weitere Eingabe ausgewählt, z.B. \"console.| -> console.log\", weil \"log\" vor Kurzem abgeschlossen wurde.",
		"Wählen Sie Vorschläge basierend auf früheren Präfixen aus, die diese Vorschläge abgeschlossen haben, z.B. \"co -> console\" und \"con ->\" const\".",
		"Steuert, wie Vorschläge bei Anzeige der Vorschlagsliste vorab ausgewählt werden.",
		"Die Tab-Vervollständigung fügt den passendsten Vorschlag ein, wenn auf Tab gedrückt wird.",
		"Tab-Vervollständigungen deaktivieren.",
		"Codeschnipsel per Tab vervollständigen, wenn die Präfixe übereinstimmen. Funktioniert am besten, wenn \"quickSuggestions\" deaktiviert sind.",
		"Tab-Vervollständigungen aktivieren.",
		"Ungewöhnliche Zeilenabschlusszeichen werden automatisch entfernt.",
		"Ungewöhnliche Zeilenabschlusszeichen werden ignoriert.",
		"Zum Entfernen ungewöhnlicher Zeilenabschlusszeichen wird eine Eingabeaufforderung angezeigt.",
		"Entfernen Sie unübliche Zeilenabschlusszeichen, die Probleme verursachen können.",
		"Das Einfügen und Löschen von Leerzeichen erfolgt nach Tabstopps.",
		"Verwenden Sie die Standardregel für Zeilenumbrüche.",
		"Trennstellen dürfen nicht für Texte in Chinesisch/Japanisch/Koreanisch (CJK) verwendet werden. Das Verhalten von Nicht-CJK-Texten ist mit dem für normales Verhalten identisch.",
		"Steuert die Regeln für Trennstellen, die für Texte in Chinesisch/Japanisch/Koreanisch (CJK) verwendet werden.",
		"Zeichen, die als Worttrennzeichen verwendet werden, wenn wortbezogene Navigationen oder Vorgänge ausgeführt werden.",
		"Zeilenumbrüche erfolgen nie.",
		"Der Zeilenumbruch erfolgt an der Breite des Anzeigebereichs.",
		"Der Zeilenumbruch erfolgt bei \"#editor.wordWrapColumn#\".",
		"Der Zeilenumbruch erfolgt beim Mindestanzeigebereich und \"#editor.wordWrapColumn\".",
		"Steuert, wie der Zeilenumbruch durchgeführt werden soll.",
		"Steuert die umschließende Spalte des Editors, wenn \"#editor.wordWrap#\" den Wert \"wordWrapColumn\" oder \"bounded\" aufweist.",
		"Steuert, ob Inlinefarbdekorationen mithilfe des Standard-Dokumentfarbanbieters angezeigt werden sollen.",
		"Steuert, ob der Editor Registerkarten empfängt oder zur Navigation zur Workbench zurückgibt.",
	],
	"vs/editor/common/core/editorColorRegistry": [
		"Hintergrundfarbe zur Hervorhebung der Zeile an der Cursorposition.",
		"Hintergrundfarbe für den Rahmen um die Zeile an der Cursorposition.",
		"Hintergrundfarbe der markierten Bereiche, wie z.B. Quick Open oder die Suche. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Hintergrundfarbe für den Rahmen um hervorgehobene Bereiche.",
		"Hintergrundfarbe des hervorgehobenen Symbols, z. B. \"Gehe zu Definition\" oder \"Gehe zu nächster/vorheriger\". Die Farbe darf nicht undurchsichtig sein, um zugrunde liegende Dekorationen nicht zu verbergen.",
		"Hintergrundfarbe des Rahmens um hervorgehobene Symbole",
		"Farbe des Cursors im Editor.",
		"Hintergrundfarbe vom Editor-Cursor. Erlaubt die Anpassung der Farbe von einem Zeichen, welches von einem Block-Cursor überdeckt wird.",
		"Farbe der Leerzeichen im Editor.",
		"Zeilennummernfarbe im Editor.",
		"Farbe der Führungslinien für Einzüge im Editor.",
		"\"editorIndentGuide.background\" ist veraltet. Verwenden Sie stattdessen \"editorIndentGuide.background1\".",
		"Farbe der Führungslinien für Einzüge im aktiven Editor.",
		"\"editorIndentGuide.activeBackground\" ist veraltet. Verwenden Sie stattdessen \"editorIndentGuide.activeBackground1\".",
		"Farbe der Führungslinien für Einzüge im Editor (1).",
		"Farbe der Führungslinien für Einzüge im Editor (2).",
		"Farbe der Führungslinien für Einzüge im Editor (3).",
		"Farbe der Führungslinien für Einzüge im Editor (4).",
		"Farbe der Führungslinien für Einzüge im Editor (5).",
		"Farbe der Führungslinien für Einzüge im Editor (6).",
		"Farbe der Führungslinien für Einzüge im aktiven Editor (1).",
		"Farbe der Führungslinien für Einzüge im aktiven Editor (2).",
		"Farbe der Führungslinien für Einzüge im aktiven Editor (3).",
		"Farbe der Führungslinien für Einzüge im aktiven Editor (4).",
		"Farbe der Führungslinien für Einzüge im aktiven Editor (5).",
		"Farbe der Führungslinien für Einzüge im aktiven Editor (6).",
		"Zeilennummernfarbe der aktiven Editorzeile.",
		"Die ID ist veraltet. Verwenden Sie stattdessen \"editorLineNumber.activeForeground\".",
		"Zeilennummernfarbe der aktiven Editorzeile.",
		"Die Farbe der letzten Editor-Zeile, wenn „editor.renderFinalNewline“ auf „abgeblendet“ festgelegt ist.",
		"Farbe des Editor-Lineals.",
		"Vordergrundfarbe der CodeLens-Links im Editor",
		"Hintergrundfarbe für zusammengehörige Klammern",
		"Farbe für zusammengehörige Klammern",
		"Farbe des Rahmens für das Übersicht-Lineal.",
		"Hintergrundfarbe des Editor-Übersichtslineals.",
		"Hintergrundfarbe der Editorleiste. Die Leiste enthält die Glyphenränder und die Zeilennummern.",
		"Rahmenfarbe unnötigen (nicht genutzten) Quellcodes im Editor.",
		"Deckkraft des unnötigen (nicht genutzten) Quellcodes im Editor. \"#000000c0\" rendert z.B. den Code mit einer Deckkraft von 75%. Verwenden Sie für Designs mit hohem Kontrast das Farbdesign \"editorUnnecessaryCode.border\", um unnötigen Code zu unterstreichen statt ihn abzublenden.",
		"Rahmenfarbe des Ghost-Texts im Editor.",
		"Vordergrundfarbe des Ghost-Texts im Editor.",
		"Hintergrundfarbe des Ghost-Texts im Editor.",
		"Übersichtslinealmarkerfarbe für das Hervorheben von Bereichen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Übersichtslineal-Markierungsfarbe für Fehler.",
		"Übersichtslineal-Markierungsfarbe für Warnungen.",
		"Übersichtslineal-Markierungsfarbe für Informationen.",
		"Vordergrundfarbe der Klammern (1). Erfordert die Aktivierung der Farbgebung des Klammerpaars.",
		"Vordergrundfarbe der Klammern (2). Erfordert die Aktivierung der Farbgebung des Klammerpaars.",
		"Vordergrundfarbe der Klammern (3). Erfordert die Aktivierung der Farbgebung des Klammerpaars.",
		"Vordergrundfarbe der Klammern (4). Erfordert die Aktivierung der Farbgebung des Klammerpaars.",
		"Vordergrundfarbe der Klammern (5). Erfordert die Aktivierung der Farbgebung des Klammerpaars.",
		"Vordergrundfarbe der Klammern (6). Erfordert die Aktivierung der Farbgebung des Klammerpaars.",
		"Vordergrundfarbe der unerwarteten Klammern.",
		"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (1). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.",
		"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (2). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.",
		"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (3). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.",
		"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (4). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.",
		"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (5). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.",
		"Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (6). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.",
		"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (1). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.",
		"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (2). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.",
		"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (3). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.",
		"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (4). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.",
		"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (5). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.",
		"Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (6). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.",
		"Rahmenfarbe, die zum Hervorheben von Unicode-Zeichen verwendet wird.",
		"Hintergrundfarbe, die zum Hervorheben von Unicode-Zeichen verwendet wird.",
	],
	"vs/editor/common/editorContextKeys": [
		"Gibt an, ob der Editor-Text den Fokus besitzt (Cursor blinkt).",
		"Gibt an, ob der Editor oder ein Editor-Widget den Fokus besitzt (z. B. ob der Fokus sich im Suchwidget befindet).",
		"Gibt an, ob ein Editor oder eine Rich-Text-Eingabe den Fokus besitzt (Cursor blinkt).",
		"Gibt an, ob der Editor schreibgeschützt ist",
		"Gibt an, ob der Kontext ein Diff-Editor ist.",
		"Gibt an, ob der Kontext ein eingebetteter Diff-Editor ist.",
		"Gibt an, ob der Kontext ein Multi-Diff-Editor ist.",
		"Gibt an, ob alle Dateien im Multi-Diff-Editor reduziert sind",
		"Gibt an, ob der Diff-Editor Änderungen aufweist.",
		"Gibt an, ob ein verschobener Codeblock für den Vergleich ausgewählt wird.",
		"Gibt an, ob der barrierefreie Diff-Viewer sichtbar ist.",
		"Gibt an, ob für den Diff-Editor der Breakpoint für das Rendern im Modus \"Parallel\" oder \"Inline\" erreicht wurde.",
		"Gibt an, ob \"editor.columnSelection\" aktiviert ist.",
		"Gibt an, ob im Editor Text ausgewählt ist.",
		"Gibt an, ob der Editor über Mehrfachauswahl verfügt.",
		"Gibt an, ob die TAB-TASTE den Fokus aus dem Editor verschiebt.",
		"Gibt an, ob Hover im Editor sichtbar ist.",
		"Gibt an, ob Daraufzeigen im Editor fokussiert ist.",
		"Gibt an, ob der Fokus auf dem Fixierten Bildlauf liegt.",
		"Gibt an, ob der Fixierte Bildlauf sichtbar ist.",
		"Gibt an, ob der eigenständige Farbwähler sichtbar ist.",
		"Gibt an, ob der eigenständige Farbwähler fokussiert ist.",
		"Gibt an, ob der Editor Bestandteil eines größeren Editors ist (z. B. Notebooks).",
		"Der Sprachbezeichner des Editors.",
		"Gibt an, ob der Editor über einen Vervollständigungselementanbieter verfügt.",
		"Gibt an, ob der Editor über einen Codeaktionsanbieter verfügt.",
		"Gibt an, ob der Editor über einen CodeLens-Anbieter verfügt.",
		"Gibt an, ob der Editor über einen Definitionsanbieter verfügt.",
		"Gibt an, ob der Editor über einen Deklarationsanbieter verfügt.",
		"Gibt an, ob der Editor über einen Implementierungsanbieter verfügt.",
		"Gibt an, ob der Editor über einen Typdefinitionsanbieter verfügt.",
		"Gibt an, ob der Editor über einen Hoveranbieter verfügt.",
		"Gibt an, ob der Editor über einen Dokumenthervorhebungsanbieter verfügt.",
		"Gibt an, ob der Editor über einen Dokumentsymbolanbieter verfügt.",
		"Gibt an, ob der Editor über einen Verweisanbieter verfügt.",
		"Gibt an, ob der Editor über einen Umbenennungsanbieter verfügt.",
		"Gibt an, ob der Editor über einen Signaturhilfeanbieter verfügt.",
		"Gibt an, ob der Editor über einen Inlinehinweisanbieter verfügt.",
		"Gibt an, ob der Editor über einen Dokumentformatierungsanbieter verfügt.",
		"Gibt an, ob der Editor über einen Anbieter für Dokumentauswahlformatierung verfügt.",
		"Gibt an, ob der Editor über mehrere Dokumentformatierungsanbieter verfügt.",
		"Gibt an, ob der Editor über mehrere Anbieter für Dokumentauswahlformatierung verfügt.",
	],
	"vs/editor/common/languages": [
		"Array",
		"Boolescher Wert",
		"Klasse",
		"Konstante",
		"Konstruktor",
		"Enumeration",
		"Enumerationsmember",
		"Ereignis",
		"Feld",
		"Datei",
		"Funktion",
		"Schnittstelle",
		"Schlüssel",
		"Methode",
		"Modul",
		"Namespace",
		"NULL",
		"Zahl",
		"Objekt",
		"Operator",
		"Paket",
		"Eigenschaft",
		"Zeichenfolge",
		"Struktur",
		"Typparameter",
		"Variable",
		"{0} ({1})",
	],
	"vs/editor/common/languages/modesRegistry": [
		"Nur-Text",
	],
	"vs/editor/common/model/editStack": [
		"Eingabe",
	],
	"vs/editor/common/standaloneStrings": [
		"Entwickler: Token überprüfen",
		"Gehe zu Zeile/Spalte...",
		"Alle Anbieter für den Schnellzugriff anzeigen",
		"Befehlspalette",
		"Befehle anzeigen und ausführen",
		"Gehe zu Symbol...",
		"Gehe zu Symbol nach Kategorie...",
		"Editor-Inhalt",
		"Drücken Sie ALT + F1, um die Barrierefreiheitsoptionen aufzurufen.",
		"Zu Design mit hohem Kontrast umschalten",
		"{0} Bearbeitungen in {1} Dateien durchgeführt",
	],
	"vs/editor/common/viewLayout/viewLineRenderer": [
		"Mehr anzeigen ({0})",
		"{0} Zeichen",
	],
	"vs/editor/contrib/anchorSelect/browser/anchorSelect": [
		"Auswahlanker",
		"Anker festgelegt bei \"{0}:{1}\"",
		"Auswahlanker festlegen",
		"Zu Auswahlanker wechseln",
		"Auswahl von Anker zu Cursor",
		"Auswahlanker abbrechen",
	],
	"vs/editor/contrib/bracketMatching/browser/bracketMatching": [
		"Übersichtslineal-Markierungsfarbe für zusammengehörige Klammern.",
		"Gehe zu Klammer",
		"Auswählen bis Klammer",
		"Klammern entfernen",
		"Gehe zu &&Klammer",
		"Text auswählen und Klammern oder geschweifte Klammern einschließen",
	],
	"vs/editor/contrib/caretOperations/browser/caretOperations": [
		"Ausgewählten Text nach links verschieben",
		"Ausgewählten Text nach rechts verschieben",
	],
	"vs/editor/contrib/caretOperations/browser/transpose": [
		"Buchstaben austauschen",
	],
	"vs/editor/contrib/clipboard/browser/clipboard": [
		"&&Ausschneiden",
		"Ausschneiden",
		"Ausschneiden",
		"Ausschneiden",
		"&&Kopieren",
		"Kopieren",
		"Kopieren",
		"Kopieren",
		"Kopieren als",
		"Kopieren als",
		"Freigeben",
		"Freigeben",
		"Freigeben",
		"&&Einfügen",
		"Einfügen",
		"Einfügen",
		"Einfügen",
		"Mit Syntaxhervorhebung kopieren",
	],
	"vs/editor/contrib/codeAction/browser/codeAction": [
		"Beim Anwenden der Code-Aktion ist ein unbekannter Fehler aufgetreten",
	],
	"vs/editor/contrib/codeAction/browser/codeActionCommands": [
		"Art der auszuführenden Codeaktion",
		"Legt fest, wann die zurückgegebenen Aktionen angewendet werden",
		"Die erste zurückgegebene Codeaktion immer anwenden",
		"Die erste zurückgegebene Codeaktion anwenden, wenn nur eine vorhanden ist",
		"Zurückgegebene Codeaktionen nicht anwenden",
		"Legt fest, ob nur bevorzugte Codeaktionen zurückgegeben werden sollen",
		"Schnelle Problembehebung ...",
		"Keine Codeaktionen verfügbar",
		"Keine bevorzugten Codeaktionen für \"{0}\" verfügbar",
		"Keine Codeaktionen für \"{0}\" verfügbar",
		"Keine bevorzugten Codeaktionen verfügbar",
		"Keine Codeaktionen verfügbar",
		"Refactoring durchführen...",
		"Keine bevorzugten Refactorings für \"{0}\" verfügbar",
		"Keine Refactorings für \"{0}\" verfügbar",
		"Keine bevorzugten Refactorings verfügbar",
		"Keine Refactorings verfügbar",
		"Quellaktion...",
		"Keine bevorzugten Quellaktionen für \"{0}\" verfügbar",
		"Keine Quellaktionen für \"{0}\" verfügbar",
		"Keine bevorzugten Quellaktionen verfügbar",
		"Keine Quellaktionen verfügbar",
		"Importe organisieren",
		"Keine Aktion zum Organisieren von Importen verfügbar",
		"Alle korrigieren",
		"Aktion \"Alle korrigieren\" nicht verfügbar",
		"Automatisch korrigieren...",
		"Keine automatischen Korrekturen verfügbar",
	],
	"vs/editor/contrib/codeAction/browser/codeActionContributions": [
		"Aktivieren/Deaktivieren Sie die Anzeige von Gruppenheadern im Codeaktionsmenü.",
		"Hiermit aktivieren/deaktivieren Sie die Anzeige der nächstgelegenen schnellen Problembehebung innerhalb einer Zeile, wenn derzeit keine Diagnose durchgeführt wird.",
	],
	"vs/editor/contrib/codeAction/browser/codeActionController": [
		"Kontext: {0} in Zeile {1} und Spalte {2}.",
		"Deaktivierte Elemente ausblenden",
		"Deaktivierte Elemente anzeigen",
	],
	"vs/editor/contrib/codeAction/browser/codeActionMenu": [
		"Weitere Aktionen...",
		"Schnelle Problembehebung",
		"Extrahieren",
		"Inline",
		"Erneut generieren",
		"Verschieben",
		"Umgeben mit",
		"Quellaktion",
	],
	"vs/editor/contrib/codeAction/browser/lightBulbWidget": [
		"Zeigt Codeaktionen an. Bevorzugte Schnellkorrektur verfügbar ({0})",
		"Codeaktionen anzeigen ({0})",
		"Codeaktionen anzeigen",
		"Inlinechat starten ({0})",
		"Inlinechat starten",
		"KI-Aktion auslösen",
	],
	"vs/editor/contrib/codelens/browser/codelensController": [
		"CodeLens-Befehle für aktuelle Zeile anzeigen",
		"Befehl auswählen",
	],
	"vs/editor/contrib/colorPicker/browser/colorPickerWidget": [
		"Zum Umschalten zwischen Farboptionen (rgb/hsl/hex) klicken",
		"Symbol zum Schließen des Farbwählers",
	],
	"vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions": [
		"Eigenständige Farbwähler anzeigen oder konzentrieren",
		"&&Eigenständige Farbwähler anzeigen oder fokussieren",
		"Farbwähler ausblenden",
		"Farbe mit eigenständigem Farbwähler einfügen",
	],
	"vs/editor/contrib/comment/browser/comment": [
		"Zeilenkommentar umschalten",
		"Zeilenkommen&&tar umschalten",
		"Zeilenkommentar hinzufügen",
		"Zeilenkommentar entfernen",
		"Blockkommentar umschalten",
		"&&Blockkommentar umschalten",
	],
	"vs/editor/contrib/contextmenu/browser/contextmenu": [
		"Minimap",
		"Zeichen rendern",
		"Vertikale Größe",
		"Proportional",
		"Ausfüllen",
		"Anpassen",
		"Schieberegler",
		"Maus über",
		"Immer",
		"Editor-Kontextmenü anzeigen",
	],
	"vs/editor/contrib/cursorUndo/browser/cursorUndo": [
		"Mit Cursor rückgängig machen",
		"Wiederholen mit Cursor",
	],
	"vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution": [
		"Einfügen als...",
		"Die ID der Einfügebearbeitung, die angewendet werden soll. Wenn keine Angabe erfolgt, zeigt der Editor eine Auswahl an.",
	],
	"vs/editor/contrib/dropOrPasteInto/browser/copyPasteController": [
		"Gibt an, ob das Einfügewidget angezeigt wird.",
		"Einfügeoptionen anzeigen...",
		"Einfügehandler werden ausgeführt. Klicken Sie hier, um den Vorgang abzubrechen.",
		"Einfügeaktion auswählen",
		"Einfügehandler werden ausgeführt",
	],
	"vs/editor/contrib/dropOrPasteInto/browser/defaultProviders": [
		"Integriert",
		"Nur-Text einfügen",
		"URI einfügen",
		"URI einfügen",
		"Pfade einfügen",
		"Pfad einfügen",
		"Relative Pfade einfügen",
		"Relativen Pfad einfügen",
	],
	"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution": [
		"Konfiguriert den Standardablageanbieter für den Inhalt eines vorgegebenen MIME-Typs.",
	],
	"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController": [
		"Gibt an, ob das Ablagewidget angezeigt wird.",
		"Ablageoptionen anzeigen...",
		"Drophandler werden ausgeführt. Klicken Sie hier, um den Vorgang abzubrechen.",
	],
	"vs/editor/contrib/editorState/browser/keybindingCancellation": [
		"Gibt an, ob der Editor einen abbrechbaren Vorgang ausführt, z. B. \"Verweisvorschau\".",
	],
	"vs/editor/contrib/find/browser/findController": [
		"Die Datei ist zu groß, um einen Vorgang zum Ersetzen aller Elemente auszuführen.",
		"Suchen",
		"&&Suchen",
		"Überschreibt das Flag „Use Regular Expression“.\r\nDas Flag wird für die Zukunft nicht gespeichert.\r\n0: Nichts unternehmen\r\n1: TRUE\r\n2: FALSE",
		"Überschreibt das Flag „Match Whole Word“.\r\nDas Flag wird für die Zukunft nicht gespeichert.\r\n0: Nichts unternehmen\r\n1: TRUE\r\n2: FALSE",
		"Überschreibt das Flag „Math Case“.\r\nDas Flag wird für die Zukunft nicht gespeichert.\r\n0: Nichts unternehmen\r\n1: TRUE\r\n2: FALSE",
		"Überschreibt das Flag „Preserve Case“.\r\nDas Flag wird für die Zukunft nicht gespeichert.\r\n0: Nichts unternehmen\r\n1: TRUE\r\n2: FALSE",
		"Mit Argumenten suchen",
		"Mit Auswahl suchen",
		"Weitersuchen",
		"Vorheriges Element suchen",
		"Zu Übereinstimmung wechseln ...",
		"Keine Übereinstimmungen. Versuchen Sie, nach etwas anderem zu suchen.",
		"Geben Sie eine Zahl ein, um zu einer bestimmten Übereinstimmung zu wechseln (zwischen 1 und {0}).",
		"Zahl zwischen 1 und {0} eingeben",
		"Zahl zwischen 1 und {0} eingeben",
		"Nächste Auswahl suchen",
		"Vorherige Auswahl suchen",
		"Ersetzen",
		"&&Ersetzen",
	],
	"vs/editor/contrib/find/browser/findWidget": [
		"Symbol für \"In Auswahl suchen\" im Editor-Such-Widget.",
		"Symbol für die Anzeige, dass das Editor-Such-Widget zugeklappt wurde.",
		"Symbol für die Anzeige, dass das Editor-Such-Widget aufgeklappt wurde.",
		"Symbol für \"Ersetzen\" im Editor-Such-Widget.",
		"Symbol für \"Alle ersetzen\" im Editor-Such-Widget.",
		"Symbol für \"Vorheriges Element suchen\" im Editor-Such-Widget.",
		"Symbol für \"Nächstes Element suchen\" im Editor-Such-Widget.",
		"Suchen/Ersetzen",
		"Suchen",
		"Suchen",
		"Vorherige Übereinstimmung",
		"Nächste Übereinstimmung",
		"In Auswahl suchen",
		"Schließen",
		"Ersetzen",
		"Ersetzen",
		"Ersetzen",
		"Alle ersetzen",
		"Ersetzen umschalten",
		"Nur die ersten {0} Ergebnisse wurden hervorgehoben, aber alle Suchoperationen werden auf dem gesamten Text durchgeführt.",
		"{0} von {1}",
		"Keine Ergebnisse",
		"{0} gefunden",
		"{0} für \"{1}\" gefunden",
		"{0} für \"{1}\" gefunden, bei {2}",
		"{0} für \"{1}\" gefunden",
		"STRG+EINGABE fügt jetzt einen Zeilenumbruch ein, statt alles zu ersetzen. Sie können die Tastenzuordnung für \"editor.action.replaceAll\" ändern, um dieses Verhalten außer Kraft zu setzen.",
	],
	"vs/editor/contrib/folding/browser/folding": [
		"Auffalten",
		"Faltung rekursiv aufheben",
		"Falten",
		"Einklappung umschalten",
		"Rekursiv falten",
		"Alle Blockkommentare falten",
		"Alle Regionen falten",
		"Alle Regionen auffalten",
		"Alle bis auf ausgewählte falten",
		"Alle bis auf ausgewählte auffalten",
		"Alle falten",
		"Alle auffalten",
		"Zur übergeordneten Reduzierung wechseln",
		"Zum vorherigen Faltbereich wechseln",
		"Zum nächsten Faltbereich wechseln",
		"Faltungsbereich aus Auswahl erstellen",
		"Manuelle Faltbereiche entfernen",
		"Faltebene {0}",
	],
	"vs/editor/contrib/folding/browser/foldingDecorations": [
		"Hintergrundfarbe hinter gefalteten Bereichen. Die Farbe darf nicht deckend sein, sodass zugrunde liegende Dekorationen nicht ausgeblendet werden.",
		"Farbe des Faltsteuerelements im Editor-Bundsteg.",
		"Symbol für aufgeklappte Bereiche im Editor-Glyphenrand.",
		"Symbol für zugeklappte Bereiche im Editor-Glyphenrand.",
		"Symbol für manuell reduzierte Bereiche im Glyphenrand des Editors.",
		"Symbol für manuell erweiterte Bereiche im Glyphenrand des Editors.",
	],
	"vs/editor/contrib/fontZoom/browser/fontZoom": [
		"Editorschriftart vergrößern",
		"Editorschriftart verkleinern",
		"Editor Schriftart Vergrößerung zurücksetzen",
	],
	"vs/editor/contrib/format/browser/formatActions": [
		"Dokument formatieren",
		"Auswahl formatieren",
	],
	"vs/editor/contrib/gotoError/browser/gotoError": [
		"Gehe zu nächstem Problem (Fehler, Warnung, Information)",
		"Symbol für den Marker zum Wechseln zum nächsten Element.",
		"Gehe zu vorigem Problem (Fehler, Warnung, Information)",
		"Symbol für den Marker zum Wechseln zum vorherigen Element.",
		"Gehe zu dem nächsten Problem in den Dateien (Fehler, Warnung, Info)",
		"Nächstes &&Problem",
		"Gehe zu dem vorherigen Problem in den Dateien (Fehler, Warnung, Info)",
		"Vorheriges &&Problem",
	],
	"vs/editor/contrib/gotoError/browser/gotoErrorWidget": [
		"Fehler",
		"Warnung",
		"Info",
		"Hinweis",
		"{0} bei {1}. ",
		"{0} von {1} Problemen",
		"{0} von {1} Problemen",
		"Editormarkierung: Farbe bei Fehler des Navigationswidgets.",
		"Hintergrund der Fehlerüberschrift des Markernavigationswidgets im Editor.",
		"Editormarkierung: Farbe bei Warnung des Navigationswidgets.",
		"Hintergrund der Warnungsüberschrift des Markernavigationswidgets im Editor.",
		"Editormarkierung: Farbe bei Information des Navigationswidgets.",
		"Hintergrund der Informationsüberschrift des Markernavigationswidgets im Editor.",
		"Editormarkierung: Hintergrund des Navigationswidgets.",
	],
	"vs/editor/contrib/gotoSymbol/browser/goToCommands": [
		"Vorschau",
		"Definitionen",
		"Keine Definition gefunden für \"{0}\".",
		"Keine Definition gefunden",
		"Gehe zu Definition",
		"Gehe &&zu Definition",
		"Definition an der Seite öffnen",
		"Definition einsehen",
		"Deklarationen",
		"Keine Deklaration für \"{0}\" gefunden.",
		"Keine Deklaration gefunden.",
		"Zur Deklaration wechseln",
		"Gehe zu &&Deklaration",
		"Keine Deklaration für \"{0}\" gefunden.",
		"Keine Deklaration gefunden.",
		"Vorschau für Deklaration anzeigen",
		"Typdefinitionen",
		"Keine Typendefinition gefunden für \"{0}\"",
		"Keine Typendefinition gefunden",
		"Zur Typdefinition wechseln",
		"Zur &&Typdefinition wechseln",
		"Vorschau der Typdefinition anzeigen",
		"Implementierungen",
		"Keine Implementierung gefunden für \"{0}\"",
		"Keine Implementierung gefunden",
		"Gehe zu Implementierungen",
		"Gehe zu &&Implementierungen",
		"Vorschau für Implementierungen anzeigen",
		"Für \"{0}\" wurden keine Verweise gefunden.",
		"Keine Referenzen gefunden",
		"Gehe zu Verweisen",
		"Gehe zu &&Verweisen",
		"Verweise",
		"Vorschau für Verweise anzeigen",
		"Verweise",
		"Zum beliebigem Symbol wechseln",
		"Speicherorte",
		"Keine Ergebnisse für \"{0}\"",
		"Verweise",
	],
	"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition": [
		"Klicken Sie, um {0} Definitionen anzuzeigen.",
	],
	"vs/editor/contrib/gotoSymbol/browser/peek/referencesController": [
		"Gibt an, ob die Verweisvorschau sichtbar ist, z. B. \"Verweisvorschau\" oder \"Definition einsehen\".",
		"Wird geladen...",
		"{0} ({1})",
	],
	"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree": [
		"{0} Verweise",
		"{0} Verweis",
		"Verweise",
	],
	"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget": [
		"Keine Vorschau verfügbar.",
		"Keine Ergebnisse",
		"Verweise",
	],
	"vs/editor/contrib/gotoSymbol/browser/referencesModel": [
		"in {0} in Zeile {1} in Spalte {2}",
		"{0} in {1} in Zeile {2} in Spalte {3}",
		"1 Symbol in {0}, vollständiger Pfad {1}",
		"{0} Symbole in {1}, vollständiger Pfad {2}",
		"Es wurden keine Ergebnisse gefunden.",
		"1 Symbol in {0} gefunden",
		"{0} Symbole in {1} gefunden",
		"{0} Symbole in {1} Dateien gefunden",
	],
	"vs/editor/contrib/gotoSymbol/browser/symbolNavigation": [
		"Gibt an, ob Symbolpositionen vorliegen, bei denen die Navigation nur über die Tastatur möglich ist.",
		"Symbol {0} von {1}, {2} für nächstes",
		"Symbol {0} von {1}",
	],
	"vs/editor/contrib/hover/browser/hover": [
		"Anzeigen oder Fokus beim Daraufzeigen",
		"Beim Daraufzeigen wird der Fokus nicht automatisch verwendet.",
		"Beim Daraufzeigen wird nur dann den Fokus erhalten, wenn er bereits sichtbar ist.",
		"Beim Daraufzeigen wird automatisch der Fokus erhalten, wenn er angezeigt wird.",
		"Definitionsvorschauhover anzeigen",
		"Bildlauf nach oben beim Daraufzeigen",
		"Bildlauf nach unten beim Daraufzeigen",
		"Bildlauf nach links beim Daraufzeigen",
		"Bildlauf nach rechts beim Daraufzeigen",
		"Eine Seite nach oben beim Daraufzeigen",
		"Eine Seite nach unten beim Daraufzeigen",
		"Gehe nach oben beim Daraufzeigen",
		"Gehe nach unten beim Daraufzeigen",
	],
	"vs/editor/contrib/hover/browser/markdownHoverParticipant": [
		"Wird geladen...",
		"Das Rendering langer Zeilen wurde aus Leistungsgründen angehalten. Dies kann über „editor.stopRenderingLineAfter“ konfiguriert werden.",
		"Die Tokenisierung wird bei langen Zeilen aus Leistungsgründen übersprungen. Dies kann über „editor.maxTokenizationLineLength“ konfiguriert werden.",
	],
	"vs/editor/contrib/hover/browser/markerHoverParticipant": [
		"Problem anzeigen",
		"Keine Schnellkorrekturen verfügbar",
		"Es wird nach Schnellkorrekturen gesucht...",
		"Keine Schnellkorrekturen verfügbar",
		"Schnelle Problembehebung ...",
	],
	"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace": [
		"Durch vorherigen Wert ersetzen",
		"Durch nächsten Wert ersetzen",
	],
	"vs/editor/contrib/indentation/browser/indentation": [
		"Einzug in Leerzeichen konvertieren",
		"Einzug in Tabstopps konvertieren",
		"Konfigurierte Tabulatorgröße",
		"Standardregisterkartengröße",
		"Aktuelle Registerkartengröße",
		"Tabulatorgröße für aktuelle Datei auswählen",
		"Einzug mithilfe von Tabstopps",
		"Einzug mithilfe von Leerzeichen",
		"Anzeigegröße der Registerkarte ändern",
		"Einzug aus Inhalt erkennen",
		"Neuen Einzug für Zeilen festlegen",
		"Gewählte Zeilen zurückziehen",
	],
	"vs/editor/contrib/inlayHints/browser/inlayHintsHover": [
		"Zum Einfügen doppelklicken",
		"BEFEHL + Klicken",
		"STRG + Klicken",
		"OPTION + Klicken",
		"ALT + Klicken",
		"Wechseln Sie zu Definition ({0}), klicken Sie mit der rechten Maustaste, um weitere Informationen zu finden.",
		"Gehe zu Definition ({0})",
		"Befehl ausführen",
	],
	"vs/editor/contrib/inlineCompletions/browser/commands": [
		"Nächsten Inline-Vorschlag anzeigen",
		"Vorherigen Inline-Vorschlag anzeigen",
		"Inline-Vorschlag auslösen",
		"Nächstes Wort des Inline-Vorschlags annehmen",
		"Wort annehmen",
		"Nächste Zeile des Inlinevorschlags akzeptieren",
		"Zeile annehmen",
		"Inline-Vorschlag annehmen",
		"Annehmen",
		"Inlinevorschlag ausblenden",
		"Symbolleiste immer anzeigen",
	],
	"vs/editor/contrib/inlineCompletions/browser/hoverParticipant": [
		"Vorschlag:",
	],
	"vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys": [
		"Gibt an, ob ein Inline-Vorschlag sichtbar ist.",
		"Gibt an, ob der Inline-Vorschlag mit Leerzeichen beginnt.",
		"Ob der Inline-Vorschlag mit Leerzeichen beginnt, das kleiner ist als das, was durch die Tabulatortaste eingefügt werden würde",
		"Gibt an, ob Vorschläge für den aktuellen Vorschlag unterdrückt werden sollen",
	],
	"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController": [
		"Überprüfen Sie dies in der barrierefreien Ansicht ({0}).",
	],
	"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget": [
		"Symbol für die Anzeige des nächsten Parameterhinweises.",
		"Symbol für die Anzeige des vorherigen Parameterhinweises.",
		"{0} ({1})",
		"Zurück",
		"Weiter",
	],
	"vs/editor/contrib/lineSelection/browser/lineSelection": [
		"Zeilenauswahl erweitern",
	],
	"vs/editor/contrib/linesOperations/browser/linesOperations": [
		"Zeile nach oben kopieren",
		"Zeile nach oben &&kopieren",
		"Zeile nach unten kopieren",
		"Zeile nach unten ko&&pieren",
		"Auswahl duplizieren",
		"&&Auswahl duplizieren",
		"Zeile nach oben verschieben",
		"Zeile nach oben &&verschieben",
		"Zeile nach unten verschieben",
		"Zeile nach &&unten verschieben",
		"Zeilen aufsteigend sortieren",
		"Zeilen absteigend sortieren",
		"Doppelte Zeilen löschen",
		"Nachgestelltes Leerzeichen kürzen",
		"Zeile löschen",
		"Zeileneinzug",
		"Zeile ausrücken",
		"Zeile oben einfügen",
		"Zeile unten einfügen",
		"Alle übrigen löschen",
		"Alle rechts löschen",
		"Zeilen verknüpfen",
		"Zeichen um den Cursor herum transponieren",
		"In Großbuchstaben umwandeln",
		"In Kleinbuchstaben umwandeln",
		"In große Anfangsbuchstaben umwandeln",
		"In Snake Case umwandeln",
		"In Camel-Fall transformieren",
		"Verwandle dich in eine Kebab-Hülle",
	],
	"vs/editor/contrib/linkedEditing/browser/linkedEditing": [
		"Verknüpfte Bearbeitung starten",
		"Hintergrundfarbe, wenn der Editor automatisch nach Typ umbenennt.",
	],
	"vs/editor/contrib/links/browser/links": [
		"Fehler beim Öffnen dieses Links, weil er nicht wohlgeformt ist: {0}",
		"Fehler beim Öffnen dieses Links, weil das Ziel fehlt.",
		"Befehl ausführen",
		"Link folgen",
		"BEFEHL + Klicken",
		"STRG + Klicken",
		"OPTION + Klicken",
		"alt + klicken",
		"Führen Sie den Befehl \"{0}\" aus.",
		"Link öffnen",
	],
	"vs/editor/contrib/message/browser/messageController": [
		"Gibt an, ob der Editor zurzeit eine Inlinenachricht anzeigt.",
	],
	"vs/editor/contrib/multicursor/browser/multicursor": [
		"Hinzugefügter Cursor: {0}",
		"Hinzugefügte Cursor: {0}",
		"Cursor oberhalb hinzufügen",
		"Cursor oberh&&alb hinzufügen",
		"Cursor unterhalb hinzufügen",
		"Cursor unterhal&&b hinzufügen",
		"Cursor an Zeilenenden hinzufügen",
		"C&&ursor an Zeilenenden hinzufügen",
		"Cursor am Ende hinzufügen",
		"Cursor am Anfang hinzufügen",
		"Auswahl zur nächsten Übereinstimmungssuche hinzufügen",
		"&&Nächstes Vorkommen hinzufügen",
		"Letzte Auswahl zu vorheriger Übereinstimmungssuche hinzufügen",
		"Vo&&rheriges Vorkommen hinzufügen",
		"Letzte Auswahl in nächste Übereinstimmungssuche verschieben",
		"Letzte Auswahl in vorherige Übereinstimmungssuche verschieben",
		"Alle Vorkommen auswählen und Übereinstimmung suchen",
		"Alle V&&orkommen auswählen",
		"Alle Vorkommen ändern",
		"Fokus auf nächsten Cursor",
		"Fokussiert den nächsten Cursor",
		"Fokus auf vorherigen Cursor",
		"Fokussiert den vorherigen Cursor",
	],
	"vs/editor/contrib/parameterHints/browser/parameterHints": [
		"Parameterhinweise auslösen",
	],
	"vs/editor/contrib/parameterHints/browser/parameterHintsWidget": [
		"Symbol für die Anzeige des nächsten Parameterhinweises.",
		"Symbol für die Anzeige des vorherigen Parameterhinweises.",
		"{0}, Hinweis",
		"Vordergrundfarbe des aktiven Elements im Parameterhinweis.",
	],
	"vs/editor/contrib/peekView/browser/peekView": [
		"Gibt an, ob der aktuelle Code-Editor in der Vorschau eingebettet ist.",
		"Schließen",
		"Hintergrundfarbe des Titelbereichs der Peek-Ansicht.",
		"Farbe des Titels in der Peek-Ansicht.",
		"Farbe der Titelinformationen in der Peek-Ansicht.",
		"Farbe der Peek-Ansichtsränder und des Pfeils.",
		"Hintergrundfarbe der Ergebnisliste in der Peek-Ansicht.",
		"Vordergrundfarbe für Zeilenknoten in der Ergebnisliste der Peek-Ansicht.",
		"Vordergrundfarbe für Dateiknoten in der Ergebnisliste der Peek-Ansicht.",
		"Hintergrundfarbe des ausgewählten Eintrags in der Ergebnisliste der Peek-Ansicht.",
		"Vordergrundfarbe des ausgewählten Eintrags in der Ergebnisliste der Peek-Ansicht.",
		"Hintergrundfarbe des Peek-Editors.",
		"Hintergrundfarbe der Leiste im Peek-Editor.",
		"Die Hintergrundfarbe für den „Sticky“-Bildlaufeffekt im Editor für die „Peek“-Ansicht.",
		"Farbe für Übereinstimmungsmarkierungen in der Ergebnisliste der Peek-Ansicht.",
		"Farbe für Übereinstimmungsmarkierungen im Peek-Editor.",
		"Rahmen für Übereinstimmungsmarkierungen im Peek-Editor.",
	],
	"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess": [
		"Öffnen Sie zuerst einen Text-Editor, um zu einer Zeile zu wechseln.",
		"Wechseln Sie zu Zeile {0} und Zeichen {1}.",
		"Zu Zeile {0} wechseln.",
		"Aktuelle Zeile: {0}, Zeichen: {1}. Geben Sie eine Zeilennummer zwischen 1 und {2} ein, zu der Sie navigieren möchten.",
		"Aktuelle Zeile: {0}, Zeichen: {1}. Geben Sie eine Zeilennummer ein, zu der Sie navigieren möchten.",
	],
	"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess": [
		"Öffnen Sie zunächst einen Text-Editor mit Symbolinformationen, um zu einem Symbol zu navigieren.",
		"Der aktive Text-Editor stellt keine Symbolinformationen bereit.",
		"Keine übereinstimmenden Editorsymbole.",
		"Keine Editorsymbole.",
		"An der Seite öffnen",
		"Unten öffnen",
		"Symbole ({0})",
		"Eigenschaften ({0})",
		"Methoden ({0})",
		"Funktionen ({0})",
		"Konstruktoren ({0})",
		"Variablen ({0})",
		"Klassen ({0})",
		"Strukturen ({0})",
		"Ereignisse ({0})",
		"Operatoren ({0})",
		"Schnittstellen ({0})",
		"Namespaces ({0})",
		"Pakete ({0})",
		"Typparameter ({0})",
		"Module ({0})",
		"Eigenschaften ({0})",
		"Enumerationen ({0})",
		"Enumerationsmember ({0})",
		"Zeichenfolgen ({0})",
		"Dateien ({0})",
		"Arrays ({0})",
		"Zahlen ({0})",
		"Boolesche Werte ({0})",
		"Objekte ({0})",
		"Schlüssel ({0})",
		"Felder ({0})",
		"Konstanten ({0})",
	],
	"vs/editor/contrib/readOnlyMessage/browser/contribution": [
		"Bearbeitung von schreibgeschützter Eingabe nicht möglich",
		"Ein Bearbeiten ist im schreibgeschützten Editor nicht möglich",
	],
	"vs/editor/contrib/rename/browser/rename": [
		"Kein Ergebnis.",
		"Ein unbekannter Fehler ist beim Auflösen der Umbenennung eines Ortes aufgetreten.",
		"\'{0}\' wird in \'{1}\' umbenannt",
		"{0} wird in {1} umbenannt.",
		"\"{0}\" erfolgreich in \"{1}\" umbenannt. Zusammenfassung: {2}",
		"Die rename-Funktion konnte die Änderungen nicht anwenden.",
		"Die rename-Funktion konnte die Änderungen nicht berechnen.",
		"Symbol umbenennen",
		"Möglichkeit aktivieren/deaktivieren, Änderungen vor dem Umbenennen als Vorschau anzeigen zu lassen",
	],
	"vs/editor/contrib/rename/browser/renameInputField": [
		"Gibt an, ob das Widget zum Umbenennen der Eingabe sichtbar ist.",
		"Benennen Sie die Eingabe um. Geben Sie einen neuen Namen ein, und drücken Sie die EINGABETASTE, um den Commit auszuführen.",
		"{0} zur Umbenennung, {1} zur Vorschau",
	],
	"vs/editor/contrib/smartSelect/browser/smartSelect": [
		"Auswahl aufklappen",
		"Auswahl &&erweitern",
		"Markierung verkleinern",
		"Au&&swahl verkleinern",
	],
	"vs/editor/contrib/snippet/browser/snippetController2": [
		"Gibt an, ob der Editor sich zurzeit im Schnipselmodus befindet.",
		"Gibt an, ob ein nächster Tabstopp im Schnipselmodus vorhanden ist.",
		"Gibt an, ob ein vorheriger Tabstopp im Schnipselmodus vorhanden ist.",
		"Zum nächsten Platzhalter wechseln...",
	],
	"vs/editor/contrib/snippet/browser/snippetVariables": [
		"Sonntag",
		"Montag",
		"Dienstag",
		"Mittwoch",
		"Donnerstag",
		"Freitag",
		"Samstag",
		"So",
		"Mo",
		"Di",
		"Mi",
		"Do",
		"Fr",
		"Sa",
		"Januar",
		"Februar",
		"März",
		"April",
		"Mai",
		"Juni",
		"Juli",
		"August",
		"September",
		"Oktober",
		"November",
		"Dezember",
		"Jan",
		"Feb",
		"Mär",
		"Apr",
		"Mai",
		"Jun",
		"Jul",
		"Aug",
		"Sep",
		"Okt",
		"Nov",
		"Dez",
	],
	"vs/editor/contrib/stickyScroll/browser/stickyScrollActions": [
		"Fixierten Bildlauf umschalten",
		"Fixierten Bildlauf &&umschalten",
		"Fixierter Bildlauf",
		"&&Fixierter Bildlauf",
		"Fokus auf Fixierten Bildlauf",
		"&&Fokus fixierter Bildlauf",
		"Nächste fixierte Zeile auswählen",
		"Zuletzt gewählte fixierte Zeile auswählen",
		"Gehe zur fokussierten fixierten Zeile",
		"Editor auswählen",
	],
	"vs/editor/contrib/suggest/browser/suggest": [
		"Gibt an, ob ein Vorschlag fokussiert ist",
		"Gibt an, ob Vorschlagsdetails sichtbar sind.",
		"Gibt an, ob mehrere Vorschläge zur Auswahl stehen.",
		"Gibt an, ob das Einfügen des aktuellen Vorschlags zu einer Änderung führt oder ob bereits alles eingegeben wurde.",
		"Gibt an, ob Vorschläge durch Drücken der EINGABETASTE eingefügt werden.",
		"Gibt an, ob der aktuelle Vorschlag Verhalten zum Einfügen und Ersetzen aufweist.",
		"Gibt an, ob Einfügen oder Ersetzen als Standardverhalten verwendet wird.",
		"Gibt an, ob der aktuelle Vorschlag die Auflösung weiterer Details unterstützt.",
	],
	"vs/editor/contrib/suggest/browser/suggestController": [
		"Das Akzeptieren von \"{0}\" ergab {1} zusätzliche Bearbeitungen.",
		"Vorschlag auslösen",
		"Einfügen",
		"Einfügen",
		"Ersetzen",
		"Ersetzen",
		"Einfügen",
		"weniger anzeigen",
		"mehr anzeigen",
		"Größe des Vorschlagswidgets zurücksetzen",
	],
	"vs/editor/contrib/suggest/browser/suggestWidget": [
		"Hintergrundfarbe des Vorschlagswidgets.",
		"Rahmenfarbe des Vorschlagswidgets.",
		"Vordergrundfarbe des Vorschlagswidgets.",
		"Die Vordergrundfarbe des ausgewählten Eintrags im Vorschlagswidget.",
		"Die Vordergrundfarbe des Symbols des ausgewählten Eintrags im Vorschlagswidget.",
		"Hintergrundfarbe des ausgewählten Eintrags im Vorschlagswidget.",
		"Farbe der Trefferhervorhebung im Vorschlagswidget.",
		"Die Farbe des Treffers wird im Vorschlagswidget hervorgehoben, wenn ein Element fokussiert wird.",
		"Vordergrundfarbe des Status des Vorschlagswidgets.",
		"Wird geladen...",
		"Keine Vorschläge.",
		"Vorschlagen",
		"{0} {1}, {2}",
		"{0} {1}",
		"{0}, {1}",
		"{0}, Dokumente: {1}",
	],
	"vs/editor/contrib/suggest/browser/suggestWidgetDetails": [
		"Schließen",
		"Wird geladen...",
	],
	"vs/editor/contrib/suggest/browser/suggestWidgetRenderer": [
		"Symbol für weitere Informationen im Vorschlags-Widget.",
		"Weitere Informationen",
	],
	"vs/editor/contrib/suggest/browser/suggestWidgetStatus": [
		"{0} ({1})",
	],
	"vs/editor/contrib/symbolIcons/browser/symbolIcons": [
		"Die Vordergrundfarbe für Arraysymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für boolesche Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Klassensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Farbsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für konstante Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Konstruktorsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Enumeratorsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Enumeratormembersymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Ereignissymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Feldsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Dateisymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Ordnersymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Funktionssymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Schnittstellensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Schlüsselsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Schlüsselwortsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Methodensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Modulsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Namespacesymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für NULL-Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Zahlensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Objektsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Operatorsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Paketsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Eigenschaftensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Referenzsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Codeschnipselsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Zeichenfolgensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Struktursymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Textsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Typparametersymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für Einheitensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
		"Die Vordergrundfarbe für variable Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.",
	],
	"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode": [
		"TAB-Umschalttaste verschiebt Fokus",
		"Beim Drücken auf Tab wird der Fokus jetzt auf das nächste fokussierbare Element verschoben",
		"Beim Drücken von Tab wird jetzt das Tabulator-Zeichen eingefügt",
	],
	"vs/editor/contrib/tokenization/browser/tokenization": [
		"Entwickler: Force Retokenize",
	],
	"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter": [
		"Symbol, das mit einer Warnmeldung im Erweiterungs-Editor angezeigt wird.",
		"Dieses Dokument enthält viele nicht einfache ASCII-Unicode-Zeichen.",
		"Dieses Dokument enthält viele mehrdeutige Unicode-Zeichen.",
		"Dieses Dokument enthält viele unsichtbare Unicode-Zeichen.",
		"Das Zeichen {0} kann mit dem Zeichen {1} verwechselt werden, was im Quellcode häufiger vorkommt.",
		"Das Zeichen {0} kann mit dem Zeichen {1} verwechselt werden, was im Quellcode häufiger vorkommt.",
		"Das Zeichen {0} ist nicht sichtbar.",
		"Das Zeichen {0} ist kein einfaches ASCII-Zeichen.",
		"Einstellungen anpassen",
		"Hervorhebung in Kommentaren deaktivieren",
		"Deaktivieren der Hervorhebung von Zeichen in Kommentaren",
		"Hervorhebung in Zeichenfolgen deaktivieren",
		"Deaktivieren der Hervorhebung von Zeichen in Zeichenfolgen",
		"Mehrdeutige Hervorhebung deaktivieren",
		"Deaktivieren der Hervorhebung von mehrdeutigen Zeichen",
		"Unsichtbare Hervorhebung deaktivieren",
		"Deaktivieren der Hervorhebung unsichtbarer Zeichen",
		"Nicht-ASCII-Hervorhebung deaktivieren",
		"Deaktivieren der Hervorhebung von nicht einfachen ASCII-Zeichen",
		"Ausschlussoptionen anzeigen",
		"{0} (unsichtbares Zeichen) von der Hervorhebung ausschließen",
		"{0} nicht hervorheben",
		"Unicodezeichen zulassen, die in der Sprache „{0}“ häufiger vorkommen.",
		"Konfigurieren der Optionen für die Unicode-Hervorhebung",
	],
	"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators": [
		"Ungewöhnliche Zeilentrennzeichen",
		"Ungewöhnliche Zeilentrennzeichen erkannt",
		"Die Datei \"{0}\" enthält mindestens ein ungewöhnliches Zeilenabschlusszeichen, z. B. Zeilentrennzeichen (LS) oder Absatztrennzeichen (PS).\r\n\r\nEs wird empfohlen, sie aus der Datei zu entfernen. Dies kann über \"editor.unusualLineTerminators\" konfiguriert werden.",
		"&&Ungewöhnliche Zeilenabschlusszeichen entfernen",
		"Ignorieren",
	],
	"vs/editor/contrib/wordHighlighter/browser/highlightDecorations": [
		"Hintergrundfarbe eines Symbols beim Lesezugriff, z.B. beim Lesen einer Variablen. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.",
		"Hintergrundfarbe eines Symbols bei Schreibzugriff, z.B. beim Schreiben in eine Variable. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Die Hintergrundfarbe eines Textteils für ein Symbol. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.",
		"Randfarbe eines Symbols beim Lesezugriff, wie etwa beim Lesen einer Variablen.",
		"Randfarbe eines Symbols beim Schreibzugriff, wie etwa beim Schreiben einer Variablen.",
		"Die Rahmenfarbe eines Textteils für ein Symbol.",
		"Übersichtslinealmarkerfarbd für das Hervorheben von Symbolen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Übersichtslinealmarkerfarbe für Symbolhervorhebungen bei Schreibzugriff. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Die Markierungsfarbe des Übersichtslineals eines Textteils für ein Symbol. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.",
	],
	"vs/editor/contrib/wordHighlighter/browser/wordHighlighter": [
		"Gehe zur nächsten Symbolhervorhebungen",
		"Gehe zur vorherigen Symbolhervorhebungen",
		"Symbol-Hervorhebung ein-/ausschalten",
	],
	"vs/editor/contrib/wordOperations/browser/wordOperations": [
		"Wort löschen",
	],
	"vs/platform/action/common/actionCommonCategories": [
		"Ansehen",
		"Hilfe",
		"Test",
		"Datei",
		"Einstellungen",
		"Entwickler",
	],
	"vs/platform/actionWidget/browser/actionList": [
		"{0} zum Anwenden, {1} für die Vorschau",
		"{0} zum Anwenden",
		"{0} deaktiviert, Grund: {1}",
		"Aktionswidget",
	],
	"vs/platform/actionWidget/browser/actionWidget": [
		"Hintergrundfarbe für umgeschaltete Aktionselemente in der Aktionsleiste.",
		"Gibt an, ob die Aktionswidgetliste sichtbar ist.",
		"Codeaktionswidget ausblenden",
		"Vorherige Aktion auswählen",
		"Nächste Aktion auswählen",
		"Ausgewählte Aktion akzeptieren",
		"Vorschau für ausgewählte Elemente anzeigen",
	],
	"vs/platform/actions/browser/menuEntryActionViewItem": [
		"{0} ({1})",
		"{0} ({1})",
		"{0}\r\n[{1}] {2}",
	],
	"vs/platform/actions/browser/toolbar": [
		"Ausblenden",
		"Menü zurücksetzen",
	],
	"vs/platform/actions/common/menuService": [
		"\"{0}\" ausblenden",
	],
	"vs/platform/audioCues/browser/audioCueService": [
		"Fehler in der Zeile",
		"Warnung in der Zeile",
		"Gefalteter Bereich in der Zeile",
		"Haltepunkt in der Zeile",
		"Inlinevorschlag in der Zeile",
		"Terminale schnelle Problembehebung",
		"Debugger auf Haltepunkt beendet",
		"Keine Inlay-Hinweise in der Zeile",
		"Aufgabe abgeschlossen",
		"Aufgabe fehlgeschlagen",
		"Terminalbefehl fehlgeschlagen",
		"Terminalglocke",
		"Notebookzelle abgeschlossen",
		"Notebookzelle fehlgeschlagen",
		"Vergleichslinie eingefügt",
		"Vergleichslinie gelöscht",
		"Vergleichslinie geändert",
		"Chatanfrage gesendet",
		"Chatantwort empfangen",
		"Chatantwort ausstehend",
		"Löschen",
		"Speichern",
		"Formatieren",
	],
	"vs/platform/configuration/common/configurationRegistry": [
		"Außerkraftsetzungen für die Standardsprachkonfiguration",
		"Konfigurieren Sie Einstellungen, die für die Sprache {0} überschrieben werden sollen.",
		"Zu überschreibende Editor-Einstellungen für eine Sprache konfigurieren.",
		"Diese Einstellung unterstützt keine sprachspezifische Konfiguration.",
		"Zu überschreibende Editor-Einstellungen für eine Sprache konfigurieren.",
		"Diese Einstellung unterstützt keine sprachspezifische Konfiguration.",
		"Eine leere Eigenschaft kann nicht registriert werden.",
		"\"{0}\" kann nicht registriert werden. Stimmt mit dem Eigenschaftsmuster \"\\\\[.*\\\\]$\" zum Beschreiben sprachspezifischer Editor-Einstellungen überein. Verwenden Sie den Beitrag \"configurationDefaults\".",
		"{0}\" kann nicht registriert werden. Diese Eigenschaft ist bereits registriert.",
		"\"{0}\" kann nicht registriert werden. Die zugeordnete Richtlinie {1} ist bereits bei {2} registriert.",
	],
	"vs/platform/contextkey/browser/contextKeyService": [
		"Ein Befehl, der Informationen zu Kontextschlüsseln zurückgibt",
	],
	"vs/platform/contextkey/common/contextkey": [
		"Leerer Kontextschlüsselausdruck",
		"Haben Sie vergessen, einen Ausdruck zu schreiben? Sie können auch „false“ oder „true“ festlegen, um immer auf „false“ oder „true“ auszuwerten.",
		"„in“ nach „not“.",
		"schließende Klammer „)“",
		"Unerwartetes Token",
		"Haben Sie vergessen, && oder || vor dem Token einzufügen?",
		"Unerwartetes Ende des Ausdrucks.",
		"Haben Sie vergessen, einen Kontextschlüssel zu setzen?",
		"Erwartet: {0}\r\nEmpfangen: „{1}“.",
	],
	"vs/platform/contextkey/common/contextkeys": [
		"Gibt an, ob macOS als Betriebssystem verwendet wird.",
		"Gibt an, ob Linux als Betriebssystem verwendet wird.",
		"Gibt an, ob Windows als Betriebssystem verwendet wird.",
		"Gibt an, ob es sich bei der Plattform um einen Webbrowser handelt.",
		"Gibt an, ob macOS auf einer Nicht-Browser-Plattform als Betriebssystem verwendet wird.",
		"Gibt an, ob iOS als Betriebssystem verwendet wird.",
		"Gibt an, ob es sich bei der Plattform um einen mobilen Webbrowser handelt.",
		"Qualitätstyp des VS Codes",
		"Gibt an, ob sich der Tastaturfokus in einem Eingabefeld befindet.",
	],
	"vs/platform/contextkey/common/scanner": [
		"Meinten Sie {0}?",
		"Meinten Sie {0} oder {1}?",
		"Meinten Sie {0}, {1} oder {2}?",
		"Haben Sie vergessen, das Anführungszeichen zu öffnen oder zu schließen?",
		"Haben Sie vergessen, das Zeichen „/“ (Schrägstrich) zu escapen? Setzen Sie zwei Backslashes davor, um es zu escapen, z. B. „\\\\/“.",
	],
	"vs/platform/history/browser/contextScopedHistoryWidget": [
		"Gibt an, ob Vorschläge sichtbar sind.",
	],
	"vs/platform/keybinding/common/abstractKeybindingService": [
		"({0}) wurde gedrückt. Es wird auf die zweite Taste in der Kombination gewartet...",
		"({0}) wurde gedrückt. Es wird auf die zweite Taste in der Kombination gewartet...",
		"Die Tastenkombination ({0}, {1}) ist kein Befehl.",
		"Die Tastenkombination ({0}, {1}) ist kein Befehl.",
	],
	"vs/platform/list/browser/listService": [
		"Workbench",
		"Ist unter Windows und Linux der STRG-Taste und unter macOS der Befehlstaste zugeordnet.",
		"Ist unter Windows und Linux der ALT-Taste und unter macOS der Wahltaste zugeordnet.",
		"Der Modifizierer zum Hinzufügen eines Elements in Bäumen und Listen zu einer Mehrfachauswahl mit der Maus (zum Beispiel im Explorer, in geöffneten Editoren und in der SCM-Ansicht). Die Mausbewegung \"Seitlich öffnen\" wird – sofern unterstützt – so angepasst, dass kein Konflikt mit dem Modifizierer für Mehrfachauswahl entsteht.",
		"Steuert, wie Elemente in Strukturen und Listen mithilfe der Maus geöffnet werden (sofern unterstützt). Bei übergeordneten Elementen, deren untergeordnete Elemente sich in Strukturen befinden, steuert diese Einstellung, ob ein Einfachklick oder ein Doppelklick das übergeordnete Elemente erweitert. Beachten Sie, dass einige Strukturen und Listen diese Einstellung ggf. ignorieren, wenn sie nicht zutrifft.",
		"Steuert, ob Listen und Strukturen ein horizontales Scrollen in der Workbench unterstützen. Warnung: Das Aktivieren dieser Einstellung kann sich auf die Leistung auswirken.",
		"Steuert, ob Klicks in der Bildlaufleiste Seite für Seite scrollen.",
		"Steuert den Struktureinzug in Pixeln.",
		"Steuert, ob die Struktur Einzugsführungslinien rendern soll.",
		"Steuert, ob Listen und Strukturen einen optimierten Bildlauf verwenden.",
		"Ein Multiplikator, der für die Mausrad-Bildlaufereignisse \"deltaX\" und \"deltaY\" verwendet werden soll.",
		"Multiplikator für Scrollgeschwindigkeit bei Drücken von ALT.",
		"Elemente beim Suchen hervorheben. Die Navigation nach oben und unten durchläuft dann nur die markierten Elemente.",
		"Filterelemente bei der Suche.",
		"Steuert den Standardsuchmodus für Listen und Strukturen in der Workbench.",
		"Bei der einfachen Tastaturnavigation werden Elemente in den Fokus genommen, die mit der Tastatureingabe übereinstimmen. Die Übereinstimmungen gelten nur für Präfixe.",
		"Hervorheben von Tastaturnavigationshervorgebungselemente, die mit der Tastatureingabe übereinstimmen. Beim nach oben und nach unten Navigieren werden nur die hervorgehobenen Elemente durchlaufen.",
		"Durch das Filtern der Tastaturnavigation werden alle Elemente herausgefiltert und ausgeblendet, die nicht mit der Tastatureingabe übereinstimmen.",
		"Steuert die Tastaturnavigation in Listen und Strukturen in der Workbench. Kann \"simple\" (einfach), \"highlight\" (hervorheben) und \"filter\" (filtern) sein.",
		"Bitte verwenden Sie stattdessen „workbench.list.defaultFindMode“ und „workbench.list.typeNavigationMode“.",
		"Verwenden Sie bei der Suche eine Fuzzyübereinstimmung.",
		"Verwenden Sie bei der Suche eine zusammenhängende Übereinstimmung.",
		"Steuert den Typ der Übereinstimmung, der beim Durchsuchen von Listen und Strukturen in der Workbench verwendet wird.",
		"Steuert, wie Strukturordner beim Klicken auf die Ordnernamen erweitert werden. Beachten Sie, dass einige Strukturen und Listen diese Einstellung ggf. ignorieren, wenn sie nicht zutrifft.",
		"Steuert, ob fester Bildlauf in Strukturen aktiviert ist.",
		"Steuert die Anzahl der festen Elemente, die in der Struktur angezeigt werden, wenn \"#workbench.tree.enableStickyScroll#\" aktiviert ist.",
		"Steuert die Funktionsweise der Typnavigation in Listen und Strukturen in der Workbench. Bei einer Festlegung auf \"trigger\" beginnt die Typnavigation, sobald der Befehl \"list.triggerTypeNavigation\" ausgeführt wird.",
	],
	"vs/platform/markers/common/markers": [
		"Fehler",
		"Warnung",
		"Info",
	],
	"vs/platform/quickinput/browser/commandsQuickAccess": [
		"zuletzt verwendet",
		"ähnliche Befehle",
		"häufig verwendet",
		"andere Befehle",
		"ähnliche Befehle",
		"{0}, {1}",
		"Der Befehl \"{0}\" hat zu einem Fehler geführt.",
	],
	"vs/platform/quickinput/browser/helpQuickAccess": [
		"{0}, {1}",
	],
	"vs/platform/quickinput/browser/quickInput": [
		"Zurück",
		"Drücken Sie die EINGABETASTE, um Ihre Eingabe zu bestätigen, oder ESC, um den Vorgang abzubrechen.",
		"{0}/{1}",
		"Nehmen Sie eine Eingabe vor, um die Ergebnisse einzugrenzen.",
	],
	"vs/platform/quickinput/browser/quickInputController": [
		"Aktivieren Sie alle Kontrollkästchen",
		"{0} Ergebnisse",
		"{0} ausgewählt",
		"OK",
		"Benutzerdefiniert",
		"Zurück ({0})",
		"Zurück",
	],
	"vs/platform/quickinput/browser/quickInputList": [
		"Schnelleingabe",
	],
	"vs/platform/quickinput/browser/quickInputUtils": [
		"Klicken, um den Befehl \"{0}\" auszuführen",
	],
	"vs/platform/theme/common/colorRegistry": [
		"Allgemeine Vordergrundfarbe. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.",
		"Allgemeine Vordergrundfarbe. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.",
		"Allgemeine Vordergrundfarbe für Fehlermeldungen. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.",
		"Vordergrundfarbe für Beschreibungstexte, die weitere Informationen anzeigen, z.B. für eine Beschriftung.",
		"Die für Symbole in der Workbench verwendete Standardfarbe.",
		"Allgemeine Rahmenfarbe für fokussierte Elemente. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.",
		"Ein zusätzlicher Rahmen um Elemente, mit dem diese von anderen getrennt werden, um einen größeren Kontrast zu erreichen.",
		"Ein zusätzlicher Rahmen um aktive Elemente, mit dem diese von anderen getrennt werden, um einen größeren Kontrast zu erreichen.",
		"Hintergrundfarbe der Textauswahl in der Workbench (z.B. für Eingabefelder oder Textbereiche). Diese Farbe gilt nicht für die Auswahl im Editor.",
		"Farbe für Text-Trennzeichen.",
		"Vordergrundfarbe für Links im Text.",
		"Vordergrundfarbe für angeklickte Links im Text und beim Zeigen darauf mit der Maus.",
		"Vordergrundfarbe für vorformatierte Textsegmente.",
		"Hintergrundfarbe für vorformatierte Textsegmente.",
		"Hintergrundfarbe für Blockzitate im Text.",
		"Rahmenfarbe für blockquote-Elemente im Text.",
		"Hintergrundfarbe für Codeblöcke im Text.",
		"Schattenfarbe von Widgets wie zum Beispiel Suchen/Ersetzen innerhalb des Editors.",
		"Die Rahmenfarbe von Widgets, z. B. Suchen/Ersetzen im Editor.",
		"Hintergrund für Eingabefeld.",
		"Vordergrund für Eingabefeld.",
		"Rahmen für Eingabefeld.",
		"Rahmenfarbe für aktivierte Optionen in Eingabefeldern.",
		"Hintergrundfarbe für aktivierte Optionen in Eingabefeldern.",
		"Hintergrundfarbe beim Daraufzeigen für Optionen in Eingabefeldern.",
		"Vordergrundfarbe für aktivierte Optionen in Eingabefeldern.",
		"Eingabefeld-Vordergrundfarbe für Platzhaltertext.",
		"Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad der Information.",
		"Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad der Information.",
		"Rahmenfarbe bei der Eingabevalidierung für den Schweregrad der Information.",
		"Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.",
		"Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.",
		"Rahmenfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.",
		"Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.",
		"Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.",
		"Rahmenfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.",
		"Hintergrund für Dropdown.",
		"Hintergrund für Dropdownliste.",
		"Vordergrund für Dropdown.",
		"Rahmen für Dropdown.",
		"Vordergrundfarbe der Schaltfläche.",
		"Farbe des Schaltflächentrennzeichens.",
		"Hintergrundfarbe der Schaltfläche.",
		"Hintergrundfarbe der Schaltfläche, wenn darauf gezeigt wird.",
		"Rahmenfarbe der Schaltfläche.",
		"Sekundäre Vordergrundfarbe der Schaltfläche.",
		"Hintergrundfarbe der sekundären Schaltfläche.",
		"Hintergrundfarbe der sekundären Schaltfläche beim Daraufzeigen.",
		"Hintergrundfarbe für Badge. Badges sind kurze Info-Texte, z.B. für Anzahl Suchergebnisse.",
		"Vordergrundfarbe für Badge. Badges sind kurze Info-Texte, z.B. für Anzahl Suchergebnisse.",
		"Schatten der Scrollleiste, um anzuzeigen, dass die Ansicht gescrollt wird.",
		"Hintergrundfarbe vom Scrollbar-Schieber",
		"Hintergrundfarbe des Schiebereglers, wenn darauf gezeigt wird.",
		"Hintergrundfarbe des Schiebereglers, wenn darauf geklickt wird.",
		"Hintergrundfarbe des Fortschrittbalkens, der für zeitintensive Vorgänge angezeigt werden kann.",
		"Hintergrundfarbe für Fehlertext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Vordergrundfarbe von Fehlerunterstreichungen im Editor.",
		"Wenn festgelegt, wird die Farbe doppelter Unterstreichungen für Fehler im Editor angezeigt.",
		"Hintergrundfarbe für Warnungstext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Vordergrundfarbe von Warnungsunterstreichungen im Editor.",
		"Wenn festgelegt, wird die Farbe doppelter Unterstreichungen für Warnungen im Editor angezeigt.",
		"Hintergrundfarbe für Infotext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Vordergrundfarbe von Informationsunterstreichungen im Editor.",
		"Wenn festgelegt, wird die Farbe doppelter Unterstreichungen für Infos im Editor angezeigt.",
		"Vordergrundfarbe der Hinweisunterstreichungen im Editor.",
		"Wenn festgelegt, wird die Farbe doppelter Unterstreichungen für Hinweise im Editor angezeigt.",
		"Rahmenfarbe aktiver Trennleisten.",
		"Hintergrundfarbe des Editors.",
		"Standardvordergrundfarbe des Editors.",
		"Einrastfunktion der Hintergrundfarbe für den Editor",
		"Einrastfunktion beim Daraufzeigen der Hintergrundfarbe für den Editor",
		"Hintergrundfarbe von Editor-Widgets wie zum Beispiel Suchen/Ersetzen.",
		"Vordergrundfarbe für Editorwidgets wie Suchen/Ersetzen.",
		"Rahmenfarbe von Editorwigdets. Die Farbe wird nur verwendet, wenn für das Widget ein Rahmen verwendet wird und die Farbe nicht von einem Widget überschrieben wird.",
		"Rahmenfarbe der Größenanpassungsleiste von Editorwigdets. Die Farbe wird nur verwendet, wenn für das Widget ein Größenanpassungsrahmen verwendet wird und die Farbe nicht von einem Widget außer Kraft gesetzt wird.",
		"Schnellauswahl der Hintergrundfarbe. Im Widget für die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.",
		"Vordergrundfarbe der Schnellauswahl. Im Widget für die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.",
		"Hintergrundfarbe für den Titel der Schnellauswahl. Im Widget für die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.",
		"Schnellauswahlfarbe für das Gruppieren von Bezeichnungen.",
		"Schnellauswahlfarbe für das Gruppieren von Rahmen.",
		"Die Hintergrundfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.",
		"Die Vordergrundfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.",
		"Die Rahmenfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.",
		"Die Rahmenfarbe der Schaltfläche der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.",
		"Farbe der Editor-Auswahl.",
		"Farbe des gewählten Text für einen hohen Kontrast",
		"Die Farbe der Auswahl befindet sich in einem inaktiven Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegende Dekorationen verdeckt.",
		"Farbe für Bereiche mit dem gleichen Inhalt wie die Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Randfarbe für Bereiche, deren Inhalt der Auswahl entspricht.",
		"Farbe des aktuellen Suchergebnisses.",
		"Farbe der anderen Suchergebnisse. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Farbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.",
		"Randfarbe des aktuellen Suchergebnisses.",
		"Randfarbe der anderen Suchtreffer.",
		"Rahmenfarbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Farbe der Abfrageübereinstimmungen des Such-Editors",
		"Rahmenfarbe der Abfrageübereinstimmungen des Such-Editors",
		"Farbe des Texts in der Abschlussmeldung des Such-Viewlets.",
		"Hervorhebung unterhalb des Worts, für das ein Hoverelement angezeigt wird. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Hintergrundfarbe des Editor-Mauszeigers.",
		"Vordergrundfarbe des Editor-Mauszeigers",
		"Rahmenfarbe des Editor-Mauszeigers.",
		"Hintergrundfarbe der Hoverstatusleiste des Editors.",
		"Farbe der aktiven Links.",
		"Vordergrundfarbe für Inlinehinweise",
		"Hintergrundfarbe für Inlinehinweise",
		"Vordergrundfarbe von Inlinehinweisen für Typen",
		"Hintergrundfarbe von Inlinehinweisen für Typen",
		"Vordergrundfarbe von Inlinehinweisen für Parameter",
		"Hintergrundfarbe von Inlinehinweisen für Parameter",
		"Die für das Aktionssymbol \"Glühbirne\" verwendete Farbe.",
		"Die für das Aktionssymbol \"Automatische Glühbirnenkorrektur\" verwendete Farbe.",
		"Die Farbe, die für das KI-Symbol der Glühbirne verwendet wird.",
		"Hintergrundfarbe für eingefügten Text. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Hintergrundfarbe für Text, der entfernt wurde. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Hintergrundfarbe für eingefügte Zeilen. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.",
		"Hintergrundfarbe für Zeilen, die entfernt wurden. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.",
		"Hintergrundfarbe für den Rand, an dem Zeilen eingefügt wurden.",
		"Hintergrundfarbe für den Rand, an dem die Zeilen entfernt wurden.",
		"Vordergrund des Diff-Übersichtslineals für eingefügten Inhalt.",
		"Vordergrund des Diff-Übersichtslineals für entfernten Inhalt.",
		"Konturfarbe für eingefügten Text.",
		"Konturfarbe für entfernten Text.",
		"Die Rahmenfarbe zwischen zwei Text-Editoren.",
		"Farbe der diagonalen Füllung des Vergleichs-Editors. Die diagonale Füllung wird in Ansichten mit parallelem Vergleich verwendet.",
		"Die Hintergrundfarbe von unveränderten Blöcken im Diff-Editor.",
		"Die Vordergrundfarbe von unveränderten Blöcken im Diff-Editor.",
		"Die Hintergrundfarbe des unveränderten Codes im Diff-Editor.",
		"Hintergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.",
		"Vordergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.",
		"Konturfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.",
		"Umrissfarbe der Liste/des Baums für das fokussierte Element, wenn die Liste/der Baum aktiv und ausgewählt ist. Eine aktive Liste/Baum hat Tastaturfokus, eine inaktive nicht.",
		"Hintergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.",
		"Vordergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.",
		"Vordergrundfarbe des Symbols der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.",
		"Hintergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.",
		"Vordergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Baumstruktur inaktiv ist. Eine aktive Liste/Baumstruktur hat Tastaturfokus, eine inaktive hingegen nicht.",
		"Vordergrundfarbe des Symbols der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.",
		"Hintergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.",
		"Konturfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.",
		"Hintergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.",
		"Vordergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.",
		"Drag & Drop-Hintergrund der Liste/Struktur, wenn Elemente mithilfe der Maus verschoben werden.",
		"Vordergrundfarbe der Liste/Struktur zur Trefferhervorhebung beim Suchen innerhalb der Liste/Struktur.",
		"Die Vordergrundfarbe der Liste/Struktur des Treffers hebt aktiv fokussierte Elemente hervor, wenn innerhalb der Liste / der Struktur gesucht wird.",
		"Vordergrundfarbe einer Liste/Struktur für ungültige Elemente, z.B. ein nicht ausgelöster Stamm im Explorer.",
		"Vordergrundfarbe für Listenelemente, die Fehler enthalten.",
		"Vordergrundfarbe für Listenelemente, die Warnungen enthalten.",
		"Hintergrundfarbe des Typfilterwidgets in Listen und Strukturen.",
		"Konturfarbe des Typfilterwidgets in Listen und Strukturen.",
		"Konturfarbe des Typfilterwidgets in Listen und Strukturen, wenn es keine Übereinstimmungen gibt.",
		"Schattenfarbe des Typfilterwidgets in Listen und Strukturen.",
		"Hintergrundfarbe der gefilterten Übereinstimmung",
		"Rahmenfarbe der gefilterten Übereinstimmung",
		"Strukturstrichfarbe für die Einzugsführungslinien.",
		"Strukturstrichfarbe für die Einzugslinien, die nicht aktiv sind.",
		"Tabellenrahmenfarbe zwischen Spalten.",
		"Hintergrundfarbe für ungerade Tabellenzeilen.",
		"Hintergrundfarbe für nicht hervorgehobene Listen-/Strukturelemente.",
		"Hintergrundfarbe von Kontrollkästchenwidget.",
		"Hintergrundfarbe des Kontrollkästchenwidgets, wenn das Element ausgewählt ist, in dem es sich befindet.",
		"Vordergrundfarbe von Kontrollkästchenwidget.",
		"Rahmenfarbe von Kontrollkästchenwidget.",
		"Rahmenfarbe des Kontrollkästchenwidgets, wenn das Element ausgewählt ist, in dem es sich befindet.",
		"Verwenden Sie stattdessen \"quickInputList.focusBackground\".",
		"Die Hintergrundfarbe der Schnellauswahl für das fokussierte Element.",
		"Die Vordergrundfarbe des Symbols der Schnellauswahl für das fokussierte Element.",
		"Die Hintergrundfarbe der Schnellauswahl für das fokussierte Element.",
		"Rahmenfarbe von Menüs.",
		"Vordergrundfarbe von Menüelementen.",
		"Hintergrundfarbe von Menüelementen.",
		"Vordergrundfarbe des ausgewählten Menüelements im Menü.",
		"Hintergrundfarbe des ausgewählten Menüelements im Menü.",
		"Rahmenfarbe des ausgewählten Menüelements im Menü.",
		"Farbe eines Trenner-Menüelements in Menüs.",
		"Symbolleistenhintergrund beim Bewegen der Maus über Aktionen",
		"Symbolleistengliederung beim Bewegen der Maus über Aktionen",
		"Symbolleistenhintergrund beim Halten der Maus über Aktionen",
		"Hervorhebungs-Hintergrundfarbe eines Codeschnipsel-Tabstopps.",
		"Hervorhebungs-Rahmenfarbe eines Codeschnipsel-Tabstopps.",
		"Hervorhebungs-Hintergrundfarbe des letzten Tabstopps eines Codeschnipsels.",
		"Rahmenfarbe zur Hervorhebung des letzten Tabstopps eines Codeschnipsels.",
		"Farbe der Breadcrumb-Elemente, die den Fokus haben.",
		"Hintergrundfarbe der Breadcrumb-Elemente.",
		"Farbe der Breadcrumb-Elemente, die den Fokus haben.",
		"Die Farbe der ausgewählten Breadcrumb-Elemente.",
		"Hintergrundfarbe des Breadcrumb-Auswahltools.",
		"Hintergrund des aktuellen Headers in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Hintergrund für den aktuellen Inhalt in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Hintergrund für eingehende Header in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Hintergrund für eingehenden Inhalt in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Headerhintergrund für gemeinsame Vorgängerelemente in Inlinezusammenführungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Hintergrund des Inhalts gemeinsamer Vorgängerelemente in Inlinezusammenführungskonflikt. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Rahmenfarbe für Kopfzeilen und die Aufteilung in Inline-Mergingkonflikten.",
		"Aktueller Übersichtslineal-Vordergrund für Inline-Mergingkonflikte.",
		"Eingehender Übersichtslineal-Vordergrund für Inline-Mergingkonflikte.",
		"Hintergrund des Übersichtslineals des gemeinsamen übergeordneten Elements bei Inlinezusammenführungskonflikten.",
		"Übersichtslinealmarkerfarbe für das Suchen von Übereinstimmungen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Übersichtslinealmarkerfarbe für das Hervorheben der Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.",
		"Minimap-Markerfarbe für gefundene Übereinstimmungen.",
		"Minimap-Markerfarbe für wiederholte Editorauswahlen.",
		"Minimap-Markerfarbe für die Editorauswahl.",
		"Minimapmarkerfarbe für Informationen.",
		"Minimapmarkerfarbe für Warnungen",
		"Minimapmarkerfarbe für Fehler",
		"Hintergrundfarbe der Minimap.",
		"Deckkraft von Vordergrundelementen, die in der Minimap gerendert werden. Beispiel: „#000000c0“ wird die Elemente mit einer Deckkraft von 75 % rendern.",
		"Hintergrundfarbe des Minimap-Schiebereglers.",
		"Hintergrundfarbe des Minimap-Schiebereglers beim Daraufzeigen.",
		"Hintergrundfarbe des Minimap-Schiebereglers, wenn darauf geklickt wird.",
		"Die Farbe, die für das Problemfehlersymbol verwendet wird.",
		"Die Farbe, die für das Problemwarnsymbol verwendet wird.",
		"Die Farbe, die für das Probleminfosymbol verwendet wird.",
		"Die in Diagrammen verwendete Vordergrundfarbe.",
		"Die für horizontale Linien in Diagrammen verwendete Farbe.",
		"Die in Diagrammvisualisierungen verwendete Farbe Rot.",
		"Die in Diagrammvisualisierungen verwendete Farbe Blau.",
		"Die in Diagrammvisualisierungen verwendete Farbe Gelb.",
		"Die in Diagrammvisualisierungen verwendete Farbe Orange.",
		"Die in Diagrammvisualisierungen verwendete Farbe Grün.",
		"Die in Diagrammvisualisierungen verwendete Farbe Violett.",
	],
	"vs/platform/theme/common/iconRegistry": [
		"Die ID der zu verwendenden Schriftart. Sofern nicht festgelegt, wird die zuerst definierte Schriftart verwendet.",
		"Das der Symboldefinition zugeordnete Schriftzeichen.",
		"Symbol für Aktion zum Schließen in Widgets",
		"Symbol für den Wechsel zur vorherigen Editor-Position.",
		"Symbol für den Wechsel zur nächsten Editor-Position.",
	],
	"vs/platform/undoRedo/common/undoRedoService": [
		"Die folgenden Dateien wurden geschlossen und auf dem Datenträger geändert: {0}.",
		"Die folgenden Dateien wurden auf inkompatible Weise geändert: {0}.",
		"\"{0}\" konnte nicht für alle Dateien rückgängig gemacht werden. {1}",
		"\"{0}\" konnte nicht für alle Dateien rückgängig gemacht werden. {1}",
		"\"{0}\" konnte nicht für alle Dateien rückgängig gemacht werden, da Änderungen an {1} vorgenommen wurden.",
		"\"{0}\" konnte nicht für alle Dateien rückgängig gemacht werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen für \"{1}\" durchgeführt wird.",
		"\"{0}\" konnte nicht für alle Dateien rückgängig gemacht werden, weil in der Zwischenzeit bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wurde.",
		"Möchten Sie \"{0}\" für alle Dateien rückgängig machen?",
		"&&In {0} Dateien rückgängig machen",
		"&&Datei rückgängig machen",
		"\"{0}\" konnte nicht rückgängig gemacht werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wird.",
		"Möchten Sie \"{0}\" rückgängig machen?",
		"&&Ja",
		"Nein",
		"\"{0}\" konnte nicht in allen Dateien wiederholt werden. {1}",
		"\"{0}\" konnte nicht in allen Dateien wiederholt werden. {1}",
		"\"{0}\" konnte nicht in allen Dateien wiederholt werden, da Änderungen an {1} vorgenommen wurden.",
		"\"{0}\" konnte nicht für alle Dateien wiederholt werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen für \"{1}\" durchgeführt wird.",
		"\"{0}\" konnte nicht für alle Dateien wiederholt werden, weil in der Zwischenzeit bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wurde.",
		"\"{0}\" konnte nicht wiederholt werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wird.",
	],
	"vs/platform/workspace/common/workspace": [
		"Codearbeitsbereich",
	]
});