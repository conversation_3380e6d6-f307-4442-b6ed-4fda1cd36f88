{"version": "4.16.2", "name": "prisma", "description": "Prisma is an open-source database toolkit. It includes a JavaScript/TypeScript ORM for Node.js, migrations and a modern GUI to view and edit the data in your database. You can use Prisma in new projects or add it to an existing one.", "keywords": ["orm", "prisma2", "prisma", "cli", "database", "sql", "postgresql", "mysql", "sqlite", "ma<PERSON>b", "mssql", "typescript", "query-builder"], "main": "build/index.js", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/cli"}, "homepage": "https://www.prisma.io", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "bugs": "https://github.com/prisma/prisma/issues", "license": "Apache-2.0", "engines": {"node": ">=14.17"}, "prisma": {"prismaCommit": "8089b6c29c73c1fd78abb6e814db2235a80333ea"}, "files": ["README.md", "build", "install", "runtime/*.js", "runtime/*.d.ts", "runtime/utils", "runtime/dist", "runtime/llhttp", "prisma-client", "preinstall", "scripts/preinstall-entry.js"], "pkg": {"assets": ["build/**/*", "runtime/**/*", "prisma-client/**/*", "node_modules/@prisma/engines/**/*", "node_modules/@prisma/engines/*"]}, "bin": {"prisma": "build/index.js", "prisma2": "build/index.js"}, "devDependencies": {"@prisma/studio": "0.484.0", "@prisma/studio-server": "0.484.0", "@swc/core": "1.3.64", "@swc/jest": "0.2.26", "@types/debug": "4.1.8", "@types/fs-extra": "9.0.13", "@types/jest": "29.5.2", "@types/rimraf": "3.0.2", "checkpoint-client": "1.1.24", "debug": "4.3.4", "dotenv": "16.0.3", "esbuild": "0.15.13", "execa": "5.1.1", "fast-deep-equal": "3.1.3", "fast-glob": "3.2.12", "fs-extra": "11.1.1", "fs-jetpack": "5.1.0", "get-port": "5.1.1", "global-dirs": "3.0.1", "is-installed-globally": "0.4.0", "jest": "29.5.0", "jest-junit": "16.0.0", "kleur": "4.1.5", "line-replace": "2.0.1", "log-update": "4.0.0", "node-fetch": "2.6.11", "npm-packlist": "5.1.3", "open": "7.4.2", "pkg-up": "3.1.0", "resolve-pkg": "2.0.0", "rimraf": "3.0.2", "strip-ansi": "6.0.1", "ts-pattern": "4.3.0", "typescript": "4.9.5", "@prisma/client": "4.16.2", "@prisma/debug": "4.16.2", "@prisma/fetch-engine": "4.16.2", "@prisma/generator-helper": "4.16.2", "@prisma/get-platform": "4.16.2", "@prisma/internals": "4.16.2", "@prisma/migrate": "4.16.2"}, "dependencies": {"@prisma/engines": "4.16.2"}, "sideEffects": false, "scripts": {"dev": "DEV=true node -r esbuild-register helpers/build.ts", "build": "node -r esbuild-register helpers/build.ts", "test": "jest --maxConcurrency=1 --silent", "tsc": "tsc -d -p tsconfig.build.json", "preinstall": "node scripts/preinstall-entry.js"}}