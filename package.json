{"name": "androidweb-enterprise", "version": "1.0.0", "description": "AI Mobile Developer Platform - Enterprise Edition", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "ai:start": "cd ai-engine && uvicorn main:app --reload --host 0.0.0.0 --port 8000", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@monaco-editor/react": "^4.6.0", "@prisma/client": "^4.16.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@stripe/stripe-js": "^2.2.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query": "^5.8.4", "@types/node": "^20.10.0", "@types/pg": "^8.15.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "framer-motion": "^10.16.16", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "monaco-editor": "^0.45.0", "next": "^14.0.4", "nodemailer": "^6.9.7", "pg": "^8.16.3", "postcss": "^8.4.32", "prisma": "^4.16.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.4.1", "react-resizable-panels": "^3.0.3", "recharts": "^2.8.0", "redis": "^4.6.11", "sharp": "^0.33.0", "socket.io-client": "^4.7.4", "stripe": "^14.7.0", "tailwind-merge": "^2.1.0", "tailwindcss": "^3.3.6", "tsx": "^4.20.3", "typescript": "^5.3.2", "zod": "^3.25.76", "zustand": "^4.4.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/nodemailer": "^6.4.14", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}