// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id                String   @id @default(cuid())
  email             String   @unique
  passwordHash      String   @map("password_hash")
  role              Role     @default(USER)
  subscriptionTier  SubscriptionTier @default(FREE) @map("subscription_tier")
  subscriptionStatus SubscriptionStatus @default(ACTIVE) @map("subscription_status")
  stripeCustomerId  String?  @map("stripe_customer_id")
  emailVerified     <PERSON>olean  @default(false) @map("email_verified")
  emailVerificationToken String? @map("email_verification_token")
  passwordResetToken String? @map("password_reset_token")
  passwordResetExpires DateTime? @map("password_reset_expires")
  lastLogin         DateTime? @map("last_login")
  loginAttempts     Int      @default(0) @map("login_attempts")
  lockoutUntil      DateTime? @map("lockout_until")
  twoFactorEnabled  <PERSON>olean  @default(false) @map("two_factor_enabled")
  twoFactorSecret   String?  @map("two_factor_secret")
  profile           Json     @default("{}")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  subscriptions     Subscription[]
  projects          Project[]
  conversations     Conversation[]
  usageTracking     UsageTracking[]
  paymentHistory    PaymentHistory[]
  sessions          Session[]
  auditLogs         AuditLog[]

  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  token     String   @unique
  expiresAt DateTime @map("expires_at")
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model Subscription {
  id                    String   @id @default(cuid())
  userId                String   @map("user_id")
  stripeSubscriptionId  String?  @unique @map("stripe_subscription_id")
  planId                String   @map("plan_id")
  status                SubscriptionStatus
  currentPeriodStart    DateTime? @map("current_period_start")
  currentPeriodEnd      DateTime? @map("current_period_end")
  cancelAtPeriodEnd     Boolean  @default(false) @map("cancel_at_period_end")
  trialStart            DateTime? @map("trial_start")
  trialEnd              DateTime? @map("trial_end")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model Project {
  id          String        @id @default(cuid())
  userId      String        @map("user_id")
  name        String
  description String?
  platform    Platform
  templateType String?      @map("template_type")
  status      ProjectStatus @default(ACTIVE)
  sourceCode  Json?         @map("source_code")
  buildConfig Json?         @map("build_config")
  metadata    Json          @default("{}")
  createdAt   DateTime      @default(now()) @map("created_at")
  updatedAt   DateTime      @updatedAt @map("updated_at")
  expiresAt   DateTime?     @map("expires_at")

  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  conversations Conversation[]

  @@map("projects")
}

model Conversation {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  projectId String   @map("project_id")
  title     String?
  messages  Json     @default("[]")
  aiContext Json     @default("{}") @map("ai_context")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  project         Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  aiLearningData  AiLearningData[]

  @@map("conversations")
}

model AiLearningData {
  id                  String   @id @default(cuid())
  conversationId      String   @map("conversation_id")
  userFeedback        Int?     @map("user_feedback")
  successMetrics      Json?    @map("success_metrics")
  codeQualityScore    Float?   @map("code_quality_score")
  userSatisfaction    Float?   @map("user_satisfaction")
  learningPatterns    Json?    @map("learning_patterns")
  processingTime      Int?     @map("processing_time")
  tokensUsed          Int?     @map("tokens_used")
  createdAt           DateTime @default(now()) @map("created_at")

  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@map("ai_learning_data")
}

model UsageTracking {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  actionType      String   @map("action_type")
  resourceConsumed Int     @default(1) @map("resource_consumed")
  metadata        Json     @default("{}")
  ipAddress       String?  @map("ip_address")
  userAgent       String?  @map("user_agent")
  createdAt       DateTime @default(now()) @map("created_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("usage_tracking")
}

model PaymentHistory {
  id                      String        @id @default(cuid())
  userId                  String        @map("user_id")
  stripePaymentIntentId   String?       @map("stripe_payment_intent_id")
  amount                  Int           // in cents
  currency                String        @default("USD")
  status                  PaymentStatus
  description             String?
  metadata                Json          @default("{}")
  createdAt               DateTime      @default(now()) @map("created_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("payment_history")
}

model SystemMetrics {
  id          String   @id @default(cuid())
  metricType  String   @map("metric_type")
  metricValue Float    @map("metric_value")
  metadata    Json     @default("{}")
  recordedAt  DateTime @default(now()) @map("recorded_at")

  @@map("system_metrics")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?  @map("user_id")
  action    String
  resource  String?
  details   Json     @default("{}")
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")
  createdAt DateTime @default(now()) @map("created_at")

  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("audit_logs")
}

// Enums
enum Role {
  USER
  ADMIN
  ENTERPRISE
  SUPER_ADMIN
}

enum SubscriptionTier {
  FREE
  PRO
  ENTERPRISE
  CUSTOM
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  CANCELLED
  PAST_DUE
  TRIALING
}

enum Platform {
  ANDROID
  IOS
  CROSS_PLATFORM
}

enum ProjectStatus {
  ACTIVE
  ARCHIVED
  DELETED
  BUILDING
  FAILED
}

enum PaymentStatus {
  PENDING
  SUCCEEDED
  FAILED
  CANCELLED
  REFUNDED
}
