#!/usr/bin/env node

/**
 * Database initialization script
 * Creates tables and initial data for AndroidWeb Enterprise
 */

const { Pool } = require('pg')
const bcrypt = require('bcryptjs')

// Database configuration
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'androidweb_enterprise',
  user: 'androidweb',
  password: 'androidweb123',
})

async function initializeDatabase() {
  const client = await pool.connect()
  
  try {
    console.log('🚀 Initializing AndroidWeb Enterprise database...')
    
    // Create tables
    console.log('📋 Creating tables...')
    
    // Users table
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(100),
        last_name VARCHAR(100),
        role VARCHAR(20) DEFAULT 'USER',
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    
    // Projects table
    await client.query(`
      CREATE TABLE IF NOT EXISTS projects (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        platform VARCHAR(20) NOT NULL,
        template_type VARCHAR(50),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        source_code JSONB,
        build_config JSONB,
        status VARCHAR(20) DEFAULT 'DRAFT',
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    
    // Conversations table
    await client.query(`
      CREATE TABLE IF NOT EXISTS conversations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
        messages JSONB DEFAULT '[]',
        ai_context JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    
    // Subscriptions table
    await client.query(`
      CREATE TABLE IF NOT EXISTS subscriptions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        plan_type VARCHAR(20) NOT NULL,
        status VARCHAR(20) DEFAULT 'ACTIVE',
        stripe_subscription_id VARCHAR(255),
        current_period_start TIMESTAMP,
        current_period_end TIMESTAMP,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    
    // AI Learning Data table
    await client.query(`
      CREATE TABLE IF NOT EXISTS ai_learning_data (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
        user_feedback INTEGER,
        success_metrics JSONB,
        code_quality_score DECIMAL(3,2),
        user_satisfaction DECIMAL(3,2),
        learning_patterns JSONB,
        processing_time INTEGER,
        tokens_used INTEGER,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `)
    
    // Usage Analytics table
    await client.query(`
      CREATE TABLE IF NOT EXISTS usage_analytics (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        action_type VARCHAR(50) NOT NULL,
        resource_type VARCHAR(50),
        resource_id UUID,
        metadata JSONB,
        ip_address INET,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `)
    
    // API Keys table
    await client.query(`
      CREATE TABLE IF NOT EXISTS api_keys (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        key_name VARCHAR(100) NOT NULL,
        key_hash VARCHAR(255) NOT NULL,
        permissions JSONB DEFAULT '[]',
        last_used_at TIMESTAMP,
        expires_at TIMESTAMP,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `)
    
    console.log('✅ Tables created successfully')
    
    // Create indexes for performance
    console.log('📊 Creating indexes...')
    
    await client.query('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')
    await client.query('CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)')
    await client.query('CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status)')
    await client.query('CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id)')
    await client.query('CREATE INDEX IF NOT EXISTS idx_conversations_project_id ON conversations(project_id)')
    await client.query('CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id)')
    await client.query('CREATE INDEX IF NOT EXISTS idx_usage_analytics_user_id ON usage_analytics(user_id)')
    await client.query('CREATE INDEX IF NOT EXISTS idx_usage_analytics_created_at ON usage_analytics(created_at)')
    
    console.log('✅ Indexes created successfully')
    
    // Create initial admin user
    console.log('👤 Creating initial admin user...')
    
    const adminPassword = await bcrypt.hash('admin123!', 12)
    
    await client.query(`
      INSERT INTO users (email, password, first_name, last_name, role, email_verified)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (email) DO NOTHING
    `, [
      '<EMAIL>',
      adminPassword,
      'Admin',
      'User',
      'SUPER_ADMIN',
      true
    ])
    
    // Create demo user
    const demoPassword = await bcrypt.hash('demo123!', 12)
    
    await client.query(`
      INSERT INTO users (email, password, first_name, last_name, role, email_verified)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (email) DO NOTHING
    `, [
      '<EMAIL>',
      demoPassword,
      'Demo',
      'User',
      'USER',
      true
    ])
    
    console.log('✅ Initial users created successfully')
    
    // Create demo project
    console.log('📱 Creating demo project...')
    
    const demoUser = await client.query('SELECT id FROM users WHERE email = $1', ['<EMAIL>'])
    
    if (demoUser.rows.length > 0) {
      const userId = demoUser.rows[0].id
      
      await client.query(`
        INSERT INTO projects (name, description, platform, template_type, user_id, source_code, build_config, status)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT DO NOTHING
      `, [
        'Demo Todo App',
        'A sample todo application created with AndroidWeb Enterprise',
        'ANDROID',
        'todo-app',
        userId,
        JSON.stringify({
          'MainActivity.kt': 'class MainActivity : AppCompatActivity() { /* Demo code */ }',
          'build.gradle': 'android { compileSdk 34 }'
        }),
        JSON.stringify({
          compileSdk: 34,
          minSdk: 24,
          targetSdk: 34
        }),
        'COMPLETED'
      ])
      
      console.log('✅ Demo project created successfully')
    }
    
    // Create demo subscription
    console.log('💳 Creating demo subscription...')
    
    if (demoUser.rows.length > 0) {
      const userId = demoUser.rows[0].id
      
      await client.query(`
        INSERT INTO subscriptions (user_id, plan_type, status, current_period_start, current_period_end)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT DO NOTHING
      `, [
        userId,
        'PRO',
        'ACTIVE',
        new Date(),
        new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      ])
      
      console.log('✅ Demo subscription created successfully')
    }
    
    console.log('🎉 Database initialization completed successfully!')
    console.log('')
    console.log('📋 Initial accounts created:')
    console.log('   Admin: <EMAIL> / admin123!')
    console.log('   Demo:  <EMAIL> / demo123!')
    console.log('')
    console.log('🚀 You can now start the application!')
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error)
    throw error
  } finally {
    client.release()
    await pool.end()
  }
}

// Run initialization
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('✅ Database initialization script completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Database initialization script failed:', error)
      process.exit(1)
    })
}

module.exports = { initializeDatabase }
