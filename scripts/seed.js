const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123!@#', 12)
  const demoPassword = await bcrypt.hash('demo123!', 12)

  // Create or update admin user
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      name: 'Administrator',
      role: 'ADMIN',
      isEmailVerified: true,
      totalAppsGenerated: 0,
    },
  })

  // Create or update demo user
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: demoPassword,
      name: 'Demo User',
      role: 'USER',
      isEmailVerified: true,
      totalAppsGenerated: 5,
    },
  })

  // Create subscription plans
  const freePlan = await prisma.subscriptionPlan.upsert({
    where: { name: 'FREE' },
    update: {},
    create: {
      name: 'FREE',
      tier: 'FREE',
      price: 0,
      maxAppsPerMonth: 5,
      maxStorageGB: 1,
      features: ['Basic app generation', 'Community support', '5 apps/month'],
      isActive: true,
    },
  })

  const proPlan = await prisma.subscriptionPlan.upsert({
    where: { name: 'PRO' },
    update: {},
    create: {
      name: 'PRO',
      tier: 'PRO',
      price: 29.99,
      maxAppsPerMonth: 50,
      maxStorageGB: 10,
      features: [
        'Advanced app generation',
        'Priority support',
        '50 apps/month',
        'Custom templates',
        'API access'
      ],
      isActive: true,
    },
  })

  const enterprisePlan = await prisma.subscriptionPlan.upsert({
    where: { name: 'ENTERPRISE' },
    update: {},
    create: {
      name: 'ENTERPRISE',
      tier: 'ENTERPRISE',
      price: 99.99,
      maxAppsPerMonth: 200,
      maxStorageGB: 100,
      features: [
        'Unlimited app generation',
        'Dedicated support',
        '200 apps/month',
        'Custom AI models',
        'White-label solution',
        'Advanced analytics'
      ],
      isActive: true,
    },
  })

  // Create subscriptions for users
  await prisma.subscription.upsert({
    where: { userId: admin.id },
    update: {},
    create: {
      userId: admin.id,
      planId: enterprisePlan.id,
      status: 'ACTIVE',
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
    },
  })

  await prisma.subscription.upsert({
    where: { userId: demoUser.id },
    update: {},
    create: {
      userId: demoUser.id,
      planId: proPlan.id,
      status: 'ACTIVE',
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    },
  })

  // Create sample projects for demo user
  const sampleProjects = [
    {
      name: 'E-Commerce Mobile App',
      description: 'Complete shopping app with cart, payments, and user accounts',
      platform: 'ANDROID',
      features: ['Product Catalog', 'Shopping Cart', 'Payment Integration', 'User Authentication'],
      status: 'COMPLETED',
      templateType: 'ecommerce',
    },
    {
      name: 'Fitness Tracker',
      description: 'Health and fitness app with workout tracking and analytics',
      platform: 'CROSS_PLATFORM',
      features: ['Workout Tracking', 'Progress Analytics', 'Goal Setting', 'Health Metrics'],
      status: 'COMPLETED',
      templateType: 'fitness',
    },
    {
      name: 'Task Manager Pro',
      description: 'Advanced productivity app for managing tasks and projects',
      platform: 'IOS',
      features: ['Task Lists', 'Project Organization', 'Team Collaboration', 'Time Tracking'],
      status: 'GENERATING',
      templateType: 'todo',
    },
  ]

  for (const projectData of sampleProjects) {
    await prisma.project.create({
      data: {
        ...projectData,
        userId: demoUser.id,
        generatedCode: {
          'MainActivity.kt': 'Sample Android code...',
          'build.gradle': 'Sample build configuration...',
        },
        buildConfig: {
          compileSdk: 34,
          minSdk: 24,
          targetSdk: 34,
        },
        documentation: `# ${projectData.name}\n\nThis is a sample project generated by AndroidWeb Enterprise.`,
      },
    })
  }

  // Create sample AI interactions
  await prisma.aiInteraction.createMany({
    data: [
      {
        userId: demoUser.id,
        type: 'APP_GENERATION',
        prompt: 'Generate an e-commerce mobile app',
        response: 'Successfully generated e-commerce app with all requested features',
        tokensUsed: 1500,
        model: 'qwen2.5:0.5b',
        success: true,
      },
      {
        userId: demoUser.id,
        type: 'CHAT',
        prompt: 'How do I add payment integration to my app?',
        response: 'To add payment integration, you can use Stripe SDK...',
        tokensUsed: 800,
        model: 'qwen2.5:0.5b',
        success: true,
      },
      {
        userId: admin.id,
        type: 'APP_GENERATION',
        prompt: 'Generate a fitness tracking app',
        response: 'Successfully generated fitness app with health metrics',
        tokensUsed: 2000,
        model: 'qwen2.5:0.5b',
        success: true,
      },
    ],
  })

  console.log('✅ Database seeded successfully!')
  console.log('\n📋 Created accounts:')
  console.log('👑 Admin: <EMAIL> / admin123!@#')
  console.log('👤 Demo:  <EMAIL> / demo123!')
  console.log('\n💳 Subscription plans created: FREE, PRO, ENTERPRISE')
  console.log('📱 Sample projects created for demo user')
  console.log('🤖 Sample AI interactions logged')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
