#!/bin/bash

# AndroidWeb Enterprise - AI Models Setup Script
# This script manages the download and setup of AI models for the platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MODELS_DIR="./data/models"
LOG_FILE="./data/model-setup.log"

# Create necessary directories
mkdir -p ./data
mkdir -p $MODELS_DIR

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a $LOG_FILE
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a $LOG_FILE
}

# Check if Ollama is installed and running
check_ollama() {
    log "Checking Ollama installation..."
    
    if ! command -v ollama &> /dev/null; then
        error "Ollama is not installed. Please install it first:"
        echo "curl -fsSL https://ollama.ai/install.sh | sh"
        exit 1
    fi
    
    # Check if Ollama service is running
    if ! pgrep -x "ollama" > /dev/null; then
        log "Starting Ollama service..."
        ollama serve &
        sleep 5
    fi
    
    # Test Ollama connection
    if curl -s http://localhost:11434/api/tags > /dev/null; then
        success "Ollama is running and accessible"
    else
        error "Ollama service is not responding"
        exit 1
    fi
}

# Function to download a model with retry logic
download_model() {
    local model_name=$1
    local max_retries=3
    local retry_count=0
    
    log "Downloading model: $model_name"
    
    while [ $retry_count -lt $max_retries ]; do
        if ollama pull $model_name; then
            success "Successfully downloaded $model_name"
            return 0
        else
            retry_count=$((retry_count + 1))
            warning "Download failed for $model_name (attempt $retry_count/$max_retries)"
            
            if [ $retry_count -lt $max_retries ]; then
                log "Retrying in 10 seconds..."
                sleep 10
            fi
        fi
    done
    
    error "Failed to download $model_name after $max_retries attempts"
    return 1
}

# Function to check if a model exists
model_exists() {
    local model_name=$1
    ollama list | grep -q "$model_name"
}

# Function to get model size
get_model_info() {
    local model_name=$1
    if model_exists "$model_name"; then
        local info=$(ollama list | grep "$model_name")
        echo "$info"
    else
        echo "Model not found"
    fi
}

# Main setup function
setup_models() {
    log "Starting AI models setup for AndroidWeb Enterprise..."
    
    # Define models to download (in order of priority)
    declare -a models=(
        "llama3.2:1b"           # Small, fast model for basic tasks
        "qwen2.5:7b"            # Main coding model
        "qwen2.5-coder:7b"      # Specialized coding model
    )
    
    # Check available disk space
    available_space=$(df . | tail -1 | awk '{print $4}')
    required_space=10485760  # 10GB in KB
    
    if [ $available_space -lt $required_space ]; then
        warning "Low disk space detected. Some models may not be downloaded."
    fi
    
    # Download models
    for model in "${models[@]}"; do
        log "Processing model: $model"
        
        if model_exists "$model"; then
            success "Model $model already exists"
            get_model_info "$model"
        else
            log "Model $model not found, downloading..."
            if download_model "$model"; then
                success "Model $model ready for use"
            else
                warning "Skipping $model due to download failure"
            fi
        fi
        
        echo "---"
    done
}

# Function to test models
test_models() {
    log "Testing downloaded models..."
    
    declare -a test_models=(
        "llama3.2:1b"
        "qwen2.5:7b"
        "qwen2.5-coder:7b"
    )
    
    for model in "${test_models[@]}"; do
        if model_exists "$model"; then
            log "Testing $model..."
            
            # Simple test prompt
            test_response=$(echo "Hello, respond with 'OK'" | ollama run "$model" --timeout 30s 2>/dev/null || echo "TIMEOUT")
            
            if [[ "$test_response" == *"OK"* ]]; then
                success "Model $model is working correctly"
            else
                warning "Model $model may have issues: $test_response"
            fi
        fi
    done
}

# Function to show model status
show_status() {
    log "Current AI models status:"
    echo ""
    
    if command -v ollama &> /dev/null; then
        echo "📋 Installed Models:"
        ollama list
        echo ""
        
        echo "💾 Storage Usage:"
        du -sh ~/.ollama/models 2>/dev/null || echo "No models directory found"
        echo ""
        
        echo "🔧 Ollama Service Status:"
        if pgrep -x "ollama" > /dev/null; then
            echo "✅ Ollama service is running"
        else
            echo "❌ Ollama service is not running"
        fi
    else
        error "Ollama is not installed"
    fi
}

# Function to cleanup failed downloads
cleanup() {
    log "Cleaning up incomplete downloads..."
    # Add cleanup logic here if needed
    success "Cleanup completed"
}

# Main script logic
case "${1:-setup}" in
    "setup")
        check_ollama
        setup_models
        test_models
        show_status
        ;;
    "status")
        show_status
        ;;
    "test")
        test_models
        ;;
    "cleanup")
        cleanup
        ;;
    "retry")
        log "Retrying failed model downloads..."
        setup_models
        ;;
    *)
        echo "Usage: $0 {setup|status|test|cleanup|retry}"
        echo ""
        echo "Commands:"
        echo "  setup   - Download and setup AI models (default)"
        echo "  status  - Show current models status"
        echo "  test    - Test downloaded models"
        echo "  cleanup - Clean up failed downloads"
        echo "  retry   - Retry failed downloads"
        exit 1
        ;;
esac

success "AI models setup script completed!"
