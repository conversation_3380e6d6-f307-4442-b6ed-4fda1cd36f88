#!/bin/bash

# AndroidWeb Enterprise - Ollama Setup Script
# This script installs and configures Ollama with required models

set -e

echo "🚀 AndroidWeb Enterprise - Ollama Setup"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on supported OS
check_os() {
    print_status "Checking operating system..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        print_success "Linux detected"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        print_success "macOS detected"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check available memory
    if [[ "$OS" == "linux" ]]; then
        MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    elif [[ "$OS" == "macos" ]]; then
        MEMORY_BYTES=$(sysctl -n hw.memsize)
        MEMORY_GB=$((MEMORY_BYTES / 1024 / 1024 / 1024))
    fi
    
    print_status "Available memory: ${MEMORY_GB}GB"
    
    if [ "$MEMORY_GB" -lt 8 ]; then
        print_warning "Recommended minimum memory is 8GB. You have ${MEMORY_GB}GB."
        print_warning "Some models may not run optimally."
    else
        print_success "Memory requirement satisfied"
    fi
    
    # Check disk space
    DISK_SPACE=$(df -h . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$DISK_SPACE" -lt 20 ]; then
        print_warning "Recommended minimum disk space is 20GB. Available: ${DISK_SPACE}GB"
    else
        print_success "Disk space requirement satisfied"
    fi
}

# Install Ollama
install_ollama() {
    print_status "Installing Ollama..."
    
    if command -v ollama &> /dev/null; then
        print_success "Ollama is already installed"
        ollama --version
        return 0
    fi
    
    print_status "Downloading and installing Ollama..."
    curl -fsSL https://ollama.ai/install.sh | sh
    
    if command -v ollama &> /dev/null; then
        print_success "Ollama installed successfully"
        ollama --version
    else
        print_error "Failed to install Ollama"
        exit 1
    fi
}

# Start Ollama service
start_ollama() {
    print_status "Starting Ollama service..."
    
    # Check if Ollama is already running
    if pgrep -x "ollama" > /dev/null; then
        print_success "Ollama is already running"
        return 0
    fi
    
    # Start Ollama in background
    if [[ "$OS" == "linux" ]]; then
        # On Linux, start as systemd service if available
        if systemctl is-enabled ollama &> /dev/null; then
            sudo systemctl start ollama
            sudo systemctl enable ollama
            print_success "Ollama service started and enabled"
        else
            # Start manually
            nohup ollama serve > /dev/null 2>&1 &
            print_success "Ollama started manually"
        fi
    elif [[ "$OS" == "macos" ]]; then
        # On macOS, start manually
        nohup ollama serve > /dev/null 2>&1 &
        print_success "Ollama started"
    fi
    
    # Wait for Ollama to be ready
    print_status "Waiting for Ollama to be ready..."
    for i in {1..30}; do
        if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
            print_success "Ollama is ready"
            return 0
        fi
        sleep 2
    done
    
    print_error "Ollama failed to start or is not responding"
    exit 1
}

# Pull required models
pull_models() {
    print_status "Pulling required AI models..."
    
    # Define models to pull
    declare -a models=(
        "mistral:7b"
        "codellama:13b"
        "llama2:13b"
    )
    
    for model in "${models[@]}"; do
        print_status "Pulling model: $model"
        
        if ollama pull "$model"; then
            print_success "Successfully pulled $model"
        else
            print_error "Failed to pull $model"
            print_warning "You can try pulling this model later with: ollama pull $model"
        fi
    done
}

# Create custom mobile development model
create_mobile_dev_model() {
    print_status "Creating custom mobile development model..."
    
    # Create Modelfile for mobile development specialist
    cat > /tmp/mobile-dev.modelfile << 'EOF'
FROM mistral:7b

SYSTEM """You are a mobile development specialist AI assistant for AndroidWeb Enterprise. 
You have extensive knowledge in:
- Android development with Kotlin and Java
- iOS development with Swift and Objective-C
- Cross-platform development with React Native and Flutter
- Mobile UI/UX design principles
- Mobile app architecture patterns (MVVM, MVP, Clean Architecture)
- Mobile-specific APIs and frameworks
- App store guidelines and best practices
- Performance optimization for mobile devices
- Mobile security best practices

Always provide practical, actionable advice with code examples when relevant.
Focus on modern development practices and current industry standards.
Be concise but thorough in explanations."""

PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER top_k 40
EOF

    # Create the custom model
    if ollama create mobile-dev-specialist -f /tmp/mobile-dev.modelfile; then
        print_success "Custom mobile development model created: mobile-dev-specialist"
    else
        print_error "Failed to create custom mobile development model"
        print_warning "You can create it later using the Modelfile in /tmp/mobile-dev.modelfile"
    fi
    
    # Clean up
    rm -f /tmp/mobile-dev.modelfile
}

# Test Ollama installation
test_ollama() {
    print_status "Testing Ollama installation..."
    
    # Test basic functionality
    if echo "Hello, can you help me with mobile development?" | ollama run mistral:7b > /dev/null 2>&1; then
        print_success "Ollama is working correctly"
    else
        print_error "Ollama test failed"
        exit 1
    fi
    
    # List available models
    print_status "Available models:"
    ollama list
}

# Create Ollama configuration
create_config() {
    print_status "Creating Ollama configuration..."
    
    # Create config directory if it doesn't exist
    CONFIG_DIR="$HOME/.ollama"
    mkdir -p "$CONFIG_DIR"
    
    # Create environment configuration
    cat > "$CONFIG_DIR/config.env" << EOF
# Ollama Configuration for AndroidWeb Enterprise
OLLAMA_HOST=0.0.0.0
OLLAMA_PORT=11434
OLLAMA_MODELS_PATH=$CONFIG_DIR/models
OLLAMA_KEEP_ALIVE=5m
OLLAMA_MAX_LOADED_MODELS=3
EOF
    
    print_success "Configuration created at $CONFIG_DIR/config.env"
}

# Main installation process
main() {
    echo
    print_status "Starting Ollama setup for AndroidWeb Enterprise..."
    echo
    
    check_os
    check_requirements
    install_ollama
    start_ollama
    create_config
    pull_models
    create_mobile_dev_model
    test_ollama
    
    echo
    print_success "🎉 Ollama setup completed successfully!"
    echo
    print_status "Next steps:"
    echo "1. The AI engine will automatically connect to Ollama"
    echo "2. Start the AI engine with: cd ai-engine && python main.py"
    echo "3. Ollama is running on http://localhost:11434"
    echo
    print_status "Available models:"
    ollama list
    echo
    print_status "To manage Ollama:"
    echo "- View models: ollama list"
    echo "- Pull new model: ollama pull <model-name>"
    echo "- Run model: ollama run <model-name>"
    echo "- Stop Ollama: pkill ollama"
    echo
}

# Run main function
main "$@"
