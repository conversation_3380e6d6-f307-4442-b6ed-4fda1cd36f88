#!/bin/bash

# AndroidWeb Enterprise - Complete System Startup Script
# This script starts all components of the AndroidWeb Enterprise platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running from correct directory
check_directory() {
    if [ ! -f "package.json" ] || [ ! -d "ai-engine" ]; then
        print_error "Please run this script from the AndroidWeb project root directory"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.8+ first."
        exit 1
    fi
    
    # Check PostgreSQL
    if ! command -v psql &> /dev/null; then
        print_warning "PostgreSQL client not found. Make sure PostgreSQL is installed and running."
    fi
    
    # Check Docker (optional)
    if command -v docker &> /dev/null; then
        print_success "Docker found - can use containerized services"
    else
        print_warning "Docker not found - will use local services"
    fi
    
    print_success "System requirements check completed"
}

# Setup environment variables
setup_environment() {
    print_status "Setting up environment variables..."
    
    if [ ! -f ".env" ]; then
        print_status "Creating .env file from template..."
        cp .env.example .env
        print_warning "Please update .env file with your configuration before continuing"
        print_warning "Press Enter to continue after updating .env file..."
        read
    fi
    
    # Load environment variables
    export $(cat .env | grep -v '^#' | xargs)
    
    print_success "Environment variables loaded"
}

# Start PostgreSQL database
start_database() {
    print_status "Starting PostgreSQL database..."
    
    # Check if PostgreSQL is running
    if pg_isready -h localhost -p 5432 &> /dev/null; then
        print_success "PostgreSQL is already running"
    else
        print_status "Starting PostgreSQL service..."
        
        # Try different methods to start PostgreSQL
        if command -v systemctl &> /dev/null; then
            sudo systemctl start postgresql
        elif command -v brew &> /dev/null; then
            brew services start postgresql
        elif command -v docker &> /dev/null; then
            print_status "Starting PostgreSQL with Docker..."
            docker run -d \
                --name androidweb-postgres \
                -e POSTGRES_DB=androidweb \
                -e POSTGRES_USER=androidweb \
                -e POSTGRES_PASSWORD=androidweb123 \
                -p 5432:5432 \
                postgres:15
        else
            print_error "Could not start PostgreSQL. Please start it manually."
            exit 1
        fi
        
        # Wait for PostgreSQL to be ready
        print_status "Waiting for PostgreSQL to be ready..."
        for i in {1..30}; do
            if pg_isready -h localhost -p 5432 &> /dev/null; then
                break
            fi
            sleep 2
        done
        
        if pg_isready -h localhost -p 5432 &> /dev/null; then
            print_success "PostgreSQL is ready"
        else
            print_error "PostgreSQL failed to start"
            exit 1
        fi
    fi
}

# Setup database schema
setup_database() {
    print_status "Setting up database schema..."
    
    # Run Prisma migrations
    print_status "Running Prisma migrations..."
    npx prisma migrate dev --name init
    
    # Generate Prisma client
    print_status "Generating Prisma client..."
    npx prisma generate
    
    print_success "Database schema setup completed"
}

# Start Redis (optional)
start_redis() {
    print_status "Starting Redis..."
    
    if command -v redis-server &> /dev/null; then
        if pgrep redis-server > /dev/null; then
            print_success "Redis is already running"
        else
            redis-server --daemonize yes
            print_success "Redis started"
        fi
    elif command -v docker &> /dev/null; then
        if ! docker ps | grep androidweb-redis > /dev/null; then
            print_status "Starting Redis with Docker..."
            docker run -d \
                --name androidweb-redis \
                -p 6379:6379 \
                redis:7-alpine
            print_success "Redis started with Docker"
        else
            print_success "Redis container is already running"
        fi
    else
        print_warning "Redis not found. Some features may not work optimally."
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing Node.js dependencies..."
    npm install
    
    print_status "Installing Python dependencies..."
    cd ai-engine
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    source venv/bin/activate
    pip install -r requirements.txt
    cd ..
    
    print_success "Dependencies installed"
}

# Start Ollama
start_ollama() {
    print_status "Starting Ollama AI service..."
    
    if command -v ollama &> /dev/null; then
        if pgrep ollama > /dev/null; then
            print_success "Ollama is already running"
        else
            print_status "Starting Ollama service..."
            nohup ollama serve > /dev/null 2>&1 &
            
            # Wait for Ollama to be ready
            print_status "Waiting for Ollama to be ready..."
            for i in {1..30}; do
                if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
                    break
                fi
                sleep 2
            done
            
            if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
                print_success "Ollama is ready"
            else
                print_error "Ollama failed to start"
                exit 1
            fi
        fi
    else
        print_warning "Ollama not found. Please install Ollama first:"
        print_warning "Run: ./scripts/setup-ollama.sh"
        exit 1
    fi
}

# Start AI Engine
start_ai_engine() {
    print_status "Starting AI Engine..."
    
    cd ai-engine
    source venv/bin/activate
    
    # Start AI Engine in background
    nohup python main.py > ../logs/ai-engine.log 2>&1 &
    AI_ENGINE_PID=$!
    echo $AI_ENGINE_PID > ../logs/ai-engine.pid
    
    cd ..
    
    # Wait for AI Engine to be ready
    print_status "Waiting for AI Engine to be ready..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/health > /dev/null 2>&1; then
            break
        fi
        sleep 2
    done
    
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        print_success "AI Engine is ready"
    else
        print_error "AI Engine failed to start"
        exit 1
    fi
}

# Start Next.js frontend
start_frontend() {
    print_status "Starting Next.js frontend..."
    
    # Start Next.js in development mode
    nohup npm run dev > logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > logs/frontend.pid
    
    # Wait for frontend to be ready
    print_status "Waiting for frontend to be ready..."
    for i in {1..60}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            break
        fi
        sleep 2
    done
    
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        print_success "Frontend is ready"
    else
        print_error "Frontend failed to start"
        exit 1
    fi
}

# Create logs directory
setup_logs() {
    mkdir -p logs
    touch logs/ai-engine.log
    touch logs/frontend.log
    touch logs/system.log
}

# Display system status
show_status() {
    print_header "🚀 AndroidWeb Enterprise - System Status"
    echo
    
    # Check each service
    echo -e "${CYAN}Service Status:${NC}"
    
    # PostgreSQL
    if pg_isready -h localhost -p 5432 &> /dev/null; then
        echo -e "  ✅ PostgreSQL: ${GREEN}Running${NC} (localhost:5432)"
    else
        echo -e "  ❌ PostgreSQL: ${RED}Not Running${NC}"
    fi
    
    # Redis
    if command -v redis-cli &> /dev/null && redis-cli ping &> /dev/null; then
        echo -e "  ✅ Redis: ${GREEN}Running${NC} (localhost:6379)"
    elif docker ps | grep androidweb-redis > /dev/null; then
        echo -e "  ✅ Redis: ${GREEN}Running${NC} (Docker)"
    else
        echo -e "  ⚠️  Redis: ${YELLOW}Not Running${NC}"
    fi
    
    # Ollama
    if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
        echo -e "  ✅ Ollama: ${GREEN}Running${NC} (localhost:11434)"
    else
        echo -e "  ❌ Ollama: ${RED}Not Running${NC}"
    fi
    
    # AI Engine
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo -e "  ✅ AI Engine: ${GREEN}Running${NC} (localhost:8000)"
    else
        echo -e "  ❌ AI Engine: ${RED}Not Running${NC}"
    fi
    
    # Frontend
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo -e "  ✅ Frontend: ${GREEN}Running${NC} (localhost:3000)"
    else
        echo -e "  ❌ Frontend: ${RED}Not Running${NC}"
    fi
    
    echo
    echo -e "${CYAN}Access URLs:${NC}"
    echo -e "  🌐 Main Application: ${BLUE}http://localhost:3000${NC}"
    echo -e "  🤖 AI Engine API: ${BLUE}http://localhost:8000${NC}"
    echo -e "  📊 Admin Dashboard: ${BLUE}http://localhost:3000/dashboard${NC}"
    echo -e "  🎨 4-Zone Workspace: ${BLUE}http://localhost:3000/workspace${NC}"
    echo -e "  📱 Templates: ${BLUE}http://localhost:3000/templates${NC}"
    
    echo
    echo -e "${CYAN}Logs:${NC}"
    echo -e "  📄 AI Engine: ${BLUE}tail -f logs/ai-engine.log${NC}"
    echo -e "  📄 Frontend: ${BLUE}tail -f logs/frontend.log${NC}"
    
    echo
    echo -e "${GREEN}🎉 AndroidWeb Enterprise is ready!${NC}"
    echo -e "${YELLOW}Press Ctrl+C to stop all services${NC}"
}

# Cleanup function
cleanup() {
    print_header "🛑 Stopping AndroidWeb Enterprise"
    
    # Stop frontend
    if [ -f "logs/frontend.pid" ]; then
        FRONTEND_PID=$(cat logs/frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            print_status "Stopping frontend..."
            kill $FRONTEND_PID
            rm logs/frontend.pid
        fi
    fi
    
    # Stop AI Engine
    if [ -f "logs/ai-engine.pid" ]; then
        AI_ENGINE_PID=$(cat logs/ai-engine.pid)
        if kill -0 $AI_ENGINE_PID 2>/dev/null; then
            print_status "Stopping AI Engine..."
            kill $AI_ENGINE_PID
            rm logs/ai-engine.pid
        fi
    fi
    
    print_success "All services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    print_header "🚀 AndroidWeb Enterprise - System Startup"
    
    check_directory
    check_requirements
    setup_environment
    setup_logs
    
    start_database
    setup_database
    start_redis
    install_dependencies
    start_ollama
    start_ai_engine
    start_frontend
    
    show_status
    
    # Keep script running
    while true; do
        sleep 10
    done
}

# Run main function
main "$@"
