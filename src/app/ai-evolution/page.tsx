'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Brain,
  TrendingUp,
  Activity,
  Zap,
  Code,
  Target,
  Clock,
  CheckCircle,
  AlertTriangle,
  Cpu,
  Smartphone
} from 'lucide-react'

interface LearningEvent {
  id: string
  timestamp: Date
  type: 'APP_CREATED' | 'PATTERN_LEARNED' | 'OPTIMIZATION' | 'ERROR_CORRECTED'
  description: string
  impact: number
  data: any
}

interface AIMetrics {
  totalAppsCreated: number
  patternsLearned: number
  successRate: number
  averageGenerationTime: number
  knowledgeBase: number
  currentLearning: string[]
}

export default function AIEvolutionPage() {
  const [learningEvents, setLearningEvents] = useState<LearningEvent[]>([])
  const [metrics, setMetrics] = useState<AIMetrics>({
    totalAppsCreated: 0,
    patternsLearned: 0,
    successRate: 0,
    averageGenerationTime: 0,
    knowledgeBase: 0,
    currentLearning: []
  })
  const [isLearning, setIsLearning] = useState(false)
  const [createdApps, setCreatedApps] = useState<any[]>([])
  const [learningProgress, setLearningProgress] = useState<any[]>([])

  useEffect(() => {
    // Simulare învățare continuă în timp real
    const interval = setInterval(() => {
      simulateAILearning()
    }, 3000)

    loadAIMetrics()
    loadCreatedApps()

    return () => clearInterval(interval)
  }, [])

  const loadCreatedApps = async () => {
    try {
      const token = localStorage.getItem('accessToken')
      if (!token) return

      const response = await fetch('/api/projects', {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        const data = await response.json()
        setCreatedApps(data.projects || [])
      }
    } catch (error) {
      console.error('Error loading apps:', error)
    }
  }

  const simulateAILearning = () => {
    const learningTypes = [
      {
        type: 'PATTERN_LEARNED' as const,
        descriptions: [
          'Învățat pattern nou pentru autentificare biometrică',
          'Optimizat structura pentru aplicații e-commerce',
          'Descoperit pattern eficient pentru UI responsive',
          'Învățat integrare avansată pentru API-uri REST'
        ]
      },
      {
        type: 'OPTIMIZATION' as const,
        descriptions: [
          'Optimizat timpul de generare cu 15%',
          'Îmbunătățit calitatea codului generat',
          'Redus numărul de erori cu 23%',
          'Optimizat arhitectura pentru performanță'
        ]
      },
      {
        type: 'APP_CREATED' as const,
        descriptions: [
          'Aplicație Android creată cu succes',
          'Aplicație iOS generată și testată',
          'App cross-platform finalizată',
          'Aplicație enterprise completată'
        ]
      }
    ]

    const randomType = learningTypes[Math.floor(Math.random() * learningTypes.length)]
    const randomDesc = randomType.descriptions[Math.floor(Math.random() * randomType.descriptions.length)]

    const newEvent: LearningEvent = {
      id: `event_${Date.now()}`,
      timestamp: new Date(),
      type: randomType.type,
      description: randomDesc,
      impact: Math.floor(Math.random() * 100) + 1,
      data: {
        model: 'qwen2.5:0.5b',
        confidence: Math.random() * 0.3 + 0.7,
        processingTime: Math.random() * 2000 + 500
      }
    }

    setLearningEvents(prev => [newEvent, ...prev.slice(0, 19)])
    
    // Update metrics
    setMetrics(prev => ({
      ...prev,
      totalAppsCreated: prev.totalAppsCreated + (randomType.type === 'APP_CREATED' ? 1 : 0),
      patternsLearned: prev.patternsLearned + (randomType.type === 'PATTERN_LEARNED' ? 1 : 0),
      successRate: Math.min(99.9, prev.successRate + Math.random() * 0.1),
      knowledgeBase: prev.knowledgeBase + Math.random() * 10,
      currentLearning: [
        'Analizează pattern-uri noi din aplicațiile create',
        'Optimizează algoritmi de generare cod',
        'Învață din feedback-ul utilizatorilor',
        'Îmbunătățește arhitectura aplicațiilor'
      ]
    }))

    setIsLearning(true)
    setTimeout(() => setIsLearning(false), 1000)
  }

  const loadAIMetrics = async () => {
    // Simulare încărcare metrici inițiale
    setMetrics({
      totalAppsCreated: 1247,
      patternsLearned: 3891,
      successRate: 94.7,
      averageGenerationTime: 4.2,
      knowledgeBase: 15847,
      currentLearning: [
        'Analizează pattern-uri noi din aplicațiile create',
        'Optimizează algoritmi de generare cod',
        'Învață din feedback-ul utilizatorilor',
        'Îmbunătățește arhitectura aplicațiilor'
      ]
    })
  }

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'APP_CREATED': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'PATTERN_LEARNED': return <Brain className="h-4 w-4 text-blue-500" />
      case 'OPTIMIZATION': return <TrendingUp className="h-4 w-4 text-purple-500" />
      case 'ERROR_CORRECTED': return <AlertTriangle className="h-4 w-4 text-orange-500" />
      default: return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getEventColor = (type: string) => {
    switch (type) {
      case 'APP_CREATED': return 'bg-green-100 text-green-800'
      case 'PATTERN_LEARNED': return 'bg-blue-100 text-blue-800'
      case 'OPTIMIZATION': return 'bg-purple-100 text-purple-800'
      case 'ERROR_CORRECTED': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Brain className="h-8 w-8 text-purple-600" />
            <h1 className="text-3xl font-bold text-gray-900">AI Evolution Dashboard</h1>
            {isLearning && (
              <Badge className="bg-green-100 text-green-800 animate-pulse">
                <Activity className="h-3 w-3 mr-1" />
                Learning...
              </Badge>
            )}
          </div>
          <p className="text-gray-600">
            Monitorizează în timp real cum AI-ul învață și evoluează din fiecare aplicație creată
          </p>
        </div>

        {/* Metrici principale */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Apps Created</p>
                  <p className="text-2xl font-bold text-gray-900">{metrics.totalAppsCreated.toLocaleString()}</p>
                </div>
                <Code className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Patterns Learned</p>
                  <p className="text-2xl font-bold text-gray-900">{metrics.patternsLearned.toLocaleString()}</p>
                </div>
                <Brain className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Success Rate</p>
                  <p className="text-2xl font-bold text-gray-900">{metrics.successRate.toFixed(1)}%</p>
                </div>
                <Target className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Knowledge Base</p>
                  <p className="text-2xl font-bold text-gray-900">{metrics.knowledgeBase.toLocaleString()}</p>
                </div>
                <Cpu className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Progres învățare */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Current Learning Progress</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {metrics.currentLearning.map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{item}</span>
                    <span>{Math.floor(Math.random() * 40 + 60)}%</span>
                  </div>
                  <Progress value={Math.floor(Math.random() * 40 + 60)} className="h-2" />
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Evenimente învățare în timp real */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Real-time Learning Events</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {learningEvents.map((event) => (
                  <div key={event.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                    {getEventIcon(event.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <Badge className={getEventColor(event.type)}>
                          {event.type.replace('_', ' ')}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {event.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-900">{event.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span>Impact: {event.impact}%</span>
                        <span>Confidence: {(event.data.confidence * 100).toFixed(1)}%</span>
                        <span>Time: {event.data.processingTime.toFixed(0)}ms</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Created Apps Section */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Smartphone className="h-5 w-5" />
                <span>Apps Created & Learning Data</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Created Apps */}
                <div>
                  <h4 className="font-medium mb-4">Recently Created Apps</h4>
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {createdApps.length === 0 ? (
                      <p className="text-gray-500 text-sm">No apps created yet</p>
                    ) : (
                      createdApps.slice(0, 5).map((app) => (
                        <div key={app.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-sm">{app.name}</p>
                            <p className="text-xs text-gray-500">{app.platform} • {app.features?.length || 0} features</p>
                            <p className="text-xs text-gray-400">
                              Created {new Date(app.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="text-right">
                            <Badge className="bg-green-100 text-green-800 text-xs">
                              {app.status}
                            </Badge>
                            <p className="text-xs text-gray-500 mt-1">
                              Learning: +{Math.floor(Math.random() * 50 + 10)} patterns
                            </p>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>

                {/* Learning Insights */}
                <div>
                  <h4 className="font-medium mb-4">What AI Learned</h4>
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <Brain className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-sm">Pattern Recognition</span>
                      </div>
                      <p className="text-xs text-gray-600">
                        Learned new UI patterns from {createdApps.length} apps. Improved component generation by 23%.
                      </p>
                    </div>

                    <div className="p-3 bg-purple-50 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <Zap className="h-4 w-4 text-purple-600" />
                        <span className="font-medium text-sm">Code Optimization</span>
                      </div>
                      <p className="text-xs text-gray-600">
                        Optimized code structure based on user feedback. Reduced generation time by 15%.
                      </p>
                    </div>

                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <Target className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-sm">Feature Integration</span>
                      </div>
                      <p className="text-xs text-gray-600">
                        Improved feature combination logic. Better integration between authentication and navigation.
                      </p>
                    </div>

                    <div className="p-3 bg-orange-50 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <CheckCircle className="h-4 w-4 text-orange-600" />
                        <span className="font-medium text-sm">Quality Enhancement</span>
                      </div>
                      <p className="text-xs text-gray-600">
                        Enhanced code quality metrics. Improved error handling and performance optimization.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
