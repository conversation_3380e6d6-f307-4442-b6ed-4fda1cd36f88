import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

const LEARNING_STATE_FILE = path.join(process.cwd(), 'data', 'continuous_learning.json')
const AUTO_GENERATION_FILE = path.join(process.cwd(), 'data', 'auto_generation.json')

interface AutoGenerationState {
  isRunning: boolean
  intervalId?: NodeJS.Timeout
  lastRun: string
  nextRun: string
  totalAutoGenerations: number
  errors: string[]
}

let autoGenerationState: AutoGenerationState = {
  isRunning: false,
  lastRun: '',
  nextRun: '',
  totalAutoGenerations: 0,
  errors: []
}

function loadAutoGenerationState(): AutoGenerationState {
  try {
    if (fs.existsSync(AUTO_GENERATION_FILE)) {
      const data = fs.readFileSync(AUTO_GENERATION_FILE, 'utf8')
      const loaded = JSON.parse(data)
      return { ...autoGenerationState, ...loaded }
    }
  } catch (error) {
    console.error('Error loading auto generation state:', error)
  }
  return autoGenerationState
}

function saveAutoGenerationState(state: AutoGenerationState) {
  try {
    const stateToSave = { ...state }
    delete stateToSave.intervalId // Don't save the interval ID
    fs.writeFileSync(AUTO_GENERATION_FILE, JSON.stringify(stateToSave, null, 2))
  } catch (error) {
    console.error('Error saving auto generation state:', error)
  }
}

function loadLearningState() {
  try {
    if (fs.existsSync(LEARNING_STATE_FILE)) {
      const data = fs.readFileSync(LEARNING_STATE_FILE, 'utf8')
      return JSON.parse(data)
    }
  } catch (error) {
    console.error('Error loading learning state:', error)
  }
  return null
}

async function performAutoGeneration() {
  try {
    console.log('🤖 AI Continuous Learning: Starting auto-generation...')
    
    const learningState = loadLearningState()
    if (!learningState || !learningState.isActive) {
      console.log('⏸️ Continuous learning is not active, skipping generation')
      return
    }

    // Check if enough time has passed since last generation
    const now = new Date()
    const lastGeneration = new Date(learningState.lastGenerationTime)
    const timeDiff = (now.getTime() - lastGeneration.getTime()) / (1000 * 60) // minutes
    
    if (timeDiff < learningState.generationInterval) {
      console.log(`⏳ Not enough time passed (${timeDiff.toFixed(1)}/${learningState.generationInterval} min)`)
      return
    }

    // Trigger generation
    const response = await fetch('http://localhost:3000/api/ai/continuous-learning', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ action: 'generate' })
    })

    if (response.ok) {
      const result = await response.json()
      autoGenerationState.totalAutoGenerations++
      autoGenerationState.lastRun = now.toISOString()
      autoGenerationState.nextRun = new Date(now.getTime() + 5 * 60 * 1000).toISOString() // Next run in 5 minutes
      
      console.log(`✅ Auto-generated app: ${result.app?.name} (${result.template?.platform})`)
      console.log(`📊 Total auto-generations: ${autoGenerationState.totalAutoGenerations}`)
      
      // Clear old errors on success
      autoGenerationState.errors = []
    } else {
      const error = await response.text()
      console.error('❌ Auto-generation failed:', error)
      autoGenerationState.errors.push(`${now.toISOString()}: ${error}`)
      
      // Keep only last 10 errors
      if (autoGenerationState.errors.length > 10) {
        autoGenerationState.errors = autoGenerationState.errors.slice(-10)
      }
    }

    saveAutoGenerationState(autoGenerationState)
  } catch (error) {
    console.error('💥 Auto-generation error:', error)
    autoGenerationState.errors.push(`${new Date().toISOString()}: ${error}`)
    saveAutoGenerationState(autoGenerationState)
  }
}

function startAutoGeneration() {
  if (autoGenerationState.isRunning) {
    console.log('🔄 Auto-generation is already running')
    return
  }

  console.log('🚀 Starting AI continuous learning auto-generation...')
  
  autoGenerationState.isRunning = true
  autoGenerationState.lastRun = ''
  autoGenerationState.nextRun = new Date(Date.now() + 5 * 60 * 1000).toISOString()
  
  // Run every 5 minutes
  autoGenerationState.intervalId = setInterval(performAutoGeneration, 5 * 60 * 1000)
  
  // Run immediately
  setTimeout(performAutoGeneration, 10000) // Wait 10 seconds before first run
  
  saveAutoGenerationState(autoGenerationState)
}

function stopAutoGeneration() {
  if (!autoGenerationState.isRunning) {
    console.log('⏹️ Auto-generation is not running')
    return
  }

  console.log('🛑 Stopping AI continuous learning auto-generation...')
  
  if (autoGenerationState.intervalId) {
    clearInterval(autoGenerationState.intervalId)
    autoGenerationState.intervalId = undefined
  }
  
  autoGenerationState.isRunning = false
  autoGenerationState.nextRun = ''
  
  saveAutoGenerationState(autoGenerationState)
}

// Initialize state on module load
autoGenerationState = loadAutoGenerationState()

export async function GET(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      autoGeneration: {
        ...autoGenerationState,
        intervalId: undefined // Don't expose interval ID
      }
    })
  } catch (error) {
    console.error('Error getting auto-generation state:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get auto-generation state' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json()

    switch (action) {
      case 'start':
        startAutoGeneration()
        return NextResponse.json({
          success: true,
          message: 'Auto-generation started',
          state: {
            ...autoGenerationState,
            intervalId: undefined
          }
        })

      case 'stop':
        stopAutoGeneration()
        return NextResponse.json({
          success: true,
          message: 'Auto-generation stopped',
          state: {
            ...autoGenerationState,
            intervalId: undefined
          }
        })

      case 'trigger':
        if (!autoGenerationState.isRunning) {
          return NextResponse.json(
            { success: false, error: 'Auto-generation is not running' },
            { status: 400 }
          )
        }
        
        // Trigger immediate generation
        performAutoGeneration()
        
        return NextResponse.json({
          success: true,
          message: 'Manual generation triggered'
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error in auto-generation:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
