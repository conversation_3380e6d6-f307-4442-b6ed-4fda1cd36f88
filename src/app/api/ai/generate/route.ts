import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import jwt from "jsonwebtoken"
import { projectDb } from "@/lib/database-simple"
import { OllamaService } from "@/lib/ollama-service"
import { AndroidCodeGenerator } from "@/lib/generators/android-generator"
import { IOSCodeGenerator } from "@/lib/generators/ios-generator"
import { CrossPlatformGenerator } from "@/lib/generators/cross-platform-generator"

const generateSchema = z.object({
  name: z.string().min(1),
  description: z.string().min(10),
  platform: z.enum(["ANDROID", "IOS", "CROSS_PLATFORM"]),
  features: z.array(z.string()).min(1),
  uiStyle: z.string().optional(),
  colorScheme: z.string().optional(),
  targetAudience: z.string().optional(),
})

const JWT_SECRET = "your-super-secret-jwt-key"

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization")
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, JWT_SECRET) as any
    const userId = decoded.userId

    const body = await request.json()
    const { name, description, platform, features, uiStyle, colorScheme, targetAudience } = generateSchema.parse(body)

    console.log("🚀 Starting REAL app generation:", { name, platform, features: features.length })

    // Initialize Ollama service
    const ollamaService = new OllamaService()

    // Check if Ollama is available
    const isOllamaReady = await ollamaService.checkHealth()
    let useAI = isOllamaReady

    if (!isOllamaReady) {
      console.log("⚠️ AI service not available, using fallback generation")
      useAI = false
    }

    // Create project record with GENERATING status
    const project = await projectDb.create({
      userId,
      name,
      description,
      platform,
      features,
      templateType: "ai_generated",
      status: "GENERATING",
      generatedCode: {},
      version: "1.0.0",
      metadata: {
        uiStyle,
        colorScheme,
        targetAudience,
        generationStarted: new Date().toISOString()
      }
    })

    // Generate app architecture using AI
    console.log("🧠 Generating app architecture with AI...")
    const appArchitecture = await ollamaService.generateAppArchitecture({
      name,
      description,
      platform,
      features,
      uiStyle,
      colorScheme,
      targetAudience
    })

    // Select appropriate code generator
    let codeGenerator
    switch (platform) {
      case "ANDROID":
        codeGenerator = new AndroidCodeGenerator(ollamaService)
        break
      case "IOS":
        codeGenerator = new IOSCodeGenerator(ollamaService)
        break
      case "CROSS_PLATFORM":
        codeGenerator = new CrossPlatformGenerator(ollamaService)
        break
      default:
        throw new Error(`Unsupported platform: ${platform}`)
    }

    // Generate complete application code
    console.log("⚡ Generating application code...")
    const generatedCode = await codeGenerator.generateCompleteApp({
      name,
      description,
      features,
      architecture: appArchitecture,
      uiStyle,
      colorScheme,
      targetAudience
    })

    // Update project with generated code
    const updatedProject = await projectDb.update(project.id, {
      status: "COMPLETED",
      generatedCode,
      metadata: {
        ...project.metadata,
        generationCompleted: new Date().toISOString(),
        architecture: appArchitecture,
        linesOfCode: Object.values(generatedCode).reduce((total, code) =>
          total + (typeof code === 'string' ? code.split('\n').length : 0), 0
        )
      }
    })

    console.log("✅ App generation completed successfully!")

    return NextResponse.json({
      project: updatedProject,
      message: "Real mobile application generated successfully using AI",
      stats: {
        filesGenerated: Object.keys(generatedCode).length,
        linesOfCode: updatedProject.metadata.linesOfCode,
        platform,
        features: features.length
      }
    })

  } catch (error) {
    console.error("❌ Real generation error:", error)
    return NextResponse.json({
      error: "Failed to generate real mobile application",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
