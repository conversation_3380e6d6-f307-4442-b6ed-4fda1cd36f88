import { NextRequest, NextResponse } from "next/server"
import { OllamaService } from "@/lib/ollama-service"

export async function GET(request: NextRequest) {
  try {
    const ollamaService = new OllamaService()
    const isHealthy = await ollamaService.checkHealth()
    
    if (isHealthy) {
      return NextResponse.json({
        status: "healthy",
        message: "Ollama AI service is running and ready",
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json({
        status: "unhealthy",
        message: "Ollama AI service is not available or no models are loaded",
        timestamp: new Date().toISOString()
      }, { status: 503 })
    }
  } catch (error) {
    console.error("Health check error:", error)
    return NextResponse.json({
      status: "error",
      message: "Failed to check Ollama service health",
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
