import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    const { model } = await request.json();

    if (!model) {
      return NextResponse.json(
        { success: false, error: 'Model name is required' },
        { status: 400 }
      );
    }

    // Map model names to Ollama model names
    const modelMap: { [key: string]: string } = {
      'Qwen-2.5-Coder': 'qwen2.5-coder:latest',
      'Qwen-2.5-Chat': 'qwen2.5:latest', 
      'Llama-3.2-1B': 'llama3.2:1b'
    };

    const ollamaModel = modelMap[model];
    if (!ollamaModel) {
      return NextResponse.json(
        { success: false, error: 'Unknown model' },
        { status: 400 }
      );
    }

    // Start download in background
    const downloadCommand = `ollama pull ${ollamaModel}`;
    
    // Don't wait for completion, just start the process
    exec(downloadCommand, (error, stdout, stderr) => {
      if (error) {
        console.error(`Download error for ${model}:`, error);
      } else {
        console.log(`Download completed for ${model}:`, stdout);
      }
    });

    return NextResponse.json({
      success: true,
      message: `Started downloading ${model}`,
      model: ollamaModel
    });

  } catch (error) {
    console.error('Error starting model download:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to start download' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Check Ollama status and available models
    const { stdout } = await execAsync('ollama list');
    const models = stdout.split('\n')
      .filter(line => line.trim() && !line.startsWith('NAME'))
      .map(line => {
        const parts = line.split(/\s+/);
        return {
          name: parts[0],
          id: parts[1],
          size: parts[2],
          modified: parts[3]
        };
      });

    return NextResponse.json({
      success: true,
      available_models: models,
      ollama_status: 'running'
    });

  } catch (error) {
    console.error('Error checking Ollama status:', error);
    return NextResponse.json({
      success: false,
      available_models: [],
      ollama_status: 'not_running',
      error: 'Ollama not available'
    });
  }
}
