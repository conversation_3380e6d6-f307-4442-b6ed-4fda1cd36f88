import { NextRequest, NextResponse } from 'next/server';
import { JsonStorage } from '@/lib/json-storage';
import { OllamaService } from '@/lib/ollama-service';

/**
 * GET /api/ai/models
 * Get list of AI models and their status
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeOllama = searchParams.get('includeOllama') === 'true';

    const storage = new JsonStorage();
    const ollamaService = new OllamaService();

    // Get models from storage
    const storedModels = storage.getAll('ai_models');

    let response: any = {
      success: true,
      models: storedModels,
      total: storedModels.length
    };

    // Include Ollama models if requested
    if (includeOllama) {
      try {
        const isOllamaHealthy = await ollamaService.checkHealth();
        
        if (isOllamaHealthy) {
          const ollamaModels = await ollamaService.getAvailableModels();
          response.ollama = {
            healthy: true,
            models: ollamaModels,
            total: ollamaModels.length
          };
        } else {
          response.ollama = {
            healthy: false,
            models: [],
            total: 0,
            message: 'Ollama service is not available'
          };
        }
      } catch (error) {
        response.ollama = {
          healthy: false,
          models: [],
          total: 0,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Failed to get AI models:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve AI models',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/ai/models
 * Create or update an AI model configuration
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validationError = validateModelRequest(body);
    if (validationError) {
      return NextResponse.json(
        { error: validationError },
        { status: 400 }
      );
    }

    const storage = new JsonStorage();
    
    // Check if model with same name already exists
    const existingModel = storage.findOne('ai_models', m => m.name === body.name);
    
    if (existingModel) {
      // Update existing model
      const updatedModel = storage.update('ai_models', existingModel.id, {
        type: body.type,
        status: body.status || existingModel.status,
        config: body.config || existingModel.config,
        performance_score: body.performance_score || existingModel.performance_score,
        total_generations: body.total_generations || existingModel.total_generations,
        success_rate: body.success_rate || existingModel.success_rate
      });
      
      return NextResponse.json({
        success: true,
        model: updatedModel,
        message: 'AI model updated successfully'
      });
    } else {
      // Create new model
      const newModel = storage.insert('ai_models', {
        name: body.name,
        type: body.type,
        status: body.status || 'inactive',
        config: body.config || {},
        performance_score: body.performance_score || 0.0,
        total_generations: body.total_generations || 0,
        success_rate: body.success_rate || 0.0
      });
      
      return NextResponse.json({
        success: true,
        model: newModel,
        message: 'AI model created successfully'
      }, { status: 201 });
    }

  } catch (error) {
    console.error('Failed to create/update AI model:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to create/update AI model',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/ai/models
 * Update model status or configuration
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.id) {
      return NextResponse.json(
        { error: 'Model ID is required' },
        { status: 400 }
      );
    }

    const storage = new JsonStorage();
    
    // Find the model
    const existingModel = storage.findById('ai_models', body.id);
    
    if (!existingModel) {
      return NextResponse.json(
        { error: 'Model not found' },
        { status: 404 }
      );
    }
    
    // Update model
    const updatedModel = storage.update('ai_models', body.id, {
      ...body,
      updatedAt: new Date().toISOString()
    });
    
    return NextResponse.json({
      success: true,
      model: updatedModel,
      message: 'AI model updated successfully'
    });

  } catch (error) {
    console.error('Failed to update AI model:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to update AI model',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/ai/models
 * Delete an AI model
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const modelId = searchParams.get('id');
    
    if (!modelId) {
      return NextResponse.json(
        { error: 'Model ID is required' },
        { status: 400 }
      );
    }

    const storage = new JsonStorage();
    
    // Check if model exists
    const existingModel = storage.findById('ai_models', modelId);
    
    if (!existingModel) {
      return NextResponse.json(
        { error: 'Model not found' },
        { status: 404 }
      );
    }
    
    // Delete model
    const deleted = storage.delete('ai_models', modelId);
    
    if (deleted) {
      return NextResponse.json({
        success: true,
        message: 'AI model deleted successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to delete model' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Failed to delete AI model:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to delete AI model',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Validate model request
 */
function validateModelRequest(body: any): string | null {
  if (!body) {
    return 'Request body is required';
  }

  if (!body.name || typeof body.name !== 'string' || body.name.trim().length === 0) {
    return 'Model name is required and must be a non-empty string';
  }

  if (body.name.length > 100) {
    return 'Model name must be less than 100 characters';
  }

  if (!body.type || typeof body.type !== 'string') {
    return 'Model type is required and must be a string';
  }

  const validTypes = ['code-generation', 'conversation', 'analysis', 'fallback', 'specialized'];
  if (!validTypes.includes(body.type)) {
    return `Model type must be one of: ${validTypes.join(', ')}`;
  }

  if (body.status && !['active', 'inactive', 'training', 'error'].includes(body.status)) {
    return 'Model status must be one of: active, inactive, training, error';
  }

  if (body.performance_score !== undefined) {
    if (typeof body.performance_score !== 'number' || body.performance_score < 0 || body.performance_score > 10) {
      return 'Performance score must be a number between 0 and 10';
    }
  }

  if (body.success_rate !== undefined) {
    if (typeof body.success_rate !== 'number' || body.success_rate < 0 || body.success_rate > 1) {
      return 'Success rate must be a number between 0 and 1';
    }
  }

  if (body.total_generations !== undefined) {
    if (typeof body.total_generations !== 'number' || body.total_generations < 0) {
      return 'Total generations must be a non-negative number';
    }
  }

  return null;
}
