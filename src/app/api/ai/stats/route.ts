import { NextRequest, NextResponse } from 'next/server';
import { AppGenerationService } from '@/lib/app-generation-service';
import { JsonStorage } from '@/lib/json-storage';

/**
 * GET /api/ai/stats
 * Get AI evolution statistics and metrics
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const modelId = searchParams.get('modelId');
    const period = searchParams.get('period') || '30d'; // Default to 30 days
    const metric = searchParams.get('metric');

    const storage = new JsonStorage();
    const generationService = new AppGenerationService();

    // Get basic generation stats
    const generationStats = generationService.getGenerationStats();

    // Get AI learning data
    const learningData = storage.getAll('ai_learning_data');
    
    // Get AI models
    const models = storage.getAll('ai_models');
    
    // Get evolution metrics
    const evolutionMetrics = storage.getAll('ai_evolution_metrics');
    
    // Filter by model if specified
    const filteredMetrics = modelId 
      ? evolutionMetrics.filter(m => m.model_id === modelId)
      : evolutionMetrics;
    
    // Filter by metric type if specified
    const filteredByMetric = metric
      ? filteredMetrics.filter(m => m.metric_type === metric)
      : filteredMetrics;
    
    // Calculate period start date
    const periodStart = calculatePeriodStart(period);
    
    // Filter by period
    const filteredByPeriod = filteredByMetric.filter(m => 
      new Date(m.recorded_at) >= periodStart
    );
    
    // Group metrics by type for visualization
    const metricsGroupedByType = groupMetricsByType(filteredByPeriod);
    
    // Calculate improvement trends
    const improvementTrends = calculateImprovementTrends(filteredByPeriod);
    
    // Calculate learning velocity
    const learningVelocity = calculateLearningVelocity(learningData, periodStart);
    
    return NextResponse.json({
      success: true,
      stats: {
        generation: generationStats,
        models: {
          total: models.length,
          active: models.filter(m => m.status === 'active').length,
          list: models.map(m => ({
            id: m.id,
            name: m.name,
            type: m.type,
            status: m.status,
            performance_score: m.performance_score,
            total_generations: m.total_generations,
            success_rate: m.success_rate
          }))
        },
        learning: {
          totalInteractions: learningData.length,
          successRate: calculateSuccessRate(learningData),
          recentInteractions: learningData.filter(d => new Date(d.created_at) >= periodStart).length,
          velocity: learningVelocity
        },
        evolution: {
          metrics: metricsGroupedByType,
          trends: improvementTrends
        }
      }
    });

  } catch (error) {
    console.error('Failed to get AI stats:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve AI statistics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/ai/stats
 * Record new AI evolution metrics
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    if (!body.model_id || !body.metric_type || body.metric_value === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: model_id, metric_type, metric_value' },
        { status: 400 }
      );
    }

    const storage = new JsonStorage();
    
    // Get previous metric value for comparison if available
    const previousMetrics = storage.find('ai_evolution_metrics', m => 
      m.model_id === body.model_id && m.metric_type === body.metric_type
    ).sort((a, b) => new Date(b.recorded_at).getTime() - new Date(a.recorded_at).getTime());
    
    const previousValue = previousMetrics.length > 0 ? previousMetrics[0].metric_value : null;
    
    // Calculate improvement percentage if previous value exists
    let improvementPercentage = null;
    if (previousValue !== null) {
      improvementPercentage = ((body.metric_value - previousValue) / previousValue) * 100;
    }
    
    // Insert new metric
    const newMetric = storage.insert('ai_evolution_metrics', {
      model_id: body.model_id,
      metric_type: body.metric_type,
      metric_value: body.metric_value,
      comparison_baseline: previousValue,
      improvement_percentage: improvementPercentage,
      recorded_at: new Date().toISOString()
    });
    
    return NextResponse.json({
      success: true,
      metric: newMetric,
      message: 'AI evolution metric recorded successfully'
    });

  } catch (error) {
    console.error('Failed to record AI metric:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to record AI metric',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Calculate period start date based on period string
 */
function calculatePeriodStart(period: string): Date {
  const now = new Date();
  const match = period.match(/^(\d+)([dwmy])$/);
  
  if (!match) {
    // Default to 30 days if invalid format
    return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }
  
  const value = parseInt(match[1]);
  const unit = match[2];
  
  switch (unit) {
    case 'd': // days
      return new Date(now.getTime() - value * 24 * 60 * 60 * 1000);
    case 'w': // weeks
      return new Date(now.getTime() - value * 7 * 24 * 60 * 60 * 1000);
    case 'm': // months
      const newMonth = now.getMonth() - value;
      return new Date(now.getFullYear(), newMonth, now.getDate());
    case 'y': // years
      return new Date(now.getFullYear() - value, now.getMonth(), now.getDate());
    default:
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }
}

/**
 * Group metrics by type for visualization
 */
function groupMetricsByType(metrics: any[]): Record<string, any[]> {
  const grouped: Record<string, any[]> = {};
  
  for (const metric of metrics) {
    if (!grouped[metric.metric_type]) {
      grouped[metric.metric_type] = [];
    }
    
    grouped[metric.metric_type].push({
      value: metric.metric_value,
      date: metric.recorded_at,
      improvement: metric.improvement_percentage
    });
  }
  
  // Sort each group by date
  for (const type in grouped) {
    grouped[type].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }
  
  return grouped;
}

/**
 * Calculate improvement trends
 */
function calculateImprovementTrends(metrics: any[]): Record<string, number> {
  const trends: Record<string, number> = {};
  const typeGroups: Record<string, any[]> = {};
  
  // Group by metric type
  for (const metric of metrics) {
    if (!typeGroups[metric.metric_type]) {
      typeGroups[metric.metric_type] = [];
    }
    
    typeGroups[metric.metric_type].push(metric);
  }
  
  // Calculate average improvement for each type
  for (const type in typeGroups) {
    const metricsWithImprovement = typeGroups[type].filter(m => m.improvement_percentage !== null);
    
    if (metricsWithImprovement.length > 0) {
      const sum = metricsWithImprovement.reduce((acc, m) => acc + m.improvement_percentage, 0);
      trends[type] = sum / metricsWithImprovement.length;
    } else {
      trends[type] = 0;
    }
  }
  
  return trends;
}

/**
 * Calculate success rate from learning data
 */
function calculateSuccessRate(learningData: any[]): number {
  if (learningData.length === 0) return 0;
  
  const successfulInteractions = learningData.filter(d => d.success).length;
  return (successfulInteractions / learningData.length) * 100;
}

/**
 * Calculate learning velocity (interactions per day)
 */
function calculateLearningVelocity(learningData: any[], periodStart: Date): number {
  const recentData = learningData.filter(d => new Date(d.created_at) >= periodStart);
  
  if (recentData.length === 0) return 0;
  
  const now = new Date();
  const daysDiff = (now.getTime() - periodStart.getTime()) / (24 * 60 * 60 * 1000);
  
  return recentData.length / daysDiff;
}
