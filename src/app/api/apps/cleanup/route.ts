import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

const APPS_FILE = path.join(process.cwd(), 'data', 'generated_apps.json')
const CLEANUP_LOG_FILE = path.join(process.cwd(), 'data', 'cleanup_log.json')

interface CleanupLog {
  lastCleanup: string
  totalDeleted: number
  cleanupHistory: Array<{
    timestamp: string
    deletedCount: number
    deletedApps: Array<{
      id: string
      name: string
      platform: string
      createdAt: string
      expiresAt: string
    }>
  }>
}

function loadApps() {
  try {
    if (fs.existsSync(APPS_FILE)) {
      const data = fs.readFileSync(APPS_FILE, 'utf8')
      return JSON.parse(data)
    }
  } catch (error) {
    console.error('Error loading apps:', error)
  }
  return []
}

function saveApps(apps: any[]) {
  try {
    fs.writeFileSync(APPS_FILE, JSON.stringify(apps, null, 2))
  } catch (error) {
    console.error('Error saving apps:', error)
  }
}

function loadCleanupLog(): CleanupLog {
  try {
    if (fs.existsSync(CLEANUP_LOG_FILE)) {
      const data = fs.readFileSync(CLEANUP_LOG_FILE, 'utf8')
      return JSON.parse(data)
    }
  } catch (error) {
    console.error('Error loading cleanup log:', error)
  }
  
  return {
    lastCleanup: '',
    totalDeleted: 0,
    cleanupHistory: []
  }
}

function saveCleanupLog(log: CleanupLog) {
  try {
    fs.writeFileSync(CLEANUP_LOG_FILE, JSON.stringify(log, null, 2))
  } catch (error) {
    console.error('Error saving cleanup log:', error)
  }
}

function performCleanup(): { deletedCount: number, deletedApps: any[], remainingApps: any[] } {
  const allApps = loadApps()
  const now = new Date()
  
  const expiredApps: any[] = []
  const validApps: any[] = []
  
  allApps.forEach((app: any) => {
    // Add expiresAt if missing (7 days from creation)
    if (!app.expiresAt && app.createdAt) {
      const createdDate = new Date(app.createdAt)
      app.expiresAt = new Date(createdDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString()
    } else if (!app.expiresAt) {
      // If no createdAt, consider it expired
      app.expiresAt = new Date(now.getTime() - 1000).toISOString()
    }
    
    // Check if expired
    if (new Date(app.expiresAt) <= now) {
      expiredApps.push({
        id: app.id,
        name: app.name,
        platform: app.platform,
        createdAt: app.createdAt,
        expiresAt: app.expiresAt
      })
    } else {
      validApps.push(app)
    }
  })
  
  // Save only valid apps
  if (expiredApps.length > 0) {
    saveApps(validApps)
    console.log(`🗑️ Cleaned up ${expiredApps.length} expired apps`)
    expiredApps.forEach(app => {
      console.log(`   - ${app.name} (${app.platform}) - expired ${app.expiresAt}`)
    })
  }
  
  return {
    deletedCount: expiredApps.length,
    deletedApps: expiredApps,
    remainingApps: validApps
  }
}

export async function GET(request: NextRequest) {
  try {
    const cleanupLog = loadCleanupLog()
    const allApps = loadApps()
    const now = new Date()
    
    // Count expired apps without deleting
    let expiredCount = 0
    let soonToExpireCount = 0
    
    allApps.forEach((app: any) => {
      if (!app.expiresAt && app.createdAt) {
        const createdDate = new Date(app.createdAt)
        app.expiresAt = new Date(createdDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString()
      } else if (!app.expiresAt) {
        app.expiresAt = new Date(now.getTime() - 1000).toISOString()
      }
      
      const expiryDate = new Date(app.expiresAt)
      if (expiryDate <= now) {
        expiredCount++
      } else if (expiryDate.getTime() - now.getTime() < 24 * 60 * 60 * 1000) {
        soonToExpireCount++
      }
    })
    
    return NextResponse.json({
      success: true,
      cleanupLog,
      stats: {
        totalApps: allApps.length,
        expiredApps: expiredCount,
        soonToExpireApps: soonToExpireCount,
        validApps: allApps.length - expiredCount
      }
    })
  } catch (error) {
    console.error('Error getting cleanup status:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get cleanup status' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json()
    
    if (action !== 'cleanup') {
      return NextResponse.json(
        { success: false, error: 'Invalid action' },
        { status: 400 }
      )
    }
    
    const result = performCleanup()
    const cleanupLog = loadCleanupLog()
    
    // Update cleanup log
    const cleanupEntry = {
      timestamp: new Date().toISOString(),
      deletedCount: result.deletedCount,
      deletedApps: result.deletedApps
    }
    
    cleanupLog.lastCleanup = cleanupEntry.timestamp
    cleanupLog.totalDeleted += result.deletedCount
    cleanupLog.cleanupHistory.push(cleanupEntry)
    
    // Keep only last 50 cleanup entries
    if (cleanupLog.cleanupHistory.length > 50) {
      cleanupLog.cleanupHistory = cleanupLog.cleanupHistory.slice(-50)
    }
    
    saveCleanupLog(cleanupLog)
    
    return NextResponse.json({
      success: true,
      message: `Cleanup completed. Deleted ${result.deletedCount} expired apps.`,
      result: {
        deletedCount: result.deletedCount,
        deletedApps: result.deletedApps,
        remainingApps: result.remainingApps.length
      },
      cleanupLog
    })
  } catch (error) {
    console.error('Error performing cleanup:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to perform cleanup' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Force cleanup of all apps (for testing/admin purposes)
    const allApps = loadApps()
    const cleanupLog = loadCleanupLog()
    
    const cleanupEntry = {
      timestamp: new Date().toISOString(),
      deletedCount: allApps.length,
      deletedApps: allApps.map((app: any) => ({
        id: app.id,
        name: app.name,
        platform: app.platform,
        createdAt: app.createdAt,
        expiresAt: app.expiresAt
      }))
    }
    
    // Clear all apps
    saveApps([])
    
    // Update cleanup log
    cleanupLog.lastCleanup = cleanupEntry.timestamp
    cleanupLog.totalDeleted += cleanupEntry.deletedCount
    cleanupLog.cleanupHistory.push(cleanupEntry)
    
    if (cleanupLog.cleanupHistory.length > 50) {
      cleanupLog.cleanupHistory = cleanupLog.cleanupHistory.slice(-50)
    }
    
    saveCleanupLog(cleanupLog)
    
    console.log(`🗑️ Force cleanup: Deleted all ${allApps.length} apps`)
    
    return NextResponse.json({
      success: true,
      message: `Force cleanup completed. Deleted all ${allApps.length} apps.`,
      result: {
        deletedCount: allApps.length,
        deletedApps: cleanupEntry.deletedApps,
        remainingApps: 0
      }
    })
  } catch (error) {
    console.error('Error performing force cleanup:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to perform force cleanup' },
      { status: 500 }
    )
  }
}
