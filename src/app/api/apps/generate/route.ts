import { NextRequest, NextResponse } from 'next/server';
import { AppGenerationService, GenerationRequest } from '@/lib/app-generation-service';

/**
 * POST /api/apps/generate
 * Generate a new mobile application using AI
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validationError = validateGenerationRequest(body);
    if (validationError) {
      return NextResponse.json(
        { error: validationError },
        { status: 400 }
      );
    }

    // Extract user ID from headers or session (simplified for demo)
    const userId = request.headers.get('x-user-id') || 'demo-user';

    const generationService = new AppGenerationService();
    
    // Start generation process
    console.log(`🚀 Starting app generation for user ${userId}:`, body.name);
    
    const generatedApp = await generationService.generateApp(userId, body as GenerationRequest);
    
    console.log(`✅ App generation completed: ${generatedApp.id}`);
    
    return NextResponse.json({
      success: true,
      app: generatedApp,
      message: 'Application generated successfully'
    });

  } catch (error) {
    console.error('App generation failed:', error);
    
    return NextResponse.json(
      {
        error: 'App generation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/apps/generate
 * Get generation status or list of generated apps
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || request.headers.get('x-user-id') || 'demo-user';
    const appId = searchParams.get('appId');

    const generationService = new AppGenerationService();

    if (appId) {
      // Get specific app
      const app = generationService.getAppById(appId);
      
      if (!app) {
        return NextResponse.json(
          { error: 'App not found' },
          { status: 404 }
        );
      }

      // Check if user owns this app
      if (app.userId !== userId) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 403 }
        );
      }

      return NextResponse.json({
        success: true,
        app
      });
    } else {
      // Get all apps for user
      const apps = generationService.getUserApps(userId);
      
      return NextResponse.json({
        success: true,
        apps,
        total: apps.length
      });
    }

  } catch (error) {
    console.error('Failed to get apps:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve apps',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Validate generation request
 */
function validateGenerationRequest(body: any): string | null {
  if (!body) {
    return 'Request body is required';
  }

  if (!body.name || typeof body.name !== 'string' || body.name.trim().length === 0) {
    return 'App name is required and must be a non-empty string';
  }

  if (body.name.length > 100) {
    return 'App name must be less than 100 characters';
  }

  if (!body.description || typeof body.description !== 'string' || body.description.trim().length === 0) {
    return 'App description is required and must be a non-empty string';
  }

  if (body.description.length > 500) {
    return 'App description must be less than 500 characters';
  }

  if (!body.platform || !['ANDROID', 'IOS', 'CROSS_PLATFORM'].includes(body.platform)) {
    return 'Platform must be one of: ANDROID, IOS, CROSS_PLATFORM';
  }

  if (!Array.isArray(body.features)) {
    return 'Features must be an array';
  }

  if (body.features.length === 0) {
    return 'At least one feature is required';
  }

  if (body.features.length > 20) {
    return 'Maximum 20 features allowed';
  }

  // Validate each feature
  for (const feature of body.features) {
    if (typeof feature !== 'string' || feature.trim().length === 0) {
      return 'All features must be non-empty strings';
    }
    if (feature.length > 50) {
      return 'Each feature must be less than 50 characters';
    }
  }

  // Optional fields validation
  if (body.uiStyle && (typeof body.uiStyle !== 'string' || body.uiStyle.length > 50)) {
    return 'UI style must be a string with less than 50 characters';
  }

  if (body.colorScheme && (typeof body.colorScheme !== 'string' || body.colorScheme.length > 30)) {
    return 'Color scheme must be a string with less than 30 characters';
  }

  if (body.targetAudience && (typeof body.targetAudience !== 'string' || body.targetAudience.length > 100)) {
    return 'Target audience must be a string with less than 100 characters';
  }

  return null;
}
