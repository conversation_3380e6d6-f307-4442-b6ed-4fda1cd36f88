import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

const APPS_FILE = path.join(process.cwd(), 'data', 'generated_apps.json')

interface GeneratedApp {
  id: string
  name: string
  platform: string
  status: string
  createdAt: string
  expiresAt: string
  userId: string
  features: string[]
  metadata: any
}

function loadApps(): GeneratedApp[] {
  try {
    if (fs.existsSync(APPS_FILE)) {
      const data = fs.readFileSync(APPS_FILE, 'utf8')
      return JSON.parse(data)
    }
  } catch (error) {
    console.error('Error loading apps:', error)
  }
  return []
}

function calculateDaysLeft(expiresAt: string): number {
  const now = new Date()
  const expiry = new Date(expiresAt)
  const diffTime = expiry.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId') || 'default'
    
    const allApps = loadApps()
    
    // Filter apps for user and add expiry dates if missing
    const now = new Date()
    const userApps = allApps
      .filter(app => app.userId === userId || userId === 'default')
      .map(app => {
        // Add expiresAt if missing (7 days from creation)
        if (!app.expiresAt && app.createdAt) {
          const createdDate = new Date(app.createdAt)
          app.expiresAt = new Date(createdDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString()
        } else if (!app.expiresAt) {
          // If no createdAt, set expiry to 7 days from now
          app.expiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString()
        }
        return app
      })
      .filter(app => new Date(app.expiresAt) > now)
      .map(app => ({
        ...app,
        daysLeft: calculateDaysLeft(app.expiresAt)
      }))
      .sort((a, b) => new Date(b.createdAt || b.id).getTime() - new Date(a.createdAt || a.id).getTime())

    return NextResponse.json({
      success: true,
      apps: userApps,
      total: userApps.length
    })
  } catch (error) {
    console.error('Error fetching saved apps:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch saved apps' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const appId = searchParams.get('appId')
    const userId = searchParams.get('userId') || 'default'
    
    if (!appId) {
      return NextResponse.json(
        { success: false, error: 'App ID is required' },
        { status: 400 }
      )
    }

    const allApps = loadApps()
    const updatedApps = allApps.filter(app => 
      !(app.id === appId && (app.userId === userId || userId === 'default'))
    )

    // Save updated apps
    fs.writeFileSync(APPS_FILE, JSON.stringify(updatedApps, null, 2))

    return NextResponse.json({
      success: true,
      message: 'App deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting app:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete app' },
      { status: 500 }
    )
  }
}
