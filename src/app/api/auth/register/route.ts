import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/database'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

const registerSchema = z.object({
  email: z.string().email('Invalid email address').toLowerCase(),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = registerSchema.parse(body)
    
    const { email, password, name, acceptTerms } = validatedData
    
    // Get client info
    const ipAddress = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'
    
    // Validate password strength
    const passwordValidation = validatePassword(password)
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { 
          error: 'Password does not meet security requirements',
          details: passwordValidation.errors 
        },
        { status: 400 }
      )
    }
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })
    
    if (existingUser) {
      // Log failed registration attempt
      await createAuditLog(
        null,
        'REGISTRATION_FAILED',
        'user',
        { email, reason: 'email_already_exists' },
        ipAddress,
        userAgent
      )
      
      return NextResponse.json(
        { error: 'An account with this email already exists' },
        { status: 409 }
      )
    }
    
    // Hash password
    const passwordHash = await hashPassword(password)
    
    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        passwordHash,
        profile: {
          name: name || email.split('@')[0],
        },
        emailVerified: false, // In production, require email verification
        role: 'USER',
        subscriptionTier: 'FREE',
        subscriptionStatus: 'ACTIVE',
      },
    })
    
    // Create session
    const sessionExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    const session = await createUserSession(
      user.id,
      '', // Will be set after token generation
      sessionExpiresAt,
      ipAddress,
      userAgent
    )
    
    // Generate tokens
    const { accessToken, refreshToken } = generateTokenPair(user, session.id)
    
    // Update session with token
    await prisma.session.update({
      where: { id: session.id },
      data: { token: accessToken }
    })
    
    // Create audit log
    await createAuditLog(
      user.id,
      'USER_REGISTERED',
      'user',
      { email, subscriptionTier: user.subscriptionTier },
      ipAddress,
      userAgent
    )
    
    // Track usage
    await trackUsage(
      user.id,
      'user_registration',
      1,
      { source: 'web' },
      ipAddress,
      userAgent
    )
    
    // Return user data (excluding sensitive information)
    const userData = {
      id: user.id,
      email: user.email,
      role: user.role,
      subscriptionTier: user.subscriptionTier,
      subscriptionStatus: user.subscriptionStatus,
      profile: user.profile,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt,
    }
    
    // Set HTTP-only cookie for refresh token
    const response = NextResponse.json(
      {
        message: 'Registration successful',
        user: userData,
        accessToken,
      },
      { status: 201 }
    )
    
    response.cookies.set('refresh-token', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/',
    })
    
    return response
    
  } catch (error) {
    console.error('Registration error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      )
    }
    
    // Log error
    await createAuditLog(
      null,
      'REGISTRATION_ERROR',
      'user',
      { error: error instanceof Error ? error.message : 'Unknown error' },
      request.ip || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    )
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
