import { NextRequest, NextResponse } from 'next/server'
import { RealMobileAppService } from '@/lib/real-mobile-app-service'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const appService = new RealMobileAppService()
    const app = await appService.getApp(params.id)
    
    if (!app) {
      return NextResponse.json(
        { success: false, error: 'App not found' },
        { status: 404 }
      )
    }

    // Generate real-time preview based on platform
    const preview = generateMobilePreview(app)
    
    return NextResponse.json({
      success: true,
      preview,
      buildProgress: app.buildProgress,
      status: app.status,
      buildLogs: app.metadata.buildLogs
    })

  } catch (error) {
    console.error('❌ Failed to generate preview:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to generate preview' },
      { status: 500 }
    )
  }
}

function generateMobilePreview(app: any) {
  const basePreview = {
    id: app.id,
    name: app.name,
    platform: app.platform,
    status: app.status,
    buildProgress: app.buildProgress
  }

  switch (app.platform) {
    case 'ANDROID':
      return {
        ...basePreview,
        type: 'android',
        ui: {
          statusBar: {
            backgroundColor: '#2196F3',
            textColor: '#FFFFFF'
          },
          appBar: {
            title: app.name,
            backgroundColor: '#2196F3',
            textColor: '#FFFFFF'
          },
          content: {
            backgroundColor: '#FFFFFF',
            elements: [
              {
                type: 'text',
                text: app.name,
                style: {
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: '#000000',
                  textAlign: 'center',
                  marginTop: '100px'
                }
              },
              {
                type: 'text',
                text: app.description,
                style: {
                  fontSize: '16px',
                  color: '#666666',
                  textAlign: 'center',
                  margin: '20px',
                  lineHeight: '1.5'
                }
              },
              {
                type: 'button',
                text: 'Get Started',
                style: {
                  backgroundColor: '#2196F3',
                  color: '#FFFFFF',
                  padding: '12px 24px',
                  borderRadius: '4px',
                  border: 'none',
                  fontSize: '16px',
                  marginTop: '40px',
                  cursor: 'pointer'
                }
              }
            ]
          },
          navigationBar: {
            backgroundColor: '#FFFFFF',
            height: '56px'
          }
        }
      }

    case 'IOS':
      return {
        ...basePreview,
        type: 'ios',
        ui: {
          statusBar: {
            backgroundColor: '#000000',
            textColor: '#FFFFFF'
          },
          navigationBar: {
            title: app.name,
            backgroundColor: '#F8F8F8',
            textColor: '#000000',
            borderBottom: '1px solid #E0E0E0'
          },
          content: {
            backgroundColor: '#F8F8F8',
            elements: [
              {
                type: 'text',
                text: app.name,
                style: {
                  fontSize: '28px',
                  fontWeight: '600',
                  color: '#000000',
                  textAlign: 'center',
                  marginTop: '120px',
                  fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif'
                }
              },
              {
                type: 'text',
                text: app.description,
                style: {
                  fontSize: '17px',
                  color: '#666666',
                  textAlign: 'center',
                  margin: '20px',
                  lineHeight: '1.4',
                  fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif'
                }
              },
              {
                type: 'button',
                text: 'Get Started',
                style: {
                  backgroundColor: '#007AFF',
                  color: '#FFFFFF',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  border: 'none',
                  fontSize: '17px',
                  fontWeight: '600',
                  marginTop: '40px',
                  cursor: 'pointer',
                  fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif'
                }
              }
            ]
          },
          tabBar: {
            backgroundColor: '#F8F8F8',
            height: '83px',
            borderTop: '1px solid #E0E0E0'
          }
        }
      }

    case 'CROSS_PLATFORM':
      return {
        ...basePreview,
        type: 'react-native',
        ui: {
          header: {
            title: app.name,
            backgroundColor: '#6200EE',
            textColor: '#FFFFFF'
          },
          content: {
            backgroundColor: '#FFFFFF',
            elements: [
              {
                type: 'text',
                text: app.name,
                style: {
                  fontSize: '26px',
                  fontWeight: 'bold',
                  color: '#000000',
                  textAlign: 'center',
                  marginTop: '100px'
                }
              },
              {
                type: 'text',
                text: app.description,
                style: {
                  fontSize: '16px',
                  color: '#666666',
                  textAlign: 'center',
                  margin: '20px',
                  lineHeight: '1.5'
                }
              },
              {
                type: 'button',
                text: 'Get Started',
                style: {
                  backgroundColor: '#6200EE',
                  color: '#FFFFFF',
                  padding: '14px 28px',
                  borderRadius: '6px',
                  border: 'none',
                  fontSize: '16px',
                  fontWeight: '500',
                  marginTop: '40px',
                  cursor: 'pointer'
                }
              }
            ]
          }
        }
      }

    default:
      return basePreview
  }
}
