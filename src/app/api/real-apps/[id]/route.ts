import { NextRequest, NextResponse } from 'next/server'
import { RealMobileAppService } from '@/lib/real-mobile-app-service'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const appService = new RealMobileAppService()
    const app = await appService.getApp(params.id)
    
    if (!app) {
      return NextResponse.json(
        { success: false, error: 'App not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      app
    })

  } catch (error) {
    console.error('❌ Failed to get app details:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get app details' },
      { status: 500 }
    )
  }
}
