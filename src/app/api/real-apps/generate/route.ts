import { NextRequest, NextResponse } from 'next/server'
import { RealMobileAppService, RealAppConfig } from '@/lib/real-mobile-app-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate request
    if (!body.name || !body.description || !body.platform) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: name, description, platform' },
        { status: 400 }
      )
    }

    const userId = request.headers.get('x-user-id') || 'demo-user'
    
    const config: RealAppConfig = {
      name: body.name,
      description: body.description,
      platform: body.platform,
      features: body.features || [],
      userId
    }

    console.log(`📱 Generating REAL mobile app: ${config.name} for ${userId}`)

    const appService = new RealMobileAppService()
    const realApp = await appService.generateRealApp(config)

    return NextResponse.json({
      success: true,
      app: realApp,
      message: 'Real mobile app generation started'
    })

  } catch (error) {
    console.error('❌ Real app generation failed:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Real app generation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    
    const appService = new RealMobileAppService()
    const apps = await appService.getApps(userId || undefined)
    
    return NextResponse.json({
      success: true,
      apps,
      total: apps.length
    })

  } catch (error) {
    console.error('❌ Failed to get real apps:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get real apps' },
      { status: 500 }
    )
  }
}
