'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Send, 
  Smartphone, 
  Code, 
  Zap, 
  MessageCircle, 
  Settings,
  Download,
  Eye,
  Loader2
} from 'lucide-react';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
}

interface GeneratedApp {
  id: string;
  name: string;
  description: string;
  platform: 'ANDROID' | 'IOS' | 'CROSS_PLATFORM';
  status: 'GENERATING' | 'COMPLETED' | 'FAILED';
  features: string[];
  generatedCode: Record<string, string>;
  metadata: {
    generationTime: number;
    linesOfCode: number;
    filesGenerated: number;
    aiModelUsed: string;
  };
}

export default function CreateAppPage() {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Hello! I\'m your AI assistant for creating mobile applications. Tell me what kind of app you\'d like to build, and I\'ll help you generate it step by step.',
      timestamp: new Date()
    }
  ]);
  
  const [inputMessage, setInputMessage] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentApp, setCurrentApp] = useState<GeneratedApp | null>(null);
  const [selectedPlatform, setSelectedPlatform] = useState<'ANDROID' | 'IOS' | 'CROSS_PLATFORM'>('ANDROID');
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const availableFeatures = [
    'user-authentication',
    'push-notifications',
    'real-time-sync',
    'offline-mode',
    'camera-integration',
    'location-services',
    'social-sharing',
    'in-app-purchases',
    'analytics',
    'dark-mode',
    'multi-language',
    'file-upload',
    'chat-messaging',
    'video-streaming',
    'payment-integration'
  ];

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const addMessage = (type: 'user' | 'ai' | 'system', content: string) => {
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      type,
      content,
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, newMessage]);
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isGenerating) return;

    const userMessage = inputMessage.trim();
    setInputMessage('');
    
    // Add user message
    addMessage('user', userMessage);
    
    // Process the message
    await processUserMessage(userMessage);
  };

  const processUserMessage = async (message: string) => {
    setIsGenerating(true);
    
    try {
      // Simple intent detection (in a real app, this would be more sophisticated)
      if (message.toLowerCase().includes('create') || message.toLowerCase().includes('build') || message.toLowerCase().includes('generate')) {
        await handleAppGeneration(message);
      } else if (message.toLowerCase().includes('feature')) {
        handleFeatureDiscussion(message);
      } else if (message.toLowerCase().includes('platform')) {
        handlePlatformDiscussion(message);
      } else {
        // General conversation
        addMessage('ai', `I understand you want to discuss: "${message}". Could you tell me more about what kind of mobile app you'd like to create? For example:
        
• What's the main purpose of your app?
• Who is your target audience?
• What key features do you need?
• Which platform would you prefer (Android, iOS, or both)?`);
      }
    } catch (error) {
      addMessage('system', 'Sorry, I encountered an error processing your request. Please try again.');
      console.error('Error processing message:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleAppGeneration = async (message: string) => {
    // Extract app details from message (simplified)
    const appName = extractAppName(message) || 'My Mobile App';
    const description = message;
    
    addMessage('ai', `Great! I'll help you create "${appName}". Let me generate this app for you with the following configuration:

📱 **Platform**: ${selectedPlatform}
🎯 **Features**: ${selectedFeatures.length > 0 ? selectedFeatures.join(', ') : 'Basic features'}

Starting generation process...`);

    try {
      const response = await fetch('/api/apps/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'demo-user'
        },
        body: JSON.stringify({
          name: appName,
          description: description,
          platform: selectedPlatform,
          features: selectedFeatures.length > 0 ? selectedFeatures : ['basic-ui', 'navigation']
        })
      });

      const result = await response.json();

      if (result.success) {
        setCurrentApp(result.app);
        addMessage('ai', `🎉 **App Generated Successfully!**

**${result.app.name}** has been created with:
• **${result.app.metadata.filesGenerated}** files generated
• **${result.app.metadata.linesOfCode}** lines of code
• **${result.app.metadata.generationTime}ms** generation time
• **AI Model**: ${result.app.metadata.aiModelUsed}

You can now preview the code or download the project files.`);
      } else {
        addMessage('system', `Failed to generate app: ${result.error}`);
      }
    } catch (error) {
      addMessage('system', 'Failed to generate app. Please try again.');
      console.error('Generation error:', error);
    }
  };

  const handleFeatureDiscussion = (message: string) => {
    addMessage('ai', `I see you're interested in features! Here are some popular features I can help you implement:

**Authentication & User Management**
• User registration and login
• Social media authentication
• Profile management

**Communication & Social**
• Push notifications
• In-app messaging
• Social sharing

**Data & Storage**
• Offline mode
• Real-time synchronization
• File upload and management

**Device Integration**
• Camera integration
• Location services
• Biometric authentication

Which features would you like to include in your app? You can select them below or tell me specifically what you need.`);
  };

  const handlePlatformDiscussion = (message: string) => {
    addMessage('ai', `Let's talk about platforms! I can generate apps for:

**🤖 Android**
• Native Kotlin development
• Material Design 3
• Google Play Store ready

**🍎 iOS**
• Native SwiftUI development
• iOS Human Interface Guidelines
• App Store ready

**🔄 Cross-Platform**
• React Native development
• Single codebase for both platforms
• Native performance

Which platform would you prefer? You can select it below or let me know your preference.`);
  };

  const extractAppName = (message: string): string | null => {
    // Simple regex to extract app name (in a real app, use NLP)
    const patterns = [
      /create (?:an? )?app (?:called |named )?["']([^"']+)["']/i,
      /build (?:an? )?app (?:called |named )?["']([^"']+)["']/i,
      /(?:called |named )["']([^"']+)["']/i
    ];
    
    for (const pattern of patterns) {
      const match = message.match(pattern);
      if (match) return match[1];
    }
    
    return null;
  };

  const toggleFeature = (feature: string) => {
    setSelectedFeatures(prev => 
      prev.includes(feature) 
        ? prev.filter(f => f !== feature)
        : [...prev, feature]
    );
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-2rem)]">
          
          {/* Chat Interface */}
          <div className="lg:col-span-2">
            <Card className="h-full flex flex-col">
              <CardHeader className="border-b">
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="h-5 w-5" />
                  AI App Generator Chat
                  {isGenerating && <Loader2 className="h-4 w-4 animate-spin" />}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col p-0">
                <ScrollArea className="flex-1 p-4">
                  <div className="space-y-4">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-[80%] rounded-lg p-3 ${
                            message.type === 'user'
                              ? 'bg-blue-600 text-white'
                              : message.type === 'ai'
                              ? 'bg-gray-100 text-gray-900'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          <div className="whitespace-pre-wrap">{message.content}</div>
                          <div className="text-xs opacity-70 mt-1">
                            {message.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    ))}
                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>
                
                <div className="border-t p-4">
                  <div className="flex gap-2">
                    <Input
                      ref={inputRef}
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Describe the app you want to create..."
                      disabled={isGenerating}
                      className="flex-1"
                    />
                    <Button 
                      onClick={handleSendMessage}
                      disabled={!inputMessage.trim() || isGenerating}
                      size="icon"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Configuration Panel */}
          <div className="space-y-6">
            
            {/* Platform Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Smartphone className="h-5 w-5" />
                  Platform
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {(['ANDROID', 'IOS', 'CROSS_PLATFORM'] as const).map((platform) => (
                  <Button
                    key={platform}
                    variant={selectedPlatform === platform ? 'default' : 'outline'}
                    className="w-full justify-start"
                    onClick={() => setSelectedPlatform(platform)}
                  >
                    {platform === 'ANDROID' && '🤖'}
                    {platform === 'IOS' && '🍎'}
                    {platform === 'CROSS_PLATFORM' && '🔄'}
                    <span className="ml-2">
                      {platform === 'CROSS_PLATFORM' ? 'Cross Platform' : platform}
                    </span>
                  </Button>
                ))}
              </CardContent>
            </Card>

            {/* Features Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Features ({selectedFeatures.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-48">
                  <div className="space-y-2">
                    {availableFeatures.map((feature) => (
                      <Badge
                        key={feature}
                        variant={selectedFeatures.includes(feature) ? 'default' : 'outline'}
                        className="cursor-pointer mr-2 mb-2"
                        onClick={() => toggleFeature(feature)}
                      >
                        {feature.replace(/-/g, ' ')}
                      </Badge>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Generated App Preview */}
            {currentApp && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    Generated App
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-semibold">{currentApp.name}</h3>
                    <p className="text-sm text-gray-600">{currentApp.description}</p>
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Files:</span> {currentApp.metadata.filesGenerated}
                    </div>
                    <div>
                      <span className="font-medium">Lines:</span> {currentApp.metadata.linesOfCode}
                    </div>
                    <div>
                      <span className="font-medium">Time:</span> {currentApp.metadata.generationTime}ms
                    </div>
                    <div>
                      <span className="font-medium">AI:</span> {currentApp.metadata.aiModelUsed}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-1" />
                      Preview
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
