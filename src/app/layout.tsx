import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers'
import { Toaster } from '@/components/ui/toaster'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'AndroidWeb Enterprise - AI Mobile Developer Platform',
  description: 'Create mobile applications through AI conversation. Enterprise-grade platform for Android and iOS development.',
  keywords: ['AI', 'mobile development', 'Android', 'iOS', 'enterprise', 'automation'],
  authors: [{ name: 'AndroidWeb Enterprise Team' }],
  creator: 'AndroidWeb Enterprise',
  publisher: 'AndroidWeb Enterprise',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://androidweb-enterprise.com',
    title: 'AndroidWeb Enterprise - AI Mobile Developer Platform',
    description: 'Create mobile applications through AI conversation. Enterprise-grade platform for Android and iOS development.',
    siteName: 'AndroidWeb Enterprise',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AndroidWeb Enterprise - AI Mobile Developer Platform',
    description: 'Create mobile applications through AI conversation. Enterprise-grade platform for Android and iOS development.',
    creator: '@androidweb',
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  )
}
