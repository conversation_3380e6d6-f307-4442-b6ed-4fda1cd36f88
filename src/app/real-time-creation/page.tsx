'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Loader2, Smartphone, MessageSquare, Code, Eye, Pause, Play } from 'lucide-react'

interface CreationStep {
  id: string
  name: string
  status: 'pending' | 'in_progress' | 'completed' | 'error'
  progress: number
  details?: string
  timestamp?: string
}

interface AppPreview {
  screenName: string
  imageUrl?: string
  codeSnippet: string
}

export default function RealTimeCreationPage() {
  const [activeCreation, setActiveCreation] = useState<string | null>('ChatApp')
  const [isMonitoring, setIsMonitoring] = useState(true)
  const [creationSteps, setCreationSteps] = useState<CreationStep[]>([
    { id: '1', name: 'Analyzing requirements', status: 'completed', progress: 100 },
    { id: '2', name: 'Generating app architecture', status: 'completed', progress: 100 },
    { id: '3', name: 'Creating UI components', status: 'in_progress', progress: 65 },
    { id: '4', name: 'Implementing business logic', status: 'pending', progress: 0 },
    { id: '5', name: 'Integrating APIs', status: 'pending', progress: 0 },
    { id: '6', name: 'Finalizing application', status: 'pending', progress: 0 },
  ])
  
  const [chatMessages, setChatMessages] = useState([
    { role: 'system', content: 'Creation process started for ChatApp' },
    { role: 'ai', content: 'I\'ll create a modern chat application with real-time messaging capabilities.' },
    { role: 'user', content: 'Can you add support for image sharing?' },
    { role: 'ai', content: 'Yes, I\'ll implement image sharing functionality using native camera integration and cloud storage.' },
    { role: 'system', content: 'Generating app architecture...' },
    { role: 'ai', content: 'I\'ve designed a scalable architecture with Firebase Realtime Database for instant messaging.' },
  ])
  
  const [appPreview, setAppPreview] = useState<AppPreview>({
    screenName: 'ChatListScreen',
    codeSnippet: `import React, { useEffect, useState } from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import { ChatItem } from '../components/ChatItem';
import { firestore } from '../services/firebase';

export const ChatListScreen = ({ navigation }) => {
  const [chats, setChats] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const unsubscribe = firestore()
      .collection('chats')
      .where('participants', 'array-contains', currentUserId)
      .onSnapshot(snapshot => {
        const chatList = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setChats(chatList);
        setLoading(false);
      });
      
    return () => unsubscribe();
  }, []);

  return (
    <View style={styles.container}>
      <FlatList
        data={chats}
        renderItem={({ item }) => (
          <ChatItem
            chat={item}
            onPress={() => navigation.navigate('ChatRoom', { chatId: item.id })}
          />
        )}
        keyExtractor={item => item.id}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff'
  }
});`
  })
  
  // Simulate real-time updates
  useEffect(() => {
    if (!isMonitoring) return
    
    const interval = setInterval(() => {
      // Update progress of in-progress step
      setCreationSteps(steps => {
        const newSteps = [...steps]
        const inProgressIndex = newSteps.findIndex(step => step.status === 'in_progress')
        
        if (inProgressIndex >= 0) {
          const step = newSteps[inProgressIndex]
          if (step.progress < 100) {
            newSteps[inProgressIndex] = { ...step, progress: step.progress + 5 }
          } else {
            newSteps[inProgressIndex] = { ...step, status: 'completed' }
            
            // Start next step
            if (inProgressIndex < newSteps.length - 1) {
              newSteps[inProgressIndex + 1] = { 
                ...newSteps[inProgressIndex + 1], 
                status: 'in_progress',
                progress: 5
              }
            }
          }
        }
        
        return newSteps
      })
      
      // Add new chat message occasionally
      if (Math.random() > 0.7) {
        const messages = [
          "I'm optimizing the database schema for better performance.",
          "Adding support for push notifications when new messages arrive.",
          "Implementing end-to-end encryption for secure messaging.",
          "Creating responsive UI that works well on different screen sizes.",
          "Adding offline support so messages can be sent when back online."
        ]
        
        setChatMessages(prev => [
          ...prev, 
          { 
            role: 'ai', 
            content: messages[Math.floor(Math.random() * messages.length)]
          }
        ])
      }
      
    }, 2000)
    
    return () => clearInterval(interval)
  }, [isMonitoring])
  
  const toggleMonitoring = () => {
    setIsMonitoring(!isMonitoring)
  }
  
  return (
    <div className="container mx-auto py-8 px-4 max-w-7xl">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">Real-time Creation Monitor</h1>
          <p className="text-gray-600">Watch in real-time as AI creates mobile applications</p>
        </div>
        <Button 
          onClick={toggleMonitoring}
          variant={isMonitoring ? "destructive" : "default"}
        >
          {isMonitoring ? (
            <>
              <Pause className="w-4 h-4 mr-2" />
              Pause Monitoring
            </>
          ) : (
            <>
              <Play className="w-4 h-4 mr-2" />
              Resume Monitoring
            </>
          )}
        </Button>
      </div>
      
      <div className="grid grid-cols-12 gap-6">
        {/* Active Creations Sidebar */}
        <div className="col-span-12 md:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Smartphone className="w-5 h-5" />
                Active Creations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div 
                  className={`p-3 rounded-md cursor-pointer flex items-center justify-between ${
                    activeCreation === 'ChatApp' ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setActiveCreation('ChatApp')}
                >
                  <div>
                    <div className="font-medium">ChatApp</div>
                    <div className="text-xs text-gray-500">Android</div>
                  </div>
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                </div>
                
                <div 
                  className={`p-3 rounded-md cursor-pointer flex items-center justify-between ${
                    activeCreation === 'ShoppingCart' ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setActiveCreation('ShoppingCart')}
                >
                  <div>
                    <div className="font-medium">ShoppingCart</div>
                    <div className="text-xs text-gray-500">React Native</div>
                  </div>
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Main Content */}
        <div className="col-span-12 md:col-span-9">
          {activeCreation ? (
            <Tabs defaultValue="progress">
              <TabsList className="mb-4">
                <TabsTrigger value="progress">
                  <Loader2 className="w-4 h-4 mr-2" />
                  Creation Progress
                </TabsTrigger>
                <TabsTrigger value="preview">
                  <Eye className="w-4 h-4 mr-2" />
                  App Preview
                </TabsTrigger>
                <TabsTrigger value="chat">
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Chat Interface
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="progress">
                <Card>
                  <CardHeader>
                    <CardTitle>Creation Progress for {activeCreation}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {creationSteps.map((step) => (
                        <div key={step.id}>
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              {step.status === 'completed' && (
                                <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center text-white">
                                  ✓
                                </div>
                              )}
                              {step.status === 'in_progress' && (
                                <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />
                              )}
                              {step.status === 'pending' && (
                                <div className="w-5 h-5 rounded-full border border-gray-300"></div>
                              )}
                              <span className={`font-medium ${
                                step.status === 'completed' ? 'text-green-700' :
                                step.status === 'in_progress' ? 'text-blue-700' : 'text-gray-500'
                              }`}>
                                {step.name}
                              </span>
                            </div>
                            <span className="text-sm text-gray-500">{step.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${
                                step.status === 'completed' ? 'bg-green-500' : 
                                step.status === 'in_progress' ? 'bg-blue-500' : 'bg-gray-300'
                              }`}
                              style={{ width: `${step.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="preview">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Phone Preview</CardTitle>
                    </CardHeader>
                    <CardContent className="flex justify-center">
                      <div className="w-[280px] h-[580px] bg-gray-100 rounded-[36px] border-8 border-gray-800 relative overflow-hidden">
                        <div className="absolute top-0 w-full h-8 bg-gray-800 flex justify-center items-end">
                          <div className="w-24 h-5 bg-gray-800 rounded-b-xl"></div>
                        </div>
                        <div className="w-full h-full pt-8 bg-white">
                          <div className="bg-blue-500 text-white p-4">
                            <h3 className="text-lg font-bold">Messages</h3>
                          </div>
                          <div className="p-2">
                            {[1, 2, 3, 4, 5].map((i) => (
                              <div key={i} className="flex items-center p-2 border-b">
                                <div className="w-10 h-10 rounded-full bg-gray-300 mr-3"></div>
                                <div>
                                  <div className="font-medium">User {i}</div>
                                  <div className="text-xs text-gray-500">Last message...</div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Code className="w-5 h-5" />
                        {appPreview.screenName}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <pre className="bg-gray-900 text-gray-100 p-4 rounded-md text-sm overflow-auto max-h-[500px]">
                        <code>{appPreview.codeSnippet}</code>
                      </pre>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="chat">
                <Card>
                  <CardHeader>
                    <CardTitle>AI Collaboration Chat</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="border rounded-md h-[500px] flex flex-col">
                      <div className="flex-1 p-4 overflow-y-auto space-y-4">
                        {chatMessages.map((message, index) => (
                          <div key={index} className={`flex ${
                            message.role === 'user' ? 'justify-end' : 
                            message.role === 'system' ? 'justify-center' : 'justify-start'
                          }`}>
                            {message.role === 'system' ? (
                              <div className="bg-gray-100 text-gray-800 text-xs py-1 px-3 rounded-full">
                                {message.content}
                              </div>
                            ) : message.role === 'user' ? (
                              <div className="bg-blue-500 text-white py-2 px-4 rounded-lg max-w-[80%]">
                                {message.content}
                              </div>
                            ) : (
                              <div className="bg-gray-200 py-2 px-4 rounded-lg max-w-[80%]">
                                {message.content}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                      <Separator />
                      <div className="p-4">
                        <div className="flex gap-2">
                          <input 
                            type="text" 
                            className="flex-1 border rounded-md px-3 py-2"
                            placeholder="Type a message to the AI..."
                          />
                          <Button>Send</Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Smartphone className="w-12 h-12 text-gray-400 mb-4" />
                <h3 className="text-xl font-medium text-gray-700">Select a Creation to Monitor</h3>
                <p className="text-gray-500 mt-2">
                  Choose an active creation from the list to see real-time progress
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
