'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Smartphone, Play, Download, Eye, Code, Layers, Zap, Trash2, Clock } from 'lucide-react'

interface CreationSession {
  id: string
  name: string
  platform: string
  status: 'active' | 'paused' | 'completed'
  progress: number
  currentStep: string
  preview?: string
  createdAt: string
  expiresAt: string
}

interface GeneratedApp {
  id: string
  name: string
  platform: string
  status: string
  createdAt: string
  expiresAt: string
  daysLeft: number
}

export default function RealtimeCreationPage() {
  const [sessions, setSessions] = useState<CreationSession[]>([])
  const [selectedSession, setSelectedSession] = useState<string>('')
  const [newAppName, setNewAppName] = useState('')
  const [newAppDescription, setNewAppDescription] = useState('')
  const [selectedPlatform, setSelectedPlatform] = useState('ANDROID')
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([])
  const [isCreating, setIsCreating] = useState(false)
  const [savedApps, setSavedApps] = useState<GeneratedApp[]>([])
  const [currentPreview, setCurrentPreview] = useState<any>(null)
  const [continuousLearning, setContinuousLearning] = useState<any>(null)
  const [autoGeneration, setAutoGeneration] = useState<any>(null)

  const features = [
    'user-authentication',
    'push-notifications',
    'location-services',
    'camera-integration',
    'payment-integration',
    'real-time-sync',
    'offline-mode',
    'analytics',
    'social-sharing',
    'dark-mode'
  ]

  const currentSession = sessions.find(s => s.id === selectedSession)

  // Load saved apps on component mount
  useEffect(() => {
    loadSavedApps()
  }, [])

  const loadSavedApps = async () => {
    try {
      const response = await fetch('/api/apps/saved')
      if (response.ok) {
        const data = await response.json()
        setSavedApps(data.apps || [])
      }
    } catch (error) {
      console.error('Error loading saved apps:', error)
    }
  }

  const startNewCreation = async () => {
    if (!newAppName.trim() || !newAppDescription.trim()) {
      alert('Please fill in app name and description')
      return
    }

    setIsCreating(true)

    try {
      // Create new session
      const newSession: CreationSession = {
        id: Date.now().toString(),
        name: newAppName,
        platform: selectedPlatform,
        status: 'active',
        progress: 0,
        currentStep: 'Initializing project...',
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      }

      setSessions(prev => [...prev, newSession])
      setSelectedSession(newSession.id)

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setSessions(prev => prev.map(session => {
          if (session.id === newSession.id && session.progress < 90) {
            const newProgress = session.progress + Math.random() * 15
            const steps = [
              'Initializing project...',
              'Setting up dependencies...',
              'Generating UI components...',
              'Implementing features...',
              'Adding navigation...',
              'Configuring state management...',
              'Setting up API integration...',
              'Adding authentication...',
              'Implementing push notifications...',
              'Finalizing build...'
            ]
            const stepIndex = Math.floor((newProgress / 100) * steps.length)

            return {
              ...session,
              progress: Math.min(newProgress, 90),
              currentStep: steps[stepIndex] || 'Processing...'
            }
          }
          return session
        }))
      }, 1500)

      // Start app generation
      const response = await fetch('/api/apps/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'realtime-user'
        },
        body: JSON.stringify({
          name: newAppName,
          description: newAppDescription,
          platform: selectedPlatform,
          features: selectedFeatures
        })
      })

      clearInterval(progressInterval)

      if (response.ok) {
        const result = await response.json()

        // Update session with completion
        setSessions(prev => prev.map(s =>
          s.id === newSession.id
            ? { ...s, status: 'completed', progress: 100, currentStep: 'Generation completed!' }
            : s
        ))

        // Add to saved apps
        const savedApp: GeneratedApp = {
          id: result.app.id,
          name: result.app.name,
          platform: result.app.platform,
          status: result.app.status,
          createdAt: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          daysLeft: 7
        }
        setSavedApps(prev => [savedApp, ...prev])
        setCurrentPreview(result.app)

        // Clear form
        setNewAppName('')
        setNewAppDescription('')
        setSelectedFeatures([])
      } else {
        throw new Error('Failed to generate app')
      }
    } catch (error) {
      console.error('Error creating app:', error)
      alert('Error creating app. Please try again.')

      // Remove failed session
      setSessions(prev => prev.filter(s => s.id !== newSession.id))
    } finally {
      setIsCreating(false)
    }
  }

  const deleteApp = async (appId: string) => {
    if (confirm('Are you sure you want to delete this app?')) {
      setSavedApps(prev => prev.filter(app => app.id !== appId))
    }
  }

  const viewAppDetails = async (appId: string) => {
    try {
      const response = await fetch(`/api/apps/${appId}`)
      if (response.ok) {
        const data = await response.json()
        setCurrentPreview(data.app)
      }
    } catch (error) {
      console.error('Error loading app details:', error)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Real-time Creation Monitor</h1>
          <p className="text-gray-600">Urmărește în timp real cum AI-ul creează aplicații mobile pas cu pas</p>
        </div>
        <Button
          className="bg-blue-600 hover:bg-blue-700"
          onClick={() => window.location.reload()}
        >
          <Zap className="w-4 h-4 mr-2" />
          Refresh Monitor
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Active Creations Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layers className="w-5 h-5" />
                Active Creations
                <Badge variant="secondary">{sessions.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {sessions.map((session) => (
                  <div
                    key={session.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedSession === session.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedSession(session.id)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{session.name}</h3>
                      <Badge
                        variant={session.status === 'active' ? 'default' : session.status === 'completed' ? 'secondary' : 'outline'}
                        className="text-xs"
                      >
                        {session.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{session.platform}</p>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Progress</span>
                        <span>{Math.round(session.progress)}%</span>
                      </div>
                      <Progress value={session.progress} className="h-2" />
                    </div>
                    <p className="text-xs text-gray-500 mt-2">{session.currentStep}</p>
                  </div>
                ))}

                {sessions.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Smartphone className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No active creations</p>
                  </div>
                )}
              </div>

              {/* Quick Start New Creation */}
              <div className="mt-6 p-4 border rounded-lg bg-gray-50">
                <h4 className="font-medium mb-3">Start New Creation</h4>
                <div className="space-y-3">
                  <Input
                    placeholder="App name"
                    value={newAppName}
                    onChange={(e) => setNewAppName(e.target.value)}
                    disabled={isCreating}
                  />
                  <Textarea
                    placeholder="App description"
                    value={newAppDescription}
                    onChange={(e) => setNewAppDescription(e.target.value)}
                    rows={2}
                    disabled={isCreating}
                  />
                  <Select value={selectedPlatform} onValueChange={setSelectedPlatform} disabled={isCreating}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ANDROID">Android</SelectItem>
                      <SelectItem value="IOS">iOS</SelectItem>
                      <SelectItem value="CROSS_PLATFORM">Cross-Platform</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button
                    className="w-full"
                    size="sm"
                    onClick={startNewCreation}
                    disabled={isCreating || !newAppName.trim() || !newAppDescription.trim()}
                  >
                    {isCreating ? (
                      <>
                        <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Creating...
                      </>
                    ) : (
                      <>
                        <Play className="w-4 h-4 mr-2" />
                        Start Creation
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Area with Phone Preview */}
        <div className="lg:col-span-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Mobile Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Smartphone className="w-5 h-5" />
                  Live Phone Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">
                  <div className="relative">
                    {/* Phone Frame */}
                    <div className="w-64 h-[500px] bg-black rounded-[2.5rem] p-2">
                      <div className="w-full h-full bg-white rounded-[2rem] overflow-hidden">
                        {/* Status Bar */}
                        <div className="h-6 bg-gray-100 flex items-center justify-between px-4 text-xs">
                          <span>9:41</span>
                          <div className="flex gap-1">
                            <div className="w-4 h-2 bg-green-500 rounded-sm"></div>
                            <div className="w-4 h-2 bg-gray-300 rounded-sm"></div>
                            <div className="w-4 h-2 bg-gray-300 rounded-sm"></div>
                          </div>
                        </div>

                        {/* App Content */}
                        <div className="p-4 h-full">
                          {currentSession ? (
                            <div>
                              <div className="text-center mb-4">
                                <h2 className="text-lg font-bold">{currentSession.name}</h2>
                                <p className="text-sm text-gray-600">{currentSession.platform} App</p>
                              </div>

                              {/* Demo UI Elements based on progress */}
                              <div className="space-y-3">
                                {currentSession.progress > 20 && (
                                  <div className="h-8 bg-blue-100 rounded flex items-center px-3">
                                    <div className="w-4 h-4 bg-blue-500 rounded mr-2"></div>
                                    <span className="text-sm">Search...</span>
                                  </div>
                                )}

                                {currentSession.progress > 40 && (
                                  <div className="grid grid-cols-2 gap-2">
                                    <div className="h-20 bg-gray-100 rounded flex items-center justify-center">
                                      <span className="text-xs">Item 1</span>
                                    </div>
                                    <div className="h-20 bg-gray-100 rounded flex items-center justify-center">
                                      <span className="text-xs">Item 2</span>
                                    </div>
                                  </div>
                                )}

                                {currentSession.progress > 60 && (
                                  <div className="h-12 bg-green-500 rounded flex items-center justify-center">
                                    <span className="text-white text-sm font-medium">Action Button</span>
                                  </div>
                                )}

                                {currentSession.progress > 80 && (
                                  <div className="space-y-2">
                                    <div className="h-6 bg-gray-200 rounded"></div>
                                    <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                                    <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ) : (
                            <div className="text-center text-gray-500 mt-20">
                              <Smartphone className="w-12 h-12 mx-auto mb-4 opacity-50" />
                              <p className="text-sm">Start creating an app to see live preview</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Loading Indicator */}
                    {currentSession && currentSession.status === 'active' && (
                      <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
                        <div className="flex items-center gap-2 bg-white px-3 py-1 rounded-full shadow-lg border">
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                          <span className="text-xs">Building...</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Creation Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Creation Progress</CardTitle>
              </CardHeader>
              <CardContent>
                {currentSession ? (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Overall Progress</span>
                        <span>{Math.round(currentSession.progress)}%</span>
                      </div>
                      <Progress value={currentSession.progress} className="h-3" />
                    </div>

                    <div className="flex items-center gap-2 text-sm">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span>Current: {currentSession.currentStep}</span>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-medium text-sm">Steps Completed:</h4>
                      {[
                        { step: 'Project Setup', completed: currentSession.progress > 10 },
                        { step: 'Dependencies', completed: currentSession.progress > 25 },
                        { step: 'UI Framework', completed: currentSession.progress > 40 },
                        { step: 'Components', completed: currentSession.progress > 55 },
                        { step: 'Navigation', completed: currentSession.progress > 70 },
                        { step: 'Features', completed: currentSession.progress > 85 },
                        { step: 'Build', completed: currentSession.progress >= 100 }
                      ].map((item, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${
                            item.completed ? 'bg-green-500' : 'bg-gray-300'
                          }`}></div>
                          <span className={`text-sm ${
                            item.completed ? 'text-green-700' : 'text-gray-500'
                          }`}>
                            {item.step}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Code className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Select an active creation to see progress</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Saved Apps Section */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Saved Apps (7-day storage)
                <Badge variant="outline">{savedApps.length}</Badge>
              </CardTitle>
              <CardDescription>
                Apps are automatically deleted after 7 days. Download them before they expire.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {savedApps.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {savedApps.map((app) => (
                    <div key={app.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">{app.name}</h3>
                        <Badge variant="secondary" className="text-xs">
                          {app.platform}
                        </Badge>
                      </div>

                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-3">
                        <Clock className="w-4 h-4" />
                        <span>{app.daysLeft} days left</span>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1"
                          onClick={() => viewAppDetails(app.id)}
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          View
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            // Simulate download
                            const link = document.createElement('a')
                            link.href = `data:text/plain;charset=utf-8,${encodeURIComponent(`App: ${app.name}\nPlatform: ${app.platform}\nGenerated: ${app.createdAt}`)}`
                            link.download = `${app.name}.txt`
                            link.click()
                          }}
                        >
                          <Download className="w-4 h-4 mr-1" />
                          Download
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => deleteApp(app.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Download className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No saved apps yet</p>
                  <p className="text-xs">Generated apps will appear here</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}