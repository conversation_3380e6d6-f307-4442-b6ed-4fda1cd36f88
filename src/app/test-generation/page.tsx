'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, CheckCircle, XCircle, Smartphone, Code, Zap } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface GenerationResult {
  project: any
  message: string
  stats: {
    filesGenerated: number
    linesOfCode: number
    platform: string
    features: number
  }
}

export default function TestGenerationPage() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [ollamaStatus, setOllamaStatus] = useState<'checking' | 'online' | 'offline'>('checking')
  const [result, setResult] = useState<GenerationResult | null>(null)
  const [formData, setFormData] = useState({
    name: 'TaskMaster Pro',
    description: 'A powerful task management app with real-time collaboration, file attachments, and smart notifications',
    platform: 'ANDROID',
    features: ['user-authentication', 'real-time-sync', 'file-upload', 'push-notifications', 'offline-mode'],
    uiStyle: 'Modern Material Design',
    colorScheme: 'Blue and White',
    targetAudience: 'Business professionals'
  })

  const checkOllamaStatus = async () => {
    setOllamaStatus('checking')
    try {
      const response = await fetch('/api/ai/health')
      if (response.ok) {
        setOllamaStatus('online')
        toast.success('Ollama AI service is ready!')
      } else {
        setOllamaStatus('offline')
        toast.error('Ollama AI service is not available')
      }
    } catch (error) {
      setOllamaStatus('offline')
      toast.error('Failed to connect to AI service')
    }
  }

  const generateApp = async () => {
    if (ollamaStatus !== 'online') {
      toast.error('AI service is not available. Please check Ollama status.')
      return
    }

    setIsGenerating(true)
    setResult(null)

    try {
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer demo-token' // For testing
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Generation failed')
      }

      const result = await response.json()
      setResult(result)
      toast.success('🎉 Real mobile app generated successfully!')
    } catch (error) {
      console.error('Generation error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to generate app')
    } finally {
      setIsGenerating(false)
    }
  }

  const addFeature = () => {
    const newFeature = prompt('Enter a new feature:')
    if (newFeature && !formData.features.includes(newFeature)) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature]
      }))
    }
  }

  const removeFeature = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter(f => f !== feature)
    }))
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          🚀 Real AI Mobile App Generator
        </h1>
        <p className="text-lg text-gray-600 mb-6">
          Generate complete, functional mobile applications using local AI (Ollama)
        </p>
        
        {/* Ollama Status */}
        <div className="flex items-center justify-center gap-4 mb-6">
          <Button 
            onClick={checkOllamaStatus} 
            variant="outline"
            disabled={ollamaStatus === 'checking'}
          >
            {ollamaStatus === 'checking' && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            Check AI Service Status
          </Button>
          
          <div className="flex items-center gap-2">
            {ollamaStatus === 'online' && <CheckCircle className="w-5 h-5 text-green-500" />}
            {ollamaStatus === 'offline' && <XCircle className="w-5 h-5 text-red-500" />}
            {ollamaStatus === 'checking' && <Loader2 className="w-5 h-5 animate-spin text-blue-500" />}
            <span className={`font-medium ${
              ollamaStatus === 'online' ? 'text-green-600' : 
              ollamaStatus === 'offline' ? 'text-red-600' : 'text-blue-600'
            }`}>
              {ollamaStatus === 'online' ? 'AI Ready' : 
               ollamaStatus === 'offline' ? 'AI Offline' : 'Checking...'}
            </span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Configuration Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="w-5 h-5" />
              App Configuration
            </CardTitle>
            <CardDescription>
              Configure your mobile application parameters
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">App Name</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter app name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Description</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe your app functionality"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Platform</label>
              <Select value={formData.platform} onValueChange={(value) => 
                setFormData(prev => ({ ...prev, platform: value }))
              }>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ANDROID">Android (Kotlin)</SelectItem>
                  <SelectItem value="IOS">iOS (Swift)</SelectItem>
                  <SelectItem value="CROSS_PLATFORM">Cross-Platform (React Native)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">UI Style</label>
              <Input
                value={formData.uiStyle}
                onChange={(e) => setFormData(prev => ({ ...prev, uiStyle: e.target.value }))}
                placeholder="e.g., Modern Material Design"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Color Scheme</label>
              <Input
                value={formData.colorScheme}
                onChange={(e) => setFormData(prev => ({ ...prev, colorScheme: e.target.value }))}
                placeholder="e.g., Blue and White"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Target Audience</label>
              <Input
                value={formData.targetAudience}
                onChange={(e) => setFormData(prev => ({ ...prev, targetAudience: e.target.value }))}
                placeholder="e.g., Business professionals"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Features</label>
              <div className="flex flex-wrap gap-2 mb-2">
                {formData.features.map((feature) => (
                  <Badge key={feature} variant="secondary" className="cursor-pointer" 
                         onClick={() => removeFeature(feature)}>
                    {feature} ×
                  </Badge>
                ))}
              </div>
              <Button onClick={addFeature} variant="outline" size="sm">
                Add Feature
              </Button>
            </div>

            <Button 
              onClick={generateApp} 
              disabled={isGenerating || ollamaStatus !== 'online'}
              className="w-full"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Generating Real App...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Generate Real Mobile App
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Results */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="w-5 h-5" />
              Generation Results
            </CardTitle>
            <CardDescription>
              Real-time generation progress and results
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isGenerating && (
              <div className="text-center py-8">
                <Loader2 className="w-12 h-12 animate-spin mx-auto mb-4 text-blue-500" />
                <p className="text-lg font-medium">Generating your mobile app...</p>
                <p className="text-sm text-gray-500 mt-2">
                  AI is creating architecture, screens, components, and code files
                </p>
              </div>
            )}

            {result && (
              <div className="space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="font-semibold text-green-800 mb-2">✅ Generation Successful!</h3>
                  <p className="text-green-700">{result.message}</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{result.stats.filesGenerated}</div>
                    <div className="text-sm text-blue-700">Files Generated</div>
                  </div>
                  <div className="bg-purple-50 p-3 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">{result.stats.linesOfCode}</div>
                    <div className="text-sm text-purple-700">Lines of Code</div>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{result.stats.platform}</div>
                    <div className="text-sm text-green-700">Platform</div>
                  </div>
                  <div className="bg-orange-50 p-3 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">{result.stats.features}</div>
                    <div className="text-sm text-orange-700">Features</div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold mb-2">Generated Project:</h4>
                  <p className="text-sm text-gray-600">Project ID: {result.project.id}</p>
                  <p className="text-sm text-gray-600">Status: {result.project.status}</p>
                  <p className="text-sm text-gray-600">Version: {result.project.version}</p>
                </div>
              </div>
            )}

            {!isGenerating && !result && (
              <div className="text-center py-8 text-gray-500">
                <Code className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Configure your app and click "Generate" to see results</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
