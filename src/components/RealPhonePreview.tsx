import React from 'react'

interface RealPhonePreviewProps {
  app: any
}

export default function RealPhonePreview({ app }: RealPhonePreviewProps) {
  if (!app) return null

  const renderStatusBar = () => (
    <div className="h-6 bg-gray-900 flex items-center justify-between px-4 text-white text-xs">
      <span>9:41</span>
      <div className="flex gap-1">
        <div className="w-4 h-2 bg-white rounded-sm"></div>
        <div className="w-1 h-2 bg-white rounded-sm"></div>
        <div className="w-6 h-2 bg-white rounded-sm"></div>
      </div>
    </div>
  )

  const renderAndroidPreview = () => (
    <div className="h-full flex flex-col">
      {renderStatusBar()}
      
      {/* App Bar */}
      <div className="h-12 bg-blue-500 flex items-center px-4">
        <h1 className="text-white font-medium text-sm">{app.name}</h1>
      </div>
      
      {/* Content */}
      <div className="flex-1 p-4 bg-white">
        <div className="text-center mt-16">
          <div className="w-16 h-16 bg-blue-500 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 2L3 7v11h4v-6h6v6h4V7l-7-5z"/>
            </svg>
          </div>
          <h2 className="text-lg font-bold text-gray-900 mb-2">{app.name}</h2>
          <p className="text-sm text-gray-600 mb-6 px-4">{app.description}</p>
          
          {app.status === 'BUILDING' && (
            <div className="mb-4">
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${app.buildProgress || 0}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500">Building... {app.buildProgress || 0}%</p>
            </div>
          )}
          
          <button className="bg-blue-500 text-white px-6 py-2 rounded-md text-sm font-medium">
            Get Started
          </button>
        </div>
      </div>
      
      {/* Navigation Bar */}
      <div className="h-12 bg-gray-100 border-t border-gray-200 flex items-center justify-around">
        <div className="w-6 h-6 bg-blue-500 rounded"></div>
        <div className="w-6 h-6 bg-gray-300 rounded"></div>
        <div className="w-6 h-6 bg-gray-300 rounded"></div>
      </div>
    </div>
  )

  const renderIOSPreview = () => (
    <div className="h-full flex flex-col">
      {renderStatusBar()}
      
      {/* Navigation Bar */}
      <div className="h-12 bg-gray-50 border-b border-gray-200 flex items-center justify-center">
        <h1 className="text-gray-900 font-semibold text-base">{app.name}</h1>
      </div>
      
      {/* Content */}
      <div className="flex-1 p-4 bg-gray-50">
        <div className="text-center mt-20">
          <div className="w-20 h-20 bg-blue-500 rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg">
            <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 2L3 7v11h4v-6h6v6h4V7l-7-5z"/>
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-3">{app.name}</h2>
          <p className="text-base text-gray-600 mb-8 px-4 leading-relaxed">{app.description}</p>
          
          {app.status === 'BUILDING' && (
            <div className="mb-6">
              <div className="w-full bg-gray-200 rounded-full h-1 mb-3">
                <div 
                  className="bg-blue-500 h-1 rounded-full transition-all duration-500"
                  style={{ width: `${app.buildProgress || 0}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500">Building... {app.buildProgress || 0}%</p>
            </div>
          )}
          
          <button className="bg-blue-500 text-white px-8 py-3 rounded-lg text-base font-semibold shadow-md">
            Get Started
          </button>
        </div>
      </div>
      
      {/* Tab Bar */}
      <div className="h-20 bg-gray-50 border-t border-gray-200 flex items-center justify-around pt-2">
        <div className="flex flex-col items-center">
          <div className="w-6 h-6 bg-blue-500 rounded mb-1"></div>
          <span className="text-xs text-blue-500">Home</span>
        </div>
        <div className="flex flex-col items-center">
          <div className="w-6 h-6 bg-gray-300 rounded mb-1"></div>
          <span className="text-xs text-gray-400">Search</span>
        </div>
        <div className="flex flex-col items-center">
          <div className="w-6 h-6 bg-gray-300 rounded mb-1"></div>
          <span className="text-xs text-gray-400">Profile</span>
        </div>
      </div>
    </div>
  )

  const renderCrossPlatformPreview = () => (
    <div className="h-full flex flex-col">
      {renderStatusBar()}
      
      {/* Header */}
      <div className="h-14 bg-purple-600 flex items-center px-4">
        <h1 className="text-white font-medium text-base">{app.name}</h1>
      </div>
      
      {/* Content */}
      <div className="flex-1 p-4 bg-white">
        <div className="text-center mt-16">
          <div className="w-18 h-18 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl mx-auto mb-4 flex items-center justify-center">
            <svg className="w-9 h-9 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 2L3 7v11h4v-6h6v6h4V7l-7-5z"/>
            </svg>
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">{app.name}</h2>
          <p className="text-sm text-gray-600 mb-6 px-3">{app.description}</p>
          
          {app.status === 'BUILDING' && (
            <div className="mb-4">
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div 
                  className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${app.buildProgress || 0}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500">Building... {app.buildProgress || 0}%</p>
            </div>
          )}
          
          <button className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-2 rounded-lg text-sm font-medium">
            Get Started
          </button>
        </div>
      </div>
    </div>
  )

  const renderBuildingState = () => (
    <div className="h-full flex flex-col">
      {renderStatusBar()}
      
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Building {app.name}</h3>
          <p className="text-sm text-gray-600 mb-4">{app.platform} Application</p>
          
          <div className="w-48 bg-gray-200 rounded-full h-2 mb-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-500"
              style={{ width: `${app.buildProgress || 0}%` }}
            ></div>
          </div>
          <p className="text-xs text-gray-500">{app.buildProgress || 0}% Complete</p>
          
          {app.metadata?.buildLogs && app.metadata.buildLogs.length > 0 && (
            <div className="mt-4 text-left">
              <p className="text-xs text-gray-600 font-mono">
                {app.metadata.buildLogs[app.metadata.buildLogs.length - 1]?.split('] ')[1] || 'Building...'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )

  // Show building state if app is still building
  if (app.status === 'BUILDING' && app.buildProgress < 100) {
    return renderBuildingState()
  }

  // Render platform-specific preview
  switch (app.platform) {
    case 'ANDROID':
      return renderAndroidPreview()
    case 'IOS':
      return renderIOSPreview()
    case 'CROSS_PLATFORM':
      return renderCrossPlatformPreview()
    default:
      return renderAndroidPreview()
  }
}
