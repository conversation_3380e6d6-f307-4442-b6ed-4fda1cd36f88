import { JsonStorage } from './json-storage';
import { OllamaService } from './ollama-service';

export interface GeneratedApp {
  id: string;
  userId: string;
  name: string;
  description: string;
  platform: 'ANDROID' | 'IOS' | 'CROSS_PLATFORM';
  status: 'GENERATING' | 'COMPLETED' | 'FAILED' | 'ARCHIVED';
  generatedCode: Record<string, string>;
  architecture: any;
  features: string[];
  metadata: {
    generationTime: number;
    linesOfCode: number;
    filesGenerated: number;
    aiModelUsed: string;
    userFeedback?: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface GenerationRequest {
  name: string;
  description: string;
  platform: 'ANDROID' | 'IOS' | 'CROSS_PLATFORM';
  features: string[];
  uiStyle?: string;
  colorScheme?: string;
  targetAudience?: string;
}

/**
 * Real service for managing app generation with AI
 */
export class AppGenerationService {
  private storage: JsonStorage;
  private ollamaService: OllamaService;

  constructor() {
    this.storage = new JsonStorage();
    this.ollamaService = new OllamaService();
    this.storage.seedInitialData();
  }

  /**
   * Generate a new mobile application
   */
  async generateApp(userId: string, request: GenerationRequest): Promise<GeneratedApp> {
    const startTime = Date.now();
    
    // Create initial app record
    const app = this.storage.insert('generated_apps', {
      userId,
      name: request.name,
      description: request.description,
      platform: request.platform,
      status: 'GENERATING',
      generatedCode: {},
      architecture: null,
      features: request.features,
      metadata: {
        generationTime: 0,
        linesOfCode: 0,
        filesGenerated: 0,
        aiModelUsed: 'unknown'
      }
    }) as GeneratedApp;

    try {
      // Check if AI is available
      const isAIAvailable = await this.ollamaService.checkHealth();
      let architecture;
      let generatedCode: Record<string, string> = {};
      let aiModelUsed = 'fallback';

      if (isAIAvailable) {
        console.log('🤖 Using real AI for generation');
        
        // Generate architecture with AI
        architecture = await this.ollamaService.generateAppArchitecture({
          name: request.name,
          description: request.description,
          platform: request.platform,
          features: request.features,
          uiStyle: request.uiStyle || 'Modern',
          colorScheme: request.colorScheme || 'Blue',
          targetAudience: request.targetAudience || 'General'
        });

        // Generate code files with AI
        generatedCode = await this.generateCodeFiles(request, architecture);
        aiModelUsed = 'qwen2.5-coder';
        
      } else {
        console.log('⚡ Using professional fallback generation');
        
        // Use professional fallback generation
        architecture = this.generateFallbackArchitecture(request);
        generatedCode = this.generateFallbackCode(request, architecture);
        aiModelUsed = 'professional-fallback';
      }

      // Calculate metrics
      const linesOfCode = Object.values(generatedCode).reduce((total, code) => {
        return total + code.split('\n').length;
      }, 0);

      const filesGenerated = Object.keys(generatedCode).length;
      const generationTime = Date.now() - startTime;

      // Update app with results
      const updatedApp = this.storage.update('generated_apps', app.id, {
        status: 'COMPLETED',
        generatedCode,
        architecture,
        metadata: {
          generationTime,
          linesOfCode,
          filesGenerated,
          aiModelUsed
        }
      }) as GeneratedApp;

      // Log learning data
      this.logLearningData(aiModelUsed, request, {
        success: true,
        generationTime,
        linesOfCode,
        filesGenerated
      });

      return updatedApp;

    } catch (error) {
      console.error('App generation failed:', error);
      
      // Update app with error status
      const failedApp = this.storage.update('generated_apps', app.id, {
        status: 'FAILED',
        metadata: {
          ...app.metadata,
          generationTime: Date.now() - startTime,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }) as GeneratedApp;

      // Log learning data for failure
      this.logLearningData('unknown', request, {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      throw error;
    }
  }

  /**
   * Generate code files using AI or fallback
   */
  private async generateCodeFiles(request: GenerationRequest, architecture: any): Promise<Record<string, string>> {
    const files: Record<string, string> = {};

    switch (request.platform) {
      case 'ANDROID':
        files['MainActivity.kt'] = await this.generateAndroidMainActivity(request, architecture);
        files['build.gradle'] = this.generateAndroidBuildGradle(request);
        files['AndroidManifest.xml'] = this.generateAndroidManifest(request);
        files['strings.xml'] = this.generateAndroidStrings(request);
        files['activity_main.xml'] = this.generateAndroidLayout(request);
        break;
        
      case 'IOS':
        files['ContentView.swift'] = await this.generateIOSContentView(request, architecture);
        files['App.swift'] = this.generateIOSApp(request);
        files['Info.plist'] = this.generateIOSInfoPlist(request);
        break;
        
      case 'CROSS_PLATFORM':
        files['App.tsx'] = await this.generateReactNativeApp(request, architecture);
        files['package.json'] = this.generateReactNativePackageJson(request);
        files['metro.config.js'] = this.generateReactNativeMetroConfig();
        break;
    }

    return files;
  }

  /**
   * Generate professional fallback architecture
   */
  private generateFallbackArchitecture(request: GenerationRequest): any {
    return {
      name: request.name,
      platform: request.platform,
      architecture: 'MVVM',
      navigation: request.platform === 'ANDROID' ? 'Navigation Component' : 
                  request.platform === 'IOS' ? 'SwiftUI Navigation' : 'React Navigation',
      database: request.platform === 'ANDROID' ? 'Room' : 
                request.platform === 'IOS' ? 'Core Data' : 'AsyncStorage',
      networking: 'REST API with Retrofit/URLSession/Axios',
      authentication: 'JWT Token Based',
      features: request.features,
      screens: this.generateScreensForFeatures(request.features),
      components: this.generateComponentsForFeatures(request.features)
    };
  }

  /**
   * Generate professional fallback code
   */
  private generateFallbackCode(request: GenerationRequest, architecture: any): Record<string, string> {
    const files: Record<string, string> = {};

    switch (request.platform) {
      case 'ANDROID':
        files['MainActivity.kt'] = this.generateProfessionalAndroidCode(request, architecture);
        files['build.gradle'] = this.generateAndroidBuildGradle(request);
        files['AndroidManifest.xml'] = this.generateAndroidManifest(request);
        break;
        
      case 'IOS':
        files['ContentView.swift'] = this.generateProfessionalIOSCode(request, architecture);
        files['App.swift'] = this.generateIOSApp(request);
        break;
        
      case 'CROSS_PLATFORM':
        files['App.tsx'] = this.generateProfessionalReactNativeCode(request, architecture);
        files['package.json'] = this.generateReactNativePackageJson(request);
        break;
    }

    return files;
  }

  /**
   * Generate screens based on features
   */
  private generateScreensForFeatures(features: string[]): string[] {
    const screens = ['HomeScreen', 'ProfileScreen'];
    
    if (features.includes('authentication') || features.includes('user-authentication')) {
      screens.push('LoginScreen', 'RegisterScreen');
    }
    
    if (features.includes('settings')) {
      screens.push('SettingsScreen');
    }
    
    if (features.includes('notifications') || features.includes('push-notifications')) {
      screens.push('NotificationsScreen');
    }
    
    return screens;
  }

  /**
   * Generate components based on features
   */
  private generateComponentsForFeatures(features: string[]): string[] {
    const components = ['Header', 'Footer', 'LoadingSpinner'];
    
    if (features.includes('user-authentication')) {
      components.push('LoginForm', 'UserAvatar');
    }
    
    if (features.includes('real-time-sync')) {
      components.push('SyncIndicator');
    }
    
    return components;
  }

  /**
   * Log learning data for AI improvement
   */
  private logLearningData(modelId: string, request: GenerationRequest, result: any): void {
    this.storage.insert('ai_learning_data', {
      model_id: modelId,
      interaction_type: 'app-generation',
      input_data: {
        platform: request.platform,
        features: request.features,
        description: request.description
      },
      output_data: result,
      processing_time: result.generationTime || 0,
      success: result.success || false
    });
  }

  /**
   * Get all generated apps for a user
   */
  getUserApps(userId: string): GeneratedApp[] {
    return this.storage.find('generated_apps', app => app.userId === userId);
  }

  /**
   * Get app by ID
   */
  getAppById(appId: string): GeneratedApp | null {
    return this.storage.findById('generated_apps', appId);
  }

  /**
   * Get generation statistics
   */
  getGenerationStats(): any {
    const apps = this.storage.getAll('generated_apps');
    const learningData = this.storage.getAll('ai_learning_data');

    return {
      totalApps: apps.length,
      completedApps: apps.filter(app => app.status === 'COMPLETED').length,
      failedApps: apps.filter(app => app.status === 'FAILED').length,
      averageGenerationTime: apps.reduce((sum, app) => sum + (app.metadata.generationTime || 0), 0) / apps.length,
      totalLinesGenerated: apps.reduce((sum, app) => sum + (app.metadata.linesOfCode || 0), 0),
      platformDistribution: {
        android: apps.filter(app => app.platform === 'ANDROID').length,
        ios: apps.filter(app => app.platform === 'IOS').length,
        crossPlatform: apps.filter(app => app.platform === 'CROSS_PLATFORM').length
      },
      aiLearningInteractions: learningData.length
    };
  }

  // Professional code generation methods
  private async generateAndroidMainActivity(request: GenerationRequest, architecture: any): Promise<string> {
    try {
      const prompt = `Generate a complete MainActivity.kt for ${request.name} with features: ${request.features.join(', ')}`;
      return await this.ollamaService.generateCode(prompt);
    } catch (error) {
      return this.generateProfessionalAndroidCode(request, architecture);
    }
  }

  private generateProfessionalAndroidCode(request: GenerationRequest, architecture: any): string {
    const packageName = `com.androidweb.${request.name.toLowerCase().replace(/\s+/g, '')}`;

    return `package ${packageName}

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.findNavController
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.navigation.ui.setupWithNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import androidx.lifecycle.ViewModelProvider
import androidx.room.Room

/**
 * ${request.name} - Professional Android Application
 * Generated by AndroidWeb Enterprise Platform
 *
 * Features: ${request.features.join(', ')}
 * Architecture: ${architecture.architecture}
 * Database: ${architecture.database}
 */
class MainActivity : AppCompatActivity() {

    private lateinit var appBarConfiguration: AppBarConfiguration
    private lateinit var viewModel: MainViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // Initialize ViewModel
        viewModel = ViewModelProvider(this)[MainViewModel::class.java]

        // Setup Navigation
        setupNavigation()

        // Initialize Services
        initializeServices()

        // Setup Authentication if required
        ${request.features.includes('authentication') || request.features.includes('user-authentication') ?
          'setupAuthentication()' : '// No authentication required'}

        // Setup Push Notifications if required
        ${request.features.includes('notifications') || request.features.includes('push-notifications') ?
          'setupPushNotifications()' : '// No push notifications required'}
    }

    private fun setupNavigation() {
        val navView: BottomNavigationView = findViewById(R.id.nav_view)
        val navController = findNavController(R.id.nav_host_fragment)

        appBarConfiguration = AppBarConfiguration(
            setOf(${architecture.screens.slice(0, 3).map((screen: string) => `R.id.navigation_${screen.toLowerCase().replace('screen', '')}`).join(', ')})
        )

        setupActionBarWithNavController(navController, appBarConfiguration)
        navView.setupWithNavController(navController)
    }

    private fun initializeServices() {
        // Initialize database
        val database = Room.databaseBuilder(
            applicationContext,
            AppDatabase::class.java,
            "${request.name.toLowerCase()}_database"
        ).build()

        // Initialize repository
        val repository = AppRepository(database.appDao())

        // Initialize network service
        val networkService = NetworkService.getInstance()

        // Setup real-time sync if required
        ${request.features.includes('real-time-sync') ?
          'setupRealTimeSync(repository, networkService)' : '// No real-time sync required'}
    }

    ${request.features.includes('authentication') || request.features.includes('user-authentication') ? `
    private fun setupAuthentication() {
        // Initialize Firebase Auth or custom auth
        val authService = AuthenticationService.getInstance()

        // Check if user is logged in
        if (!authService.isUserLoggedIn()) {
            // Navigate to login screen
            startActivity(Intent(this, LoginActivity::class.java))
            finish()
        }
    }` : ''}

    ${request.features.includes('notifications') || request.features.includes('push-notifications') ? `
    private fun setupPushNotifications() {
        // Initialize FCM or custom notification service
        val notificationService = NotificationService.getInstance()
        notificationService.initialize(this)

        // Request notification permissions (Android 13+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPermissions(arrayOf(Manifest.permission.POST_NOTIFICATIONS), 1001)
        }
    }` : ''}

    ${request.features.includes('real-time-sync') ? `
    private fun setupRealTimeSync(repository: AppRepository, networkService: NetworkService) {
        // Setup WebSocket or Firebase Realtime Database
        val syncService = SyncService(repository, networkService)
        syncService.startSync()

        // Observe connectivity changes
        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                syncService.resumeSync()
            }

            override fun onLost(network: Network) {
                syncService.pauseSync()
            }
        }

        connectivityManager.registerDefaultNetworkCallback(networkCallback)
    }` : ''}

    override fun onSupportNavigateUp(): Boolean {
        val navController = findNavController(R.id.nav_host_fragment)
        return navController.navigateUp(appBarConfiguration) || super.onSupportNavigateUp()
    }

    override fun onDestroy() {
        super.onDestroy()
        // Cleanup resources
        viewModel.cleanup()
    }
}

/**
 * Main ViewModel for ${request.name}
 */
class MainViewModel : ViewModel() {
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    fun cleanup() {
        // Cleanup resources
    }
}`;
  }

  private generateAndroidBuildGradle(request: GenerationRequest): string {
    return `plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    ${request.features.includes('notifications') || request.features.includes('push-notifications') ?
      "id 'com.google.gms.google-services'" : ''}
    id 'kotlin-kapt'
}

android {
    namespace 'com.androidweb.${request.name.toLowerCase().replace(/\s+/g, '')}'
    compileSdk 34

    defaultConfig {
        applicationId "com.androidweb.${request.name.toLowerCase().replace(/\s+/g, '')}"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    buildFeatures {
        viewBinding true
        dataBinding true
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    // Navigation
    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.6'
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.6'

    // ViewModel and LiveData
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'

    ${request.features.includes('database') || request.features.includes('offline-mode') ? `
    // Room Database
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'` : ''}

    ${request.features.includes('networking') || request.features.includes('api') ? `
    // Networking
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'` : ''}

    ${request.features.includes('authentication') || request.features.includes('user-authentication') ? `
    // Authentication
    implementation 'androidx.biometric:biometric:1.1.0'` : ''}

    ${request.features.includes('notifications') || request.features.includes('push-notifications') ? `
    // Firebase
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
    implementation 'com.google.firebase:firebase-messaging-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'` : ''}

    ${request.features.includes('real-time-sync') ? `
    // WebSocket
    implementation 'org.java-websocket:Java-WebSocket:1.5.3'` : ''}

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}`;
  }

  private generateAndroidManifest(request: GenerationRequest): string {
    return `<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Network permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    ${request.features.includes('notifications') || request.features.includes('push-notifications') ? `
    <!-- Notification permissions -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />` : ''}

    ${request.features.includes('file-upload') || request.features.includes('camera') ? `
    <!-- Storage and camera permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />` : ''}

    ${request.features.includes('location') ? `
    <!-- Location permissions -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />` : ''}

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.${request.name.replace(/\s+/g, '')}"
        tools:targetApi="31">

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.${request.name.replace(/\s+/g, '')}.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        ${request.features.includes('authentication') || request.features.includes('user-authentication') ? `
        <activity
            android:name=".auth.LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.${request.name.replace(/\s+/g, '')}.NoActionBar" />

        <activity
            android:name=".auth.RegisterActivity"
            android:exported="false"
            android:theme="@style/Theme.${request.name.replace(/\s+/g, '')}.NoActionBar" />` : ''}

        ${request.features.includes('notifications') || request.features.includes('push-notifications') ? `
        <service
            android:name=".services.NotificationService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>` : ''}

        ${request.features.includes('real-time-sync') ? `
        <service
            android:name=".services.SyncService"
            android:exported="false" />` : ''}

    </application>

</manifest>`;
  }

  private generateAndroidStrings(request: GenerationRequest): string {
    return `<resources>
    <string name="app_name">${request.name}</string>
    <string name="app_description">${request.description}</string>

    <!-- Navigation -->
    <string name="title_home">Home</string>
    <string name="title_dashboard">Dashboard</string>
    <string name="title_profile">Profile</string>
    <string name="title_settings">Settings</string>

    ${request.features.includes('authentication') || request.features.includes('user-authentication') ? `
    <!-- Authentication -->
    <string name="login_title">Login</string>
    <string name="register_title">Register</string>
    <string name="email_hint">Email</string>
    <string name="password_hint">Password</string>
    <string name="login_button">Login</string>
    <string name="register_button">Register</string>
    <string name="forgot_password">Forgot Password?</string>` : ''}

    ${request.features.includes('notifications') || request.features.includes('push-notifications') ? `
    <!-- Notifications -->
    <string name="notification_title">New Notification</string>
    <string name="notification_channel_name">App Notifications</string>
    <string name="notification_channel_description">Notifications from ${request.name}</string>` : ''}

    <!-- Common -->
    <string name="loading">Loading...</string>
    <string name="error_generic">Something went wrong. Please try again.</string>
    <string name="error_network">Network error. Please check your connection.</string>
    <string name="retry">Retry</string>
    <string name="cancel">Cancel</string>
    <string name="ok">OK</string>
    <string name="save">Save</string>
    <string name="delete">Delete</string>
    <string name="edit">Edit</string>

</resources>`;
  }

  private generateAndroidLayout(request: GenerationRequest): string {
    return `<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/nav_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:background="?android:attr/windowBackground"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:menu="@menu/bottom_nav_menu" />

    <fragment
        android:id="@+id/nav_host_fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:defaultNavHost="true"
        app:layout_constraintBottom_toTopOf="@id/nav_view"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:navGraph="@navigation/mobile_navigation" />

</androidx.constraintlayout.widget.ConstraintLayout>`;
  }

  // iOS Code Generation Methods
  private async generateIOSContentView(request: GenerationRequest, architecture: any): Promise<string> {
    try {
      const prompt = `Generate a SwiftUI ContentView for ${request.name} with features: ${request.features.join(', ')}`;
      return await this.ollamaService.generateCode(prompt);
    } catch (error) {
      return this.generateProfessionalIOSCode(request, architecture);
    }
  }

  private generateProfessionalIOSCode(request: GenerationRequest, architecture: any): string {
    return `import SwiftUI
import Combine

/**
 * ${request.name} - Professional iOS Application
 * Generated by AndroidWeb Enterprise Platform
 *
 * Features: ${request.features.join(', ')}
 * Architecture: ${architecture.architecture}
 * Database: ${architecture.database}
 */

struct ContentView: View {
    @StateObject private var viewModel = MainViewModel()
    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .tabItem {
                    Image(systemName: "house")
                    Text("Home")
                }
                .tag(0)

            DashboardView()
                .tabItem {
                    Image(systemName: "chart.bar")
                    Text("Dashboard")
                }
                .tag(1)

            ProfileView()
                .tabItem {
                    Image(systemName: "person")
                    Text("Profile")
                }
                .tag(2)

            ${request.features.includes('settings') ? `
            SettingsView()
                .tabItem {
                    Image(systemName: "gear")
                    Text("Settings")
                }
                .tag(3)` : ''}
        }
        .onAppear {
            viewModel.initialize()
        }
        ${request.features.includes('authentication') || request.features.includes('user-authentication') ? `
        .sheet(isPresented: $viewModel.showLogin) {
            LoginView()
        }` : ''}
    }
}

// MARK: - Main ViewModel
class MainViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showLogin = false

    private var cancellables = Set<AnyCancellable>()

    func initialize() {
        ${request.features.includes('authentication') || request.features.includes('user-authentication') ? `
        // Check authentication status
        if !AuthenticationService.shared.isLoggedIn {
            showLogin = true
        }` : ''}

        ${request.features.includes('notifications') || request.features.includes('push-notifications') ? `
        // Setup push notifications
        NotificationService.shared.requestPermission()` : ''}

        ${request.features.includes('real-time-sync') ? `
        // Start real-time sync
        SyncService.shared.startSync()` : ''}
    }
}

// MARK: - Views
struct HomeView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Welcome to ${request.name}")
                    .font(.largeTitle)
                    .padding()

                Text("${request.description}")
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .padding()

                Spacer()
            }
            .navigationTitle("Home")
        }
    }
}

struct DashboardView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Dashboard")
                    .font(.largeTitle)
                    .padding()

                // Add dashboard content here

                Spacer()
            }
            .navigationTitle("Dashboard")
        }
    }
}

struct ProfileView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Profile")
                    .font(.largeTitle)
                    .padding()

                // Add profile content here

                Spacer()
            }
            .navigationTitle("Profile")
        }
    }
}

${request.features.includes('settings') ? `
struct SettingsView: View {
    var body: some View {
        NavigationView {
            List {
                Section("General") {
                    NavigationLink("Account", destination: Text("Account Settings"))
                    NavigationLink("Privacy", destination: Text("Privacy Settings"))
                    NavigationLink("Notifications", destination: Text("Notification Settings"))
                }

                Section("About") {
                    HStack {
                        Text("Version")
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Settings")
        }
    }
}` : ''}

${request.features.includes('authentication') || request.features.includes('user-authentication') ? `
struct LoginView: View {
    @State private var email = ""
    @State private var password = ""
    @Environment(\\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Login to ${request.name}")
                    .font(.largeTitle)
                    .padding()

                TextField("Email", text: $email)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(.emailAddress)
                    .autocapitalization(.none)

                SecureField("Password", text: $password)
                    .textFieldStyle(RoundedBorderTextFieldStyle())

                Button("Login") {
                    // Handle login
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
                .disabled(email.isEmpty || password.isEmpty)

                Spacer()
            }
            .padding()
            .navigationTitle("Login")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}` : ''}

#Preview {
    ContentView()
}`;
  }

  private generateIOSApp(request: GenerationRequest): string {
    return `import SwiftUI

@main
struct ${request.name.replace(/\s+/g, '')}App: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}`;
  }

  private generateIOSInfoPlist(request: GenerationRequest): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>${request.name}</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>$(PRODUCT_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundleVersion</key>
    <string>1</string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>UIApplicationSceneManifest</key>
    <dict>
        <key>UIApplicationSupportsMultipleScenes</key>
        <true/>
    </dict>
    <key>UIApplicationSupportsIndirectInputEvents</key>
    <true/>
    <key>UILaunchScreen</key>
    <dict/>
    <key>UIRequiredDeviceCapabilities</key>
    <array>
        <string>armv7</string>
    </array>
    <key>UISupportedInterfaceOrientations</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationPortraitUpsideDown</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    ${request.features.includes('camera') || request.features.includes('file-upload') ? `
    <key>NSCameraUsageDescription</key>
    <string>${request.name} needs camera access to capture photos</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>${request.name} needs photo library access to select images</string>` : ''}
    ${request.features.includes('location') ? `
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>${request.name} needs location access to provide location-based features</string>` : ''}
    ${request.features.includes('notifications') || request.features.includes('push-notifications') ? `
    <key>UIBackgroundModes</key>
    <array>
        <string>remote-notification</string>
    </array>` : ''}
</dict>
</plist>`;
  }

  // React Native Code Generation Methods
  private async generateReactNativeApp(request: GenerationRequest, architecture: any): Promise<string> {
    try {
      const prompt = `Generate a React Native App.tsx for ${request.name} with features: ${request.features.join(', ')}`;
      return await this.ollamaService.generateCode(prompt);
    } catch (error) {
      return this.generateProfessionalReactNativeCode(request, architecture);
    }
  }

  private generateProfessionalReactNativeCode(request: GenerationRequest, architecture: any): string {
    return `import React, { useEffect, useState } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  View,
  Alert,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

/**
 * ${request.name} - Professional React Native Application
 * Generated by AndroidWeb Enterprise Platform
 *
 * Features: ${request.features.join(', ')}
 * Architecture: ${architecture.architecture}
 * Database: ${architecture.database}
 */

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Main App Component
const App = (): JSX.Element => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      ${request.features.includes('authentication') || request.features.includes('user-authentication') ? `
      // Check authentication status
      const authStatus = await checkAuthenticationStatus();
      setIsAuthenticated(authStatus);` : ''}

      ${request.features.includes('notifications') || request.features.includes('push-notifications') ? `
      // Setup push notifications
      await setupPushNotifications();` : ''}

      ${request.features.includes('real-time-sync') ? `
      // Initialize real-time sync
      await initializeRealTimeSync();` : ''}

      setIsLoading(false);
    } catch (error) {
      console.error('App initialization failed:', error);
      Alert.alert('Error', 'Failed to initialize app');
      setIsLoading(false);
    }
  };

  ${request.features.includes('authentication') || request.features.includes('user-authentication') ? `
  const checkAuthenticationStatus = async (): Promise<boolean> => {
    // Implement authentication check
    return false; // Replace with actual auth check
  };` : ''}

  ${request.features.includes('notifications') || request.features.includes('push-notifications') ? `
  const setupPushNotifications = async () => {
    // Implement push notification setup
    console.log('Setting up push notifications...');
  };` : ''}

  ${request.features.includes('real-time-sync') ? `
  const initializeRealTimeSync = async () => {
    // Implement real-time sync initialization
    console.log('Initializing real-time sync...');
  };` : ''}

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading ${request.name}...</Text>
      </SafeAreaView>
    );
  }

  ${request.features.includes('authentication') || request.features.includes('user-authentication') ? `
  if (!isAuthenticated) {
    return (
      <NavigationContainer>
        <Stack.Navigator>
          <Stack.Screen name="Login" component={LoginScreen} />
          <Stack.Screen name="Register" component={RegisterScreen} />
        </Stack.Navigator>
      </NavigationContainer>
    );
  }` : ''}

  return (
    <NavigationContainer>
      <StatusBar barStyle="dark-content" />
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused, color, size }) => {
            let iconName = 'home';

            switch (route.name) {
              case 'Home':
                iconName = 'home';
                break;
              case 'Dashboard':
                iconName = 'dashboard';
                break;
              case 'Profile':
                iconName = 'person';
                break;
              case 'Settings':
                iconName = 'settings';
                break;
            }

            return <Icon name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: '#007AFF',
          tabBarInactiveTintColor: 'gray',
        })}>
        <Tab.Screen name="Home" component={HomeScreen} />
        <Tab.Screen name="Dashboard" component={DashboardScreen} />
        <Tab.Screen name="Profile" component={ProfileScreen} />
        ${request.features.includes('settings') ? `
        <Tab.Screen name="Settings" component={SettingsScreen} />` : ''}
      </Tab.Navigator>
    </NavigationContainer>
  );
};

// Screen Components
const HomeScreen = (): JSX.Element => {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentInsetAdjustmentBehavior="automatic">
        <View style={styles.section}>
          <Text style={styles.title}>Welcome to ${request.name}</Text>
          <Text style={styles.description}>${request.description}</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const DashboardScreen = (): JSX.Element => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.title}>Dashboard</Text>
        <Text style={styles.description}>Your dashboard content goes here</Text>
      </View>
    </SafeAreaView>
  );
};

const ProfileScreen = (): JSX.Element => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.title}>Profile</Text>
        <Text style={styles.description}>Your profile information</Text>
      </View>
    </SafeAreaView>
  );
};

${request.features.includes('settings') ? `
const SettingsScreen = (): JSX.Element => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.title}>Settings</Text>
        <Text style={styles.description}>App settings and preferences</Text>
      </View>
    </SafeAreaView>
  );
};` : ''}

${request.features.includes('authentication') || request.features.includes('user-authentication') ? `
const LoginScreen = (): JSX.Element => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.title}>Login</Text>
        <Text style={styles.description}>Please login to continue</Text>
      </View>
    </SafeAreaView>
  );
};

const RegisterScreen = (): JSX.Element => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.title}>Register</Text>
        <Text style={styles.description}>Create a new account</Text>
      </View>
    </SafeAreaView>
  );
};` : ''}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    fontSize: 18,
    color: '#333',
  },
  section: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
});

export default App;`;
  }

  private generateReactNativePackageJson(request: GenerationRequest): string {
    return `{
  "name": "${request.name.toLowerCase().replace(/\s+/g, '-')}",
  "version": "1.0.0",
  "description": "${request.description}",
  "main": "index.js",
  "scripts": {
    "android": "react-native run-android",
    "ios": "react-native run-ios",
    "start": "react-native start",
    "test": "jest",
    "lint": "eslint . --ext .js,.jsx,.ts,.tsx"
  },
  "dependencies": {
    "react": "18.2.0",
    "react-native": "0.73.2",
    "@react-navigation/native": "^6.1.9",
    "@react-navigation/bottom-tabs": "^6.5.11",
    "@react-navigation/stack": "^6.3.20",
    "react-native-screens": "^3.29.0",
    "react-native-safe-area-context": "^4.8.2",
    "react-native-vector-icons": "^10.0.3",
    ${request.features.includes('authentication') || request.features.includes('user-authentication') ? `
    "@react-native-async-storage/async-storage": "^1.21.0",
    "react-native-keychain": "^8.1.3",` : ''}
    ${request.features.includes('networking') || request.features.includes('api') ? `
    "axios": "^1.6.5",` : ''}
    ${request.features.includes('notifications') || request.features.includes('push-notifications') ? `
    "@react-native-firebase/app": "^19.0.1",
    "@react-native-firebase/messaging": "^19.0.1",` : ''}
    ${request.features.includes('real-time-sync') ? `
    "socket.io-client": "^4.7.4",` : ''}
    ${request.features.includes('database') || request.features.includes('offline-mode') ? `
    "react-native-sqlite-storage": "^6.0.1",` : ''}
    ${request.features.includes('camera') || request.features.includes('file-upload') ? `
    "react-native-image-picker": "^7.1.0",` : ''}
    ${request.features.includes('location') ? `
    "@react-native-community/geolocation": "^3.2.1",` : ''}
    "react-native-gesture-handler": "^2.14.1"
  },
  "devDependencies": {
    "@babel/core": "^7.20.0",
    "@babel/preset-env": "^7.20.0",
    "@babel/runtime": "^7.20.0",
    "@react-native/eslint-config": "^0.73.1",
    "@react-native/metro-config": "^0.73.2",
    "@react-native/typescript-config": "^0.73.1",
    "@types/react": "^18.2.6",
    "@types/react-test-renderer": "^18.0.0",
    "babel-jest": "^29.6.3",
    "eslint": "^8.19.0",
    "jest": "^29.6.3",
    "metro-react-native-babel-preset": "0.76.8",
    "prettier": "2.8.8",
    "react-test-renderer": "18.2.0",
    "typescript": "5.0.4"
  },
  "engines": {
    "node": ">=18"
  }
}`;
  }

  private generateReactNativeMetroConfig(): string {
    return `const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 */
const config = {};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);`;
  }
}
