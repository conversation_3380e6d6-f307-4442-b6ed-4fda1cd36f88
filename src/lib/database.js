const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

class DatabaseManager {
  constructor() {
    // Ensure data directory exists
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    this.db = new Database(path.join(dataDir, 'androidweb.db'));
    this.initTables();
    this.seedInitialData();
  }

  initTables() {
    // Users table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        name TEXT NOT NULL,
        subscription_plan TEXT DEFAULT 'free',
        avatar_url TEXT,
        is_admin BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // AI Models table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS ai_models (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        status TEXT DEFAULT 'inactive',
        config TEXT,
        performance_score REAL DEFAULT 0.0,
        total_generations INTEGER DEFAULT 0,
        success_rate REAL DEFAULT 0.0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Generated Apps table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS generated_apps (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        name TEXT NOT NULL,
        description TEXT,
        platform TEXT NOT NULL,
        status TEXT DEFAULT 'generating',
        code_structure TEXT,
        preview_url TEXT,
        download_url TEXT,
        generation_time INTEGER DEFAULT 0,
        ai_model_id INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (ai_model_id) REFERENCES ai_models (id)
      )
    `);

    // AI Learning Data table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS ai_learning_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        model_id INTEGER,
        interaction_type TEXT NOT NULL,
        input_data TEXT,
        output_data TEXT,
        feedback_score INTEGER,
        processing_time INTEGER,
        success BOOLEAN DEFAULT 1,
        error_message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (model_id) REFERENCES ai_models (id)
      )
    `);

    // App Generation Sessions table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS app_generation_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        app_id INTEGER,
        chat_history TEXT,
        current_step TEXT,
        progress INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (app_id) REFERENCES generated_apps (id)
      )
    `);

    // AI Evolution Metrics table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS ai_evolution_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        model_id INTEGER,
        metric_type TEXT NOT NULL,
        metric_value REAL NOT NULL,
        comparison_baseline REAL,
        improvement_percentage REAL,
        recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (model_id) REFERENCES ai_models (id)
      )
    `);

    // User Feedback table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS user_feedback (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        app_id INTEGER,
        rating INTEGER CHECK(rating >= 1 AND rating <= 5),
        feedback_text TEXT,
        category TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (app_id) REFERENCES generated_apps (id)
      )
    `);
  }

  seedInitialData() {
    // Check if we already have data
    const userCount = this.db.prepare('SELECT COUNT(*) as count FROM users').get();
    if (userCount.count > 0) return;

    // Create admin user
    this.createUser('<EMAIL>', 'admin123', 'Admin User', 'premium', null, true);

    // Create sample AI models
    this.createAIModel('Qwen-2.5-Coder', 'code-generation', {
      model_path: 'qwen2.5-coder:latest',
      temperature: 0.7,
      max_tokens: 4096,
      specialization: 'mobile-app-development'
    });

    this.createAIModel('Qwen-2.5-Chat', 'conversation', {
      model_path: 'qwen2.5:latest',
      temperature: 0.8,
      max_tokens: 2048,
      specialization: 'user-interaction'
    });
  }

  // User methods
  createUser(email, password, name, plan = 'free', avatarUrl = null, isAdmin = false) {
    const stmt = this.db.prepare(`
      INSERT INTO users (email, password, name, subscription_plan, avatar_url, is_admin) 
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    return stmt.run(email, password, name, plan, avatarUrl, isAdmin ? 1 : 0);
  }

  getUserByEmail(email) {
    const stmt = this.db.prepare('SELECT * FROM users WHERE email = ?');
    return stmt.get(email);
  }

  getUserById(id) {
    const stmt = this.db.prepare('SELECT * FROM users WHERE id = ?');
    return stmt.get(id);
  }

  // AI Model methods
  createAIModel(name, type, config = null) {
    const stmt = this.db.prepare(`
      INSERT INTO ai_models (name, type, config) 
      VALUES (?, ?, ?)
    `);
    return stmt.run(name, type, JSON.stringify(config));
  }

  getAIModels() {
    const stmt = this.db.prepare('SELECT * FROM ai_models ORDER BY created_at DESC');
    return stmt.all();
  }

  updateAIModelStatus(id, status) {
    const stmt = this.db.prepare('UPDATE ai_models SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?');
    return stmt.run(status, id);
  }

  // Generated Apps methods
  createGeneratedApp(userId, name, description, platform) {
    const stmt = this.db.prepare(`
      INSERT INTO generated_apps (user_id, name, description, platform) 
      VALUES (?, ?, ?, ?)
    `);
    return stmt.run(userId, name, description, platform);
  }

  getGeneratedApps(userId = null) {
    let stmt;
    if (userId) {
      stmt = this.db.prepare('SELECT * FROM generated_apps WHERE user_id = ? ORDER BY created_at DESC');
      return stmt.all(userId);
    } else {
      stmt = this.db.prepare('SELECT * FROM generated_apps ORDER BY created_at DESC');
      return stmt.all();
    }
  }

  updateGeneratedApp(id, updates) {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    values.push(id);
    
    const stmt = this.db.prepare(`
      UPDATE generated_apps 
      SET ${fields}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `);
    return stmt.run(...values);
  }

  // AI Learning Data methods
  addLearningData(modelId, interactionType, inputData, outputData, feedbackScore = null, processingTime = null, success = true, errorMessage = null) {
    const stmt = this.db.prepare(`
      INSERT INTO ai_learning_data (model_id, interaction_type, input_data, output_data, feedback_score, processing_time, success, error_message) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    return stmt.run(modelId, interactionType, JSON.stringify(inputData), JSON.stringify(outputData), feedbackScore, processingTime, success ? 1 : 0, errorMessage);
  }

  getLearningData(modelId = null, limit = 100) {
    let stmt;
    if (modelId) {
      stmt = this.db.prepare('SELECT * FROM ai_learning_data WHERE model_id = ? ORDER BY created_at DESC LIMIT ?');
      return stmt.all(modelId, limit);
    } else {
      stmt = this.db.prepare('SELECT * FROM ai_learning_data ORDER BY created_at DESC LIMIT ?');
      return stmt.all(limit);
    }
  }

  // Evolution Metrics methods
  addEvolutionMetric(modelId, metricType, metricValue, comparisonBaseline = null, improvementPercentage = null) {
    const stmt = this.db.prepare(`
      INSERT INTO ai_evolution_metrics (model_id, metric_type, metric_value, comparison_baseline, improvement_percentage) 
      VALUES (?, ?, ?, ?, ?)
    `);
    return stmt.run(modelId, metricType, metricValue, comparisonBaseline, improvementPercentage);
  }

  getEvolutionMetrics(modelId = null, metricType = null) {
    let query = 'SELECT * FROM ai_evolution_metrics';
    let params = [];
    
    if (modelId && metricType) {
      query += ' WHERE model_id = ? AND metric_type = ?';
      params = [modelId, metricType];
    } else if (modelId) {
      query += ' WHERE model_id = ?';
      params = [modelId];
    } else if (metricType) {
      query += ' WHERE metric_type = ?';
      params = [metricType];
    }
    
    query += ' ORDER BY recorded_at DESC';
    
    const stmt = this.db.prepare(query);
    return stmt.all(...params);
  }

  close() {
    this.db.close();
  }
}

module.exports = DatabaseManager;
