import { OllamaService, AppArchitecture } from '../ollama-service'

export interface AndroidGenerationRequest {
  name: string
  description: string
  features: string[]
  architecture: AppArchitecture
  uiStyle?: string
  colorScheme?: string
  targetAudience?: string
}

export class AndroidCodeGenerator {
  constructor(private ollamaService: OllamaService) {}

  private async generateCodeWithFallback(prompt: string, fallbackCode: string): Promise<string> {
    try {
      return await this.ollamaService.generateCode(prompt)
    } catch (error) {
      console.log("Using fallback code generation")
      return fallbackCode
    }
  }

  async generateCompleteApp(request: AndroidGenerationRequest): Promise<Record<string, string>> {
    const generatedFiles: Record<string, string> = {}

    // Generate main files
    generatedFiles['MainActivity.kt'] = await this.generateMainActivity(request)
    generatedFiles['build.gradle'] = await this.generateBuildGradle(request)
    generatedFiles['AndroidManifest.xml'] = await this.generateManifest(request)
    
    // Generate screens
    for (const screen of request.architecture.screens) {
      generatedFiles[`${screen}Activity.kt`] = await this.generateScreenActivity(screen, request)
      generatedFiles[`activity_${screen.toLowerCase()}.xml`] = await this.generateScreenLayout(screen, request)
    }

    // Generate components
    for (const component of request.architecture.components) {
      generatedFiles[`${component}Component.kt`] = await this.generateComponent(component, request)
    }

    // Generate data models
    generatedFiles['DataModels.kt'] = await this.generateDataModels(request)
    
    // Generate API service
    generatedFiles['ApiService.kt'] = await this.generateApiService(request)
    
    // Generate database
    generatedFiles['DatabaseHelper.kt'] = await this.generateDatabase(request)

    // Generate styles and colors
    generatedFiles['styles.xml'] = await this.generateStyles(request)
    generatedFiles['colors.xml'] = await this.generateColors(request)

    return generatedFiles
  }

  private async generateMainActivity(request: AndroidGenerationRequest): Promise<string> {
    const prompt = `Generate a complete MainActivity.kt file for an Android app called "${request.name}".

Requirements:
- App description: ${request.description}
- Features: ${request.features.join(', ')}
- Navigation: ${request.architecture.navigation}
- UI Style: ${request.uiStyle || 'Modern Material Design'}
- Color Scheme: ${request.colorScheme || 'Blue'}

The MainActivity should:
1. Set up the main navigation
2. Initialize required services
3. Handle app lifecycle
4. Set up authentication if needed
5. Use modern Android development practices (Kotlin, ViewBinding, etc.)

Generate clean, production-ready Kotlin code with proper error handling and comments.`

    const fallbackCode = `package com.example.${request.name.toLowerCase().replace(/\s+/g, '')}

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.findNavController
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.navigation.ui.setupWithNavController
import com.google.android.material.bottomnavigation.BottomNavigationView

/**
 * MainActivity for ${request.name}
 *
 * This is the main entry point for the application.
 * It sets up the navigation and initializes required services.
 */
class MainActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        val navView: BottomNavigationView = findViewById(R.id.nav_view)
        val navController = findNavController(R.id.nav_host_fragment)

        val appBarConfiguration = AppBarConfiguration(
            setOf(R.id.navigation_home, R.id.navigation_dashboard, R.id.navigation_profile)
        )

        setupActionBarWithNavController(navController, appBarConfiguration)
        navView.setupWithNavController(navController)

        // Initialize services
        initializeServices()
    }

    private fun initializeServices() {
        // TODO: Initialize authentication, analytics, etc.
    }

    override fun onStart() {
        super.onStart()
        // Check if user is signed in
    }
}`

    return await this.generateCodeWithFallback(prompt, fallbackCode)
  }

  private async generateBuildGradle(request: AndroidGenerationRequest): Promise<string> {
    const prompt = `Generate a complete build.gradle (Module: app) file for an Android app called "${request.name}".

App features: ${request.features.join(', ')}
Architecture components needed: ${request.architecture.components.join(', ')}

Include dependencies for:
- Modern Android development (Kotlin, ViewBinding, etc.)
- Navigation components
- Network requests (Retrofit, OkHttp)
- Database (Room)
- Image loading (Glide/Picasso)
- Authentication
- Any other dependencies based on the features

Use the latest stable versions and follow Android best practices.`

    return await this.ollamaService.generateCode(prompt)
  }

  private async generateManifest(request: AndroidGenerationRequest): Promise<string> {
    const prompt = `Generate a complete AndroidManifest.xml file for "${request.name}".

Features: ${request.features.join(', ')}
Screens: ${request.architecture.screens.join(', ')}

Include:
- Proper permissions based on features
- All activities
- Application configuration
- Network security config if needed
- Any required services or receivers

Follow Android manifest best practices.`

    return await this.ollamaService.generateCode(prompt)
  }

  private async generateScreenActivity(screen: string, request: AndroidGenerationRequest): Promise<string> {
    const prompt = `Generate a complete ${screen}Activity.kt file for the "${screen}" screen in the Android app "${request.name}".

App context:
- Description: ${request.description}
- Features: ${request.features.join(', ')}
- UI Style: ${request.uiStyle || 'Modern'}

The ${screen} screen should:
1. Follow MVVM architecture pattern
2. Use ViewBinding
3. Handle user interactions appropriately
4. Include proper error handling
5. Follow Material Design guidelines

Generate clean, production-ready Kotlin code with comments.`

    return await this.ollamaService.generateCode(prompt)
  }

  private async generateScreenLayout(screen: string, request: AndroidGenerationRequest): Promise<string> {
    const prompt = `Generate a complete activity_${screen.toLowerCase()}.xml layout file for the "${screen}" screen.

App: ${request.name}
UI Style: ${request.uiStyle || 'Modern Material Design'}
Color Scheme: ${request.colorScheme || 'Blue'}

The layout should:
1. Follow Material Design guidelines
2. Be responsive and accessible
3. Include appropriate UI components for the screen purpose
4. Use proper constraints and styling
5. Support both portrait and landscape orientations

Generate clean XML with proper structure and styling.`

    return await this.ollamaService.generateCode(prompt)
  }

  private async generateComponent(component: string, request: AndroidGenerationRequest): Promise<string> {
    const prompt = `Generate a reusable ${component}Component.kt file for the Android app "${request.name}".

The component should:
1. Be reusable across different screens
2. Follow Android component best practices
3. Include proper styling and theming
4. Handle user interactions
5. Be accessible and responsive

App context: ${request.description}
UI Style: ${request.uiStyle || 'Modern'}

Generate clean Kotlin code with proper documentation.`

    return await this.ollamaService.generateCode(prompt)
  }

  private async generateDataModels(request: AndroidGenerationRequest): Promise<string> {
    const prompt = `Generate data models (data classes) for the Android app "${request.name}".

Based on:
- App description: ${request.description}
- Features: ${request.features.join(', ')}
- Database schema: ${request.architecture.database}

Create Kotlin data classes for:
1. User model
2. Main app entities based on features
3. API response models
4. Database entities with Room annotations

Include proper serialization annotations and validation.`

    return await this.ollamaService.generateCode(prompt)
  }

  private async generateApiService(request: AndroidGenerationRequest): Promise<string> {
    const prompt = `Generate an ApiService.kt file for the Android app "${request.name}".

API endpoints needed: ${request.architecture.apis.join(', ')}
Features: ${request.features.join(', ')}

Create:
1. Retrofit interface with all endpoints
2. API client setup
3. Request/response models
4. Error handling
5. Authentication headers if needed

Use modern Android networking practices with Retrofit and OkHttp.`

    return await this.ollamaService.generateCode(prompt)
  }

  private async generateDatabase(request: AndroidGenerationRequest): Promise<string> {
    const prompt = `Generate a DatabaseHelper.kt file using Room database for "${request.name}".

Database requirements: ${request.architecture.database}
Features: ${request.features.join(', ')}

Include:
1. Room database setup
2. Entity definitions
3. DAO interfaces
4. Database migrations
5. Repository pattern implementation

Follow Room database best practices with proper annotations.`

    return await this.ollamaService.generateCode(prompt)
  }

  private async generateStyles(request: AndroidGenerationRequest): Promise<string> {
    const prompt = `Generate a styles.xml file for the Android app "${request.name}".

UI Style: ${request.uiStyle || 'Modern Material Design'}
Color Scheme: ${request.colorScheme || 'Blue'}
Target Audience: ${request.targetAudience || 'General'}

Create:
1. App theme based on Material Design
2. Custom styles for components
3. Text styles and typography
4. Button styles
5. Card and container styles

Follow Material Design 3 guidelines.`

    return await this.ollamaService.generateCode(prompt)
  }

  private async generateColors(request: AndroidGenerationRequest): Promise<string> {
    const prompt = `Generate a colors.xml file for the Android app "${request.name}".

Color Scheme: ${request.colorScheme || 'Blue'}
UI Style: ${request.uiStyle || 'Modern'}

Create a complete color palette including:
1. Primary and secondary colors
2. Background colors
3. Text colors
4. Error and success colors
5. Surface and outline colors

Follow Material Design 3 color system with proper contrast ratios.`

    return await this.ollamaService.generateCode(prompt)
  }
}
