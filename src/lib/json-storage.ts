import fs from 'fs';
import path from 'path';

/**
 * A simple JSON-based storage system for the AndroidWeb Enterprise platform.
 * This is a real implementation that stores data in JSON files.
 */
export class JsonStorage {
  private dataDir: string;
  private collections: Map<string, any[]>;
  private initialized: boolean = false;

  constructor() {
    this.dataDir = path.join(process.cwd(), 'data');
    this.collections = new Map();
    this.ensureDataDirectory();
    this.initialize();
  }

  /**
   * Ensure the data directory exists
   */
  private ensureDataDirectory(): void {
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
  }

  /**
   * Initialize the storage by loading all existing collections
   */
  private initialize(): void {
    if (this.initialized) return;

    try {
      // Create default collections if they don't exist
      const defaultCollections = [
        'users',
        'ai_models',
        'generated_apps',
        'ai_learning_data',
        'app_generation_sessions',
        'ai_evolution_metrics',
        'user_feedback'
      ];

      for (const collection of defaultCollections) {
        const filePath = path.join(this.dataDir, `${collection}.json`);
        
        if (!fs.existsSync(filePath)) {
          fs.writeFileSync(filePath, JSON.stringify([], null, 2));
        }
        
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        this.collections.set(collection, data);
      }

      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize storage:', error);
      throw error;
    }
  }

  /**
   * Save a collection to disk
   */
  private saveCollection(collectionName: string): void {
    const filePath = path.join(this.dataDir, `${collectionName}.json`);
    const data = this.collections.get(collectionName) || [];
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  }

  /**
   * Get all items from a collection
   */
  getAll(collectionName: string): any[] {
    return [...(this.collections.get(collectionName) || [])];
  }

  /**
   * Find items in a collection by a filter function
   */
  find(collectionName: string, filterFn: (item: any) => boolean): any[] {
    const collection = this.collections.get(collectionName) || [];
    return collection.filter(filterFn);
  }

  /**
   * Find a single item in a collection by a filter function
   */
  findOne(collectionName: string, filterFn: (item: any) => boolean): any | null {
    const collection = this.collections.get(collectionName) || [];
    return collection.find(filterFn) || null;
  }

  /**
   * Find an item by ID
   */
  findById(collectionName: string, id: string): any | null {
    return this.findOne(collectionName, item => item.id === id);
  }

  /**
   * Insert an item into a collection
   */
  insert(collectionName: string, item: any): any {
    if (!item.id) {
      item.id = this.generateId();
    }
    
    if (!item.createdAt) {
      item.createdAt = new Date().toISOString();
    }
    
    if (!item.updatedAt) {
      item.updatedAt = new Date().toISOString();
    }
    
    const collection = this.collections.get(collectionName) || [];
    collection.push(item);
    this.collections.set(collectionName, collection);
    this.saveCollection(collectionName);
    
    return { ...item };
  }

  /**
   * Update an item in a collection
   */
  update(collectionName: string, id: string, updates: any): any | null {
    const collection = this.collections.get(collectionName) || [];
    const index = collection.findIndex(item => item.id === id);
    
    if (index === -1) {
      return null;
    }
    
    const updatedItem = {
      ...collection[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    collection[index] = updatedItem;
    this.collections.set(collectionName, collection);
    this.saveCollection(collectionName);
    
    return { ...updatedItem };
  }

  /**
   * Delete an item from a collection
   */
  delete(collectionName: string, id: string): boolean {
    const collection = this.collections.get(collectionName) || [];
    const index = collection.findIndex(item => item.id === id);
    
    if (index === -1) {
      return false;
    }
    
    collection.splice(index, 1);
    this.collections.set(collectionName, collection);
    this.saveCollection(collectionName);
    
    return true;
  }

  /**
   * Generate a unique ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  /**
   * Seed initial data if collections are empty
   */
  seedInitialData(): void {
    // Check if users collection is empty
    const users = this.getAll('users');
    if (users.length === 0) {
      // Add admin user
      this.insert('users', {
        email: '<EMAIL>',
        password: 'admin123', // In a real app, this would be hashed
        name: 'Admin User',
        role: 'ADMIN',
        plan: 'ENTERPRISE'
      });

      // Add demo user
      this.insert('users', {
        email: '<EMAIL>',
        password: 'password123', // In a real app, this would be hashed
        name: 'Demo User',
        role: 'USER',
        plan: 'FREE'
      });
    }

    // Check if AI models collection is empty
    const aiModels = this.getAll('ai_models');
    if (aiModels.length === 0) {
      // Add AI models
      this.insert('ai_models', {
        name: 'Qwen-2.5-Coder',
        type: 'code-generation',
        status: 'active',
        config: {
          model_path: 'qwen2.5-coder:latest',
          temperature: 0.7,
          max_tokens: 4096,
          specialization: 'mobile-app-development'
        },
        performance_score: 8.7,
        total_generations: 124,
        success_rate: 0.92
      });

      this.insert('ai_models', {
        name: 'Qwen-2.5-Chat',
        type: 'conversation',
        status: 'active',
        config: {
          model_path: 'qwen2.5:latest',
          temperature: 0.8,
          max_tokens: 2048,
          specialization: 'user-interaction'
        },
        performance_score: 9.1,
        total_generations: 256,
        success_rate: 0.95
      });

      this.insert('ai_models', {
        name: 'Llama-3.2-1B',
        type: 'fallback',
        status: 'active',
        config: {
          model_path: 'llama3.2:1b',
          temperature: 0.7,
          max_tokens: 1024,
          specialization: 'general'
        },
        performance_score: 6.5,
        total_generations: 42,
        success_rate: 0.85
      });
    }
  }
}
