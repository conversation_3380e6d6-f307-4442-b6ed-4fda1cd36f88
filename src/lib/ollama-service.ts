export interface AppArchitecture {
  screens: string[]
  navigation: string
  dataFlow: string
  components: string[]
  apis: string[]
  database: string
  authentication: string
  styling: string
}

export interface GenerationRequest {
  name: string
  description: string
  platform: string
  features: string[]
  uiStyle?: string
  colorScheme?: string
  targetAudience?: string
}

export class OllamaService {
  private baseUrl: string
  private model: string

  constructor() {
    this.baseUrl = process.env.OLLAMA_BASE_URL || 'http://localhost:11434'
    this.model = process.env.OLLAMA_MODEL || 'qwen2.5:7b'
  }

  async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`)
      if (!response.ok) return false

      const data = await response.json()

      // Check if any model is available
      if (!data.models || data.models.length === 0) {
        console.log('No models available in Ollama')
        return false
      }

      // Try to find preferred model first
      let availableModel = data.models.find((model: any) =>
        model.name.includes('qwen2.5') || model.name.includes('qwen')
      )

      // If preferred model not found, use any available model
      if (!availableModel) {
        availableModel = data.models[0]
        this.model = availableModel.name
        console.log(`Using available model: ${this.model}`)
      }

      return true
    } catch (error) {
      console.error('Ollama health check failed:', error)
      return false
    }
  }

  async generateAppArchitecture(request: GenerationRequest): Promise<AppArchitecture> {
    const prompt = `You are an expert mobile app architect. Create a detailed architecture for a ${request.platform} mobile application.

App Details:
- Name: ${request.name}
- Description: ${request.description}
- Platform: ${request.platform}
- Features: ${request.features.join(', ')}
- UI Style: ${request.uiStyle || 'Modern'}
- Color Scheme: ${request.colorScheme || 'Blue'}
- Target Audience: ${request.targetAudience || 'General'}

Generate a comprehensive architecture including:
1. Main screens and their purposes
2. Navigation structure
3. Data flow patterns
4. Required components
5. API endpoints needed
6. Database schema
7. Authentication strategy
8. Styling approach

Respond in JSON format with the following structure:
{
  "screens": ["screen1", "screen2", ...],
  "navigation": "navigation pattern description",
  "dataFlow": "data flow description",
  "components": ["component1", "component2", ...],
  "apis": ["api1", "api2", ...],
  "database": "database schema description",
  "authentication": "auth strategy description",
  "styling": "styling approach description"
}`

    try {
      const response = await this.callOllama(prompt)
      return this.parseArchitectureResponse(response)
    } catch (error) {
      console.error('Failed to generate architecture:', error)
      // Fallback architecture
      return this.getFallbackArchitecture(request)
    }
  }

  async generateCode(prompt: string): Promise<string> {
    try {
      return await this.callOllama(prompt)
    } catch (error) {
      console.error('Failed to generate code:', error)
      throw new Error('Code generation failed')
    }
  }

  private async callOllama(prompt: string): Promise<string> {
    const response = await fetch(`${this.baseUrl}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.model,
        prompt,
        stream: false,
        options: {
          temperature: 0.7,
          top_p: 0.9,
          max_tokens: 4000
        }
      }),
    })

    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.status}`)
    }

    const data = await response.json()
    return data.response
  }

  private parseArchitectureResponse(response: string): AppArchitecture {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        return {
          screens: Array.isArray(parsed.screens) ? parsed.screens : [],
          navigation: parsed.navigation || 'Tab-based navigation',
          dataFlow: parsed.dataFlow || 'Redux/Context pattern',
          components: Array.isArray(parsed.components) ? parsed.components : [],
          apis: Array.isArray(parsed.apis) ? parsed.apis : [],
          database: parsed.database || 'SQLite local storage',
          authentication: parsed.authentication || 'JWT token-based',
          styling: parsed.styling || 'Component-based styling'
        }
      }
    } catch (error) {
      console.error('Failed to parse architecture response:', error)
    }

    // Fallback parsing from text
    return this.parseArchitectureFromText(response)
  }

  private parseArchitectureFromText(response: string): AppArchitecture {
    return {
      screens: this.extractListFromText(response, 'screens?'),
      navigation: this.extractSectionFromText(response, 'navigation') || 'Tab-based navigation',
      dataFlow: this.extractSectionFromText(response, 'data flow') || 'State management pattern',
      components: this.extractListFromText(response, 'components?'),
      apis: this.extractListFromText(response, 'api|endpoint'),
      database: this.extractSectionFromText(response, 'database') || 'Local storage',
      authentication: this.extractSectionFromText(response, 'auth') || 'Token-based authentication',
      styling: this.extractSectionFromText(response, 'styl') || 'Modern component styling'
    }
  }

  private extractListFromText(text: string, keyword: string): string[] {
    const regex = new RegExp(`${keyword}[^\\n]*:?[^\\n]*\\n([\\s\\S]*?)(?=\\n\\n|\\n[A-Z]|$)`, 'i')
    const match = text.match(regex)
    if (match) {
      return match[1]
        .split('\n')
        .map(line => line.replace(/^[-*•]\s*/, '').trim())
        .filter(line => line.length > 0)
        .slice(0, 10) // Limit to 10 items
    }
    return []
  }

  private extractSectionFromText(text: string, keyword: string): string {
    const regex = new RegExp(`${keyword}[^\\n]*:?[^\\n]*\\n([^\\n]+)`, 'i')
    const match = text.match(regex)
    return match ? match[1].trim() : ''
  }

  getFallbackArchitecture(request: GenerationRequest): AppArchitecture {
    const baseScreens = ['Home', 'Profile', 'Settings']
    const featureScreens = request.features.map(feature => 
      feature.charAt(0).toUpperCase() + feature.slice(1).replace(/[^a-zA-Z]/g, '')
    )

    return {
      screens: [...baseScreens, ...featureScreens],
      navigation: request.platform === 'IOS' ? 'UINavigationController with TabBar' : 'Navigation Drawer with Bottom Navigation',
      dataFlow: 'MVVM with Repository pattern',
      components: ['Header', 'Button', 'Input', 'Card', 'List', 'Modal'],
      apis: ['/auth', '/user', '/data', '/upload'],
      database: request.platform === 'IOS' ? 'Core Data' : 'Room Database',
      authentication: 'OAuth 2.0 with JWT tokens',
      styling: request.platform === 'IOS' ? 'SwiftUI styling' : 'Material Design 3'
    }
  }
}
