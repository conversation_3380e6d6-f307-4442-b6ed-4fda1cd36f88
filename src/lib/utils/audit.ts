// Simplified audit system for demo
export type AuditAction =
  | 'USER_REGISTERED'
  | 'USER_LOGIN'
  | 'USER_LOGOUT'
  | 'PASSWORD_CHANGED'
  | 'EMAIL_VERIFIED'
  | 'SUBSCRIPTION_CREATED'
  | 'SUBSCRIPTION_UPDATED'
  | 'SUBSCRIPTION_CANCELLED'
  | 'PROJECT_CREATED'
  | 'PROJECT_UPDATED'
  | 'PROJECT_DELETED'
  | 'APP_GENERATED'
  | 'AI_INTERACTION'
  | 'ADMIN_ACTION'
  | 'SECURITY_EVENT'
  | 'REGISTRATION_FAILED'
  | 'LOGIN_FAILED'
  | 'REGISTRATION_ERROR'
  | 'SYSTEM_ERROR'

export type AuditCategory =
  | 'user'
  | 'subscription'
  | 'project'
  | 'ai'
  | 'admin'
  | 'security'
  | 'system'

export interface AuditLogData {
  [key: string]: any
}

// Simplified audit logging for demo
export async function createAuditLog(
  userId: string | null,
  action: AuditAction,
  category: AuditCategory,
  data: AuditLogData = {},
  ipAddress: string = 'unknown',
  userAgent: string = 'unknown'
): Promise<void> {
  try {
    // Log to console for demo purposes
    console.log('Audit Log:', {
      userId,
      action,
      category,
      data,
      ipAddress,
      userAgent,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Failed to create audit log:', {
      userId,
      action,
      category,
      data,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}

export async function getAuditLogs(
  filters: {
    userId?: string
    action?: AuditAction
    category?: AuditCategory
    startDate?: Date
    endDate?: Date
    limit?: number
    offset?: number
  } = {}
) {
  const {
    userId,
    action,
    category,
    startDate,
    endDate,
    limit = 50,
    offset = 0
  } = filters

  const where: any = {}

  if (userId) where.userId = userId
  if (action) where.action = action
  if (category) where.category = category
  if (startDate || endDate) {
    where.timestamp = {}
    if (startDate) where.timestamp.gte = startDate
    if (endDate) where.timestamp.lte = endDate
  }

  const [logs, total] = await Promise.all([
    db.auditLog.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        timestamp: 'desc',
      },
      take: limit,
      skip: offset,
    }),
    db.auditLog.count({ where }),
  ])

  return {
    logs,
    total,
    hasMore: offset + limit < total,
  }
}

export async function getSecurityEvents(
  userId?: string,
  limit: number = 20
) {
  const where: any = {
    category: 'security',
  }

  if (userId) {
    where.userId = userId
  }

  return db.auditLog.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
    orderBy: {
      timestamp: 'desc',
    },
    take: limit,
  })
}

export async function getUserActivity(
  userId: string,
  limit: number = 50
) {
  return db.auditLog.findMany({
    where: {
      userId,
      category: {
        in: ['user', 'project', 'ai'],
      },
    },
    orderBy: {
      timestamp: 'desc',
    },
    take: limit,
  })
}

export async function getSystemStats() {
  const now = new Date()
  const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000)
  const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

  const [
    totalLogs,
    last24HoursLogs,
    last7DaysLogs,
    userRegistrations24h,
    appGenerations24h,
    securityEvents24h,
  ] = await Promise.all([
    db.auditLog.count(),
    db.auditLog.count({
      where: {
        timestamp: {
          gte: last24Hours,
        },
      },
    }),
    db.auditLog.count({
      where: {
        timestamp: {
          gte: last7Days,
        },
      },
    }),
    db.auditLog.count({
      where: {
        action: 'USER_REGISTERED',
        timestamp: {
          gte: last24Hours,
        },
      },
    }),
    db.auditLog.count({
      where: {
        action: 'APP_GENERATED',
        timestamp: {
          gte: last24Hours,
        },
      },
    }),
    db.auditLog.count({
      where: {
        category: 'security',
        timestamp: {
          gte: last24Hours,
        },
      },
    }),
  ])

  return {
    totalLogs,
    last24HoursLogs,
    last7DaysLogs,
    userRegistrations24h,
    appGenerations24h,
    securityEvents24h,
  }
}

export async function cleanupOldLogs(daysToKeep: number = 90) {
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

  const result = await db.auditLog.deleteMany({
    where: {
      timestamp: {
        lt: cutoffDate,
      },
      category: {
        not: 'security', // Keep security logs longer
      },
    },
  })

  return result.count
}
