// Simplified audit system for demo
export type AuditAction =
  | 'USER_REGISTERED'
  | 'USER_LOGIN'
  | 'USER_LOGOUT'
  | 'PASSWORD_CHANGED'
  | 'EMAIL_VERIFIED'
  | 'SUBSCRIPTION_CREATED'
  | 'SUBSCRIPTION_UPDATED'
  | 'SUBSCRIPTION_CANCELLED'
  | 'PROJECT_CREATED'
  | 'PROJECT_UPDATED'
  | 'PROJECT_DELETED'
  | 'APP_GENERATED'
  | 'AI_INTERACTION'
  | 'ADMIN_ACTION'
  | 'SECURITY_EVENT'
  | 'REGISTRATION_FAILED'
  | 'LOGIN_FAILED'
  | 'REGISTRATION_ERROR'
  | 'SYSTEM_ERROR'

export type AuditCategory =
  | 'user'
  | 'subscription'
  | 'project'
  | 'ai'
  | 'admin'
  | 'security'
  | 'system'

export interface AuditLogData {
  [key: string]: any
}

// Simplified audit logging for demo
export async function createAuditLog(
  userId: string | null,
  action: AuditAction,
  category: AuditCategory,
  data: AuditLogData = {},
  ipAddress: string = 'unknown',
  userAgent: string = 'unknown'
): Promise<void> {
  try {
    // Log to console for demo purposes
    console.log('Audit Log:', {
      userId,
      action,
      category,
      data,
      ipAddress,
      userAgent,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Failed to create audit log:', {
      userId,
      action,
      category,
      data,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}

// Simplified demo functions
export async function getAuditLogs(filters: any = {}) {
  console.log('Getting audit logs with filters:', filters)
  return {
    logs: [],
    total: 0,
    hasMore: false,
  }
}

export async function getSecurityEvents(userId?: string, limit: number = 20) {
  console.log('Getting security events for user:', userId, 'limit:', limit)
  return []
}

export async function getUserActivity(userId: string, limit: number = 50) {
  console.log('Getting user activity for:', userId, 'limit:', limit)
  return []
}

export async function getSystemStats() {
  console.log('Getting system stats')
  return {
    totalLogs: 0,
    last24HoursLogs: 0,
    last7DaysLogs: 0,
    userRegistrations24h: 0,
    appGenerations24h: 0,
    securityEvents24h: 0,
  }
}

export async function cleanupOldLogs(daysToKeep: number = 90) {
  console.log('Cleaning up old logs, keeping:', daysToKeep, 'days')
  return 0
}
