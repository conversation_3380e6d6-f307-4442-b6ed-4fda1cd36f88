import { NextRequest, NextResponse } from 'next/server'

// Simplified middleware for now - will add full auth later
function extractTokenFromHeader(authHeader: string | null): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }
  return authHeader.substring(7)
}

function canAccessRoute(userRole: string, route: string): boolean {
  // Simplified route access check
  const adminRoutes = ['/admin', '/super-admin']
  if (adminRoutes.some(r => route.startsWith(r))) {
    return userRole === 'ADMIN' || userRole === 'SUPER_ADMIN'
  }
  return true
}

// Define route patterns
const AUTH_ROUTES = ['/login', '/register', '/forgot-password', '/reset-password']
const PUBLIC_ROUTES = ['/', '/pricing', '/features', '/contact', '/terms', '/privacy']
const API_PUBLIC_ROUTES = ['/api/auth/login', '/api/auth/register', '/api/auth/forgot-password', '/api/auth/reset-password']
const ADMIN_ROUTES = ['/admin']
const SUPER_ADMIN_ROUTES = ['/super-admin']

// Rate limiting configuration
const RATE_LIMIT_CONFIG = {
  '/api/auth/login': { requests: 5, window: 15 * 60 * 1000 }, // 5 requests per 15 minutes
  '/api/auth/register': { requests: 3, window: 60 * 60 * 1000 }, // 3 requests per hour
  '/api/auth/forgot-password': { requests: 3, window: 60 * 60 * 1000 }, // 3 requests per hour
  '/api/': { requests: 100, window: 60 * 1000 }, // 100 requests per minute for API
  default: { requests: 1000, window: 60 * 1000 }, // 1000 requests per minute for other routes
}

// In-memory rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.includes(pathname) || 
         pathname.startsWith('/api/public') ||
         API_PUBLIC_ROUTES.includes(pathname) ||
         pathname.startsWith('/_next') ||
         pathname.startsWith('/favicon') ||
         pathname.includes('.')
}

function isAuthRoute(pathname: string): boolean {
  return AUTH_ROUTES.some(route => pathname.startsWith(route))
}

function isProtectedRoute(pathname: string): boolean {
  return pathname.startsWith('/dashboard') || 
         pathname.startsWith('/workspace') ||
         pathname.startsWith('/profile') ||
         pathname.startsWith('/settings') ||
         pathname.startsWith('/api/protected') ||
         ADMIN_ROUTES.some(route => pathname.startsWith(route)) ||
         SUPER_ADMIN_ROUTES.some(route => pathname.startsWith(route))
}

function getRateLimitKey(request: NextRequest): string {
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
  const pathname = request.nextUrl.pathname
  return `${ip}:${pathname}`
}

function checkRateLimit(request: NextRequest): { allowed: boolean; remaining: number; resetTime: number } {
  const key = getRateLimitKey(request)
  const pathname = request.nextUrl.pathname
  
  // Find matching rate limit config
  let config = RATE_LIMIT_CONFIG.default
  for (const [pattern, patternConfig] of Object.entries(RATE_LIMIT_CONFIG)) {
    if (pattern !== 'default' && pathname.startsWith(pattern)) {
      config = patternConfig
      break
    }
  }
  
  const now = Date.now()
  const windowStart = now - config.window
  
  // Get or create rate limit entry
  let entry = rateLimitStore.get(key)
  if (!entry || entry.resetTime < windowStart) {
    entry = { count: 0, resetTime: now + config.window }
    rateLimitStore.set(key, entry)
  }
  
  // Check if limit exceeded
  if (entry.count >= config.requests) {
    return { allowed: false, remaining: 0, resetTime: entry.resetTime }
  }
  
  // Increment counter
  entry.count++
  rateLimitStore.set(key, entry)
  
  return { 
    allowed: true, 
    remaining: config.requests - entry.count, 
    resetTime: entry.resetTime 
  }
}

function addSecurityHeaders(response: NextResponse): NextResponse {
  // Security headers
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
  
  // HSTS (only in production)
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
  }
  
  return response
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Check rate limiting
  const rateLimit = checkRateLimit(request)
  if (!rateLimit.allowed) {
    const response = new NextResponse(
      JSON.stringify({ 
        error: 'Too many requests', 
        retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000) 
      }),
      { 
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': Math.ceil((rateLimit.resetTime - Date.now()) / 1000).toString(),
          'X-RateLimit-Limit': RATE_LIMIT_CONFIG.default.requests.toString(),
          'X-RateLimit-Remaining': rateLimit.remaining.toString(),
          'X-RateLimit-Reset': rateLimit.resetTime.toString(),
        }
      }
    )
    return addSecurityHeaders(response)
  }
  
  // Add rate limit headers to response
  const response = NextResponse.next()
  response.headers.set('X-RateLimit-Limit', RATE_LIMIT_CONFIG.default.requests.toString())
  response.headers.set('X-RateLimit-Remaining', rateLimit.remaining.toString())
  response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toString())
  
  // Skip middleware for public routes
  if (isPublicRoute(pathname)) {
    return addSecurityHeaders(response)
  }
  
  // Get token from header or cookie
  const authHeader = request.headers.get('authorization')
  const token = extractTokenFromHeader(authHeader) || request.cookies.get('auth-token')?.value
  
  // Verify token for protected routes
  if (isProtectedRoute(pathname)) {
    if (!token) {
      if (pathname.startsWith('/api/')) {
        return new NextResponse(
          JSON.stringify({ error: 'Authentication required' }),
          { status: 401, headers: { 'Content-Type': 'application/json' } }
        )
      }
      return NextResponse.redirect(new URL('/login', request.url))
    }
    
    // For now, skip token verification - will implement later
    const payload = { role: 'USER', userId: 'temp' }
    // const payload = verifyAccessToken(token)
    // if (!payload) {
    //   if (pathname.startsWith('/api/')) {
    //     return new NextResponse(
    //       JSON.stringify({ error: 'Invalid or expired token' }),
    //       { status: 401, headers: { 'Content-Type': 'application/json' } }
    //     )
    //   }
    //   return NextResponse.redirect(new URL('/login', request.url))
    // }
    
    // Check role-based access
    if (!canAccessRoute(payload.role, pathname)) {
      if (pathname.startsWith('/api/')) {
        return new NextResponse(
          JSON.stringify({ error: 'Insufficient permissions' }),
          { status: 403, headers: { 'Content-Type': 'application/json' } }
        )
      }
      return NextResponse.redirect(new URL('/unauthorized', request.url))
    }
    
    // Add user info to headers for API routes
    if (pathname.startsWith('/api/')) {
      response.headers.set('X-User-Id', payload.userId)
      response.headers.set('X-User-Role', payload.role)
      response.headers.set('X-User-Subscription', payload.subscriptionTier)
    }
  }
  
  // Redirect authenticated users away from auth routes
  if (isAuthRoute(pathname) && token) {
    // For now, skip token verification
    // const payload = verifyAccessToken(token)
    // if (payload) {
    //   return NextResponse.redirect(new URL('/dashboard', request.url))
    // }
  }
  
  return addSecurityHeaders(response)
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}
